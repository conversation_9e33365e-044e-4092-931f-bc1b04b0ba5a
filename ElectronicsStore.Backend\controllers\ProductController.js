const ProductService = require('../services/ProductService');

class ProductController {
  constructor() {
    this.productService = new ProductService();
  }

  // GET /api/products - Get all products
  async getAllProducts(req, res) {
    try {
      const filters = {
        category_id: req.query.category_id,
        search: req.query.search,
        page: req.query.page,
        limit: req.query.limit
      };

      const result = await this.productService.getAllProducts(filters);

      res.json({
        success: true,
        data: result.products,
        pagination: result.pagination
      });
    } catch (error) {
      console.error('Error in getAllProducts:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /api/products/:id - Get single product
  async getProductById(req, res) {
    try {
      const { id } = req.params;
      const product = await this.productService.getProductById(id);

      res.json({
        success: true,
        data: product
      });
    } catch (error) {
      console.error('Error in getProductById:', error);
      const statusCode = error.message.includes('غير موجود') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  // POST /api/products - Create new product
  async createProduct(req, res) {
    try {
      const productData = req.body;
      
      // Handle image upload if present
      if (req.file) {
        productData.image = `/uploads/products/${req.file.filename}`;
      }

      const product = await this.productService.createProduct(productData);

      res.status(201).json({
        success: true,
        message: 'تم إضافة المنتج بنجاح',
        data: product
      });
    } catch (error) {
      console.error('Error in createProduct:', error);
      const statusCode = error.message.includes('موجود مسبقاً') ? 400 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  // PUT /api/products/:id - Update product
  async updateProduct(req, res) {
    try {
      const { id } = req.params;
      const productData = req.body;
      
      // Handle image upload if present
      if (req.file) {
        productData.image = `/uploads/products/${req.file.filename}`;
      }

      const product = await this.productService.updateProduct(id, productData);

      res.json({
        success: true,
        message: 'تم تحديث المنتج بنجاح',
        data: product
      });
    } catch (error) {
      console.error('Error in updateProduct:', error);
      let statusCode = 500;
      if (error.message.includes('غير موجود')) statusCode = 404;
      if (error.message.includes('موجود مسبقاً')) statusCode = 400;
      
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  // DELETE /api/products/:id - Delete product
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;
      const result = await this.productService.deleteProduct(id);

      res.json({
        success: true,
        message: result.message
      });
    } catch (error) {
      console.error('Error in deleteProduct:', error);
      let statusCode = 500;
      if (error.message.includes('غير موجود')) statusCode = 404;
      if (error.message.includes('لا يمكن حذف')) statusCode = 400;
      
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /api/products/reports/low-stock - Get low stock products
  async getLowStockProducts(req, res) {
    try {
      const threshold = req.query.threshold || 10;
      const products = await this.productService.getLowStockProducts(threshold);

      res.json({
        success: true,
        data: products
      });
    } catch (error) {
      console.error('Error in getLowStockProducts:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /api/products/statistics - Get product statistics
  async getProductStatistics(req, res) {
    try {
      const stats = await this.productService.getProductStatistics();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error in getProductStatistics:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /api/products/search - Search products
  async searchProducts(req, res) {
    try {
      const { q: searchTerm, limit } = req.query;
      
      if (!searchTerm) {
        return res.status(400).json({
          success: false,
          message: 'مصطلح البحث مطلوب'
        });
      }

      const products = await this.productService.searchProducts(searchTerm, limit);

      res.json({
        success: true,
        data: products
      });
    } catch (error) {
      console.error('Error in searchProducts:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = ProductController;
