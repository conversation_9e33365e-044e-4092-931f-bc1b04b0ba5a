{"version": 3, "file": "nbcrow-token-parser.js", "names": ["_token", "require", "iconv", "_interopRequireWildcard", "_valueParser", "_helpers", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "nbc<PERSON>ow<PERSON><PERSON><PERSON>", "parser", "colMetadata", "columns", "bitmap", "bitmapByteLength", "Math", "ceil", "length", "buffer", "position", "waitForChunk", "bytes", "slice", "i", "len", "byte", "push", "metadata", "value", "isPLPStream", "chunks", "readPLPStream", "type", "name", "<PERSON><PERSON><PERSON>", "concat", "toString", "_metadata$collation", "decode", "collation", "codepage", "result", "readValue", "options", "err", "NotEnoughDataError", "offset", "useColumnNames", "columnsMap", "create", "for<PERSON>ach", "column", "colName", "NBCRowToken", "_default", "exports", "module"], "sources": ["../../src/token/nbcrow-token-parser.ts"], "sourcesContent": ["// s2.2.7.13 (introduced in TDS 7.3.B)\n\nimport Parser from './stream-parser';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\n\nimport { NBCRowToken } from './token';\nimport * as iconv from 'iconv-lite';\n\nimport { isPLPStream, readPLPStream, readValue } from '../value-parser';\nimport { NotEnoughDataError } from './helpers';\n\ninterface Column {\n  value: unknown;\n  metadata: ColumnMetadata;\n}\n\nasync function nbcRowParser(parser: Parser): Promise<NBCRowToken> {\n  const colMetadata = parser.colMetadata;\n  const columns: Column[] = [];\n  const bitmap: boolean[] = [];\n  const bitmapByteLength = Math.ceil(colMetadata.length / 8);\n\n  while (parser.buffer.length - parser.position < bitmapByteLength) {\n    await parser.waitForChunk();\n  }\n\n  const bytes = parser.buffer.slice(parser.position, parser.position + bitmapByteLength);\n  parser.position += bitmapByteLength;\n\n  for (let i = 0, len = bytes.length; i < len; i++) {\n    const byte = bytes[i];\n\n    bitmap.push(byte & 0b1 ? true : false);\n    bitmap.push(byte & 0b10 ? true : false);\n    bitmap.push(byte & 0b100 ? true : false);\n    bitmap.push(byte & 0b1000 ? true : false);\n    bitmap.push(byte & 0b10000 ? true : false);\n    bitmap.push(byte & 0b100000 ? true : false);\n    bitmap.push(byte & 0b1000000 ? true : false);\n    bitmap.push(byte & 0b10000000 ? true : false);\n  }\n\n  for (let i = 0; i < colMetadata.length; i++) {\n    const metadata = colMetadata[i];\n    if (bitmap[i]) {\n      columns.push({ value: null, metadata });\n      continue;\n    }\n\n    while (true) {\n      if (isPLPStream(metadata)) {\n        const chunks = await readPLPStream(parser);\n\n        if (chunks === null) {\n          columns.push({ value: chunks, metadata });\n        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {\n          columns.push({ value: Buffer.concat(chunks).toString('ucs2'), metadata });\n        } else if (metadata.type.name === 'VarChar') {\n          columns.push({ value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'), metadata });\n        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {\n          columns.push({ value: Buffer.concat(chunks), metadata });\n        }\n      } else {\n        let result;\n        try {\n          result = readValue(parser.buffer, parser.position, metadata, parser.options);\n        } catch (err) {\n          if (err instanceof NotEnoughDataError) {\n            await parser.waitForChunk();\n            continue;\n          }\n\n          throw err;\n        }\n\n        parser.position = result.offset;\n        columns.push({ value: result.value, metadata });\n      }\n\n      break;\n    }\n  }\n\n  if (parser.options.useColumnNames) {\n    const columnsMap: { [key: string]: Column } = Object.create(null);\n\n    columns.forEach((column) => {\n      const colName = column.metadata.colName;\n      if (columnsMap[colName] == null) {\n        columnsMap[colName] = column;\n      }\n    });\n\n    return new NBCRowToken(columnsMap);\n  } else {\n    return new NBCRowToken(columns);\n  }\n}\n\n\nexport default nbcRowParser;\nmodule.exports = nbcRowParser;\n"], "mappings": ";;;;;;AAKA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAA+C,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAT/C;;AAgBA,eAAeW,YAAYA,CAACC,MAAc,EAAwB;EAChE,MAAMC,WAAW,GAAGD,MAAM,CAACC,WAAW;EACtC,MAAMC,OAAiB,GAAG,EAAE;EAC5B,MAAMC,MAAiB,GAAG,EAAE;EAC5B,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,IAAI,CAACL,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC;EAE1D,OAAOP,MAAM,CAACQ,MAAM,CAACD,MAAM,GAAGP,MAAM,CAACS,QAAQ,GAAGL,gBAAgB,EAAE;IAChE,MAAMJ,MAAM,CAACU,YAAY,CAAC,CAAC;EAC7B;EAEA,MAAMC,KAAK,GAAGX,MAAM,CAACQ,MAAM,CAACI,KAAK,CAACZ,MAAM,CAACS,QAAQ,EAAET,MAAM,CAACS,QAAQ,GAAGL,gBAAgB,CAAC;EACtFJ,MAAM,CAACS,QAAQ,IAAIL,gBAAgB;EAEnC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,KAAK,CAACJ,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAChD,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;IAErBV,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC;IACtCZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;IACvCZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;IACxCZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;IACzCZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;IAC1CZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;IAC3CZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;IAC5CZ,MAAM,CAACa,IAAI,CAACD,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,KAAK,CAAC;EAC/C;EAEA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,WAAW,CAACM,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC3C,MAAMI,QAAQ,GAAGhB,WAAW,CAACY,CAAC,CAAC;IAC/B,IAAIV,MAAM,CAACU,CAAC,CAAC,EAAE;MACbX,OAAO,CAACc,IAAI,CAAC;QAAEE,KAAK,EAAE,IAAI;QAAED;MAAS,CAAC,CAAC;MACvC;IACF;IAEA,OAAO,IAAI,EAAE;MACX,IAAI,IAAAE,wBAAW,EAACF,QAAQ,CAAC,EAAE;QACzB,MAAMG,MAAM,GAAG,MAAM,IAAAC,0BAAa,EAACrB,MAAM,CAAC;QAE1C,IAAIoB,MAAM,KAAK,IAAI,EAAE;UACnBlB,OAAO,CAACc,IAAI,CAAC;YAAEE,KAAK,EAAEE,MAAM;YAAEH;UAAS,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,UAAU,IAAIN,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC5ErB,OAAO,CAACc,IAAI,CAAC;YAAEE,KAAK,EAAEM,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,MAAM,CAAC;YAAET;UAAS,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;UAAA,IAAAI,mBAAA;UAC3CzB,OAAO,CAACc,IAAI,CAAC;YAAEE,KAAK,EAAE7C,KAAK,CAACuD,MAAM,CAACJ,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,EAAE,EAAAO,mBAAA,GAAAV,QAAQ,CAACY,SAAS,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBG,QAAQ,KAAI,MAAM,CAAC;YAAEb;UAAS,CAAC,CAAC;QAChH,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,WAAW,IAAIN,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC7ErB,OAAO,CAACc,IAAI,CAAC;YAAEE,KAAK,EAAEM,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC;YAAEH;UAAS,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIc,MAAM;QACV,IAAI;UACFA,MAAM,GAAG,IAAAC,sBAAS,EAAChC,MAAM,CAACQ,MAAM,EAAER,MAAM,CAACS,QAAQ,EAAEQ,QAAQ,EAAEjB,MAAM,CAACiC,OAAO,CAAC;QAC9E,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;YACrC,MAAMnC,MAAM,CAACU,YAAY,CAAC,CAAC;YAC3B;UACF;UAEA,MAAMwB,GAAG;QACX;QAEAlC,MAAM,CAACS,QAAQ,GAAGsB,MAAM,CAACK,MAAM;QAC/BlC,OAAO,CAACc,IAAI,CAAC;UAAEE,KAAK,EAAEa,MAAM,CAACb,KAAK;UAAED;QAAS,CAAC,CAAC;MACjD;MAEA;IACF;EACF;EAEA,IAAIjB,MAAM,CAACiC,OAAO,CAACI,cAAc,EAAE;IACjC,MAAMC,UAAqC,GAAGhD,MAAM,CAACiD,MAAM,CAAC,IAAI,CAAC;IAEjErC,OAAO,CAACsC,OAAO,CAAEC,MAAM,IAAK;MAC1B,MAAMC,OAAO,GAAGD,MAAM,CAACxB,QAAQ,CAACyB,OAAO;MACvC,IAAIJ,UAAU,CAACI,OAAO,CAAC,IAAI,IAAI,EAAE;QAC/BJ,UAAU,CAACI,OAAO,CAAC,GAAGD,MAAM;MAC9B;IACF,CAAC,CAAC;IAEF,OAAO,IAAIE,kBAAW,CAACL,UAAU,CAAC;EACpC,CAAC,MAAM;IACL,OAAO,IAAIK,kBAAW,CAACzC,OAAO,CAAC;EACjC;AACF;AAAC,IAAA0C,QAAA,GAGc7C,YAAY;AAAA8C,OAAA,CAAA7D,OAAA,GAAA4D,QAAA;AAC3BE,MAAM,CAACD,OAAO,GAAG9C,YAAY"}