"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BulkLoadPayload = void 0;
class BulkLoadPayload {
  constructor(bulkLoad) {
    this.bulkLoad = bulkLoad;

    // We need to grab the iterator here so that `error` event handlers are set up
    // as early as possible (and are not potentially lost).
    this.iterator = this.bulkLoad.rowToPacketTransform[Symbol.asyncIterator]();
  }
  [Symbol.asyncIterator]() {
    return this.iterator;
  }
  toString(indent = '') {
    return indent + 'BulkLoad';
  }
}
exports.BulkLoadPayload = BulkLoadPayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJCdWxrTG9hZFBheWxvYWQiLCJjb25zdHJ1Y3RvciIsImJ1bGtMb2FkIiwiaXRlcmF0b3IiLCJyb3dUb1BhY2tldFRyYW5zZm9ybSIsIlN5bWJvbCIsImFzeW5jSXRlcmF0b3IiLCJ0b1N0cmluZyIsImluZGVudCIsImV4cG9ydHMiXSwic291cmNlcyI6WyIuLi9zcmMvYnVsay1sb2FkLXBheWxvYWQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJ1bGtMb2FkIGZyb20gJy4vYnVsay1sb2FkJztcblxuZXhwb3J0IGNsYXNzIEJ1bGtMb2FkUGF5bG9hZCBpbXBsZW1lbnRzIEFzeW5jSXRlcmFibGU8QnVmZmVyPiB7XG4gIGRlY2xhcmUgYnVsa0xvYWQ6IEJ1bGtMb2FkO1xuICBkZWNsYXJlIGl0ZXJhdG9yOiBBc3luY0l0ZXJhYmxlSXRlcmF0b3I8QnVmZmVyPjtcblxuICBjb25zdHJ1Y3RvcihidWxrTG9hZDogQnVsa0xvYWQpIHtcbiAgICB0aGlzLmJ1bGtMb2FkID0gYnVsa0xvYWQ7XG5cbiAgICAvLyBXZSBuZWVkIHRvIGdyYWIgdGhlIGl0ZXJhdG9yIGhlcmUgc28gdGhhdCBgZXJyb3JgIGV2ZW50IGhhbmRsZXJzIGFyZSBzZXQgdXBcbiAgICAvLyBhcyBlYXJseSBhcyBwb3NzaWJsZSAoYW5kIGFyZSBub3QgcG90ZW50aWFsbHkgbG9zdCkuXG4gICAgdGhpcy5pdGVyYXRvciA9IHRoaXMuYnVsa0xvYWQucm93VG9QYWNrZXRUcmFuc2Zvcm1bU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCk7XG4gIH1cblxuICBbU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCkge1xuICAgIHJldHVybiB0aGlzLml0ZXJhdG9yO1xuICB9XG5cbiAgdG9TdHJpbmcoaW5kZW50ID0gJycpIHtcbiAgICByZXR1cm4gaW5kZW50ICsgKCdCdWxrTG9hZCcpO1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUVPLE1BQU1BLGVBQWUsQ0FBa0M7RUFJNURDLFdBQVdBLENBQUNDLFFBQWtCLEVBQUU7SUFDOUIsSUFBSSxDQUFDQSxRQUFRLEdBQUdBLFFBQVE7O0lBRXhCO0lBQ0E7SUFDQSxJQUFJLENBQUNDLFFBQVEsR0FBRyxJQUFJLENBQUNELFFBQVEsQ0FBQ0Usb0JBQW9CLENBQUNDLE1BQU0sQ0FBQ0MsYUFBYSxDQUFDLENBQUMsQ0FBQztFQUM1RTtFQUVBLENBQUNELE1BQU0sQ0FBQ0MsYUFBYSxJQUFJO0lBQ3ZCLE9BQU8sSUFBSSxDQUFDSCxRQUFRO0VBQ3RCO0VBRUFJLFFBQVFBLENBQUNDLE1BQU0sR0FBRyxFQUFFLEVBQUU7SUFDcEIsT0FBT0EsTUFBTSxHQUFJLFVBQVc7RUFDOUI7QUFDRjtBQUFDQyxPQUFBLENBQUFULGVBQUEsR0FBQUEsZUFBQSJ9