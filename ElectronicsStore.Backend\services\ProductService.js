const ProductRepository = require('../repositories/ProductRepository');
const CategoryRepository = require('../repositories/CategoryRepository');
const SupplierRepository = require('../repositories/SupplierRepository');

class ProductService {
  constructor() {
    this.productRepository = new ProductRepository();
    this.categoryRepository = new CategoryRepository();
    this.supplierRepository = new SupplierRepository();
  }

  // Get all products with filters and pagination
  async getAllProducts(filters = {}) {
    try {
      const { category_id, search, page = 1, limit = 50 } = filters;
      
      // Validate pagination parameters
      const validatedPage = Math.max(1, parseInt(page));
      const validatedLimit = Math.min(100, Math.max(1, parseInt(limit)));
      
      // Get products from repository
      const products = await this.productRepository.findAll({
        category_id,
        search,
        page: validatedPage,
        limit: validatedLimit
      });
      
      // Get total count for pagination
      const totalCount = await this.productRepository.count({
        category_id,
        search
      });
      
      return {
        products: products,
        pagination: {
          page: validatedPage,
          limit: validatedLimit,
          total: totalCount,
          pages: Math.ceil(totalCount / validatedLimit)
        }
      };
    } catch (error) {
      throw new Error(`خطأ في جلب المنتجات: ${error.message}`);
    }
  }

  // Get single product by ID
  async getProductById(id) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('معرف المنتج غير صحيح');
      }

      const product = await this.productRepository.findById(parseInt(id));
      
      if (!product) {
        throw new Error('المنتج غير موجود');
      }

      return product;
    } catch (error) {
      throw new Error(`خطأ في جلب المنتج: ${error.message}`);
    }
  }

  // Create new product
  async createProduct(productData) {
    try {
      // Validate required fields
      this.validateProductData(productData);
      
      // Check if barcode already exists
      if (productData.barcode) {
        const existingProduct = await this.productRepository.findByBarcode(productData.barcode);
        if (existingProduct) {
          throw new Error('الباركود موجود مسبقاً');
        }
      }
      
      // Validate category exists
      if (productData.category_id) {
        const category = await this.categoryRepository.findById(productData.category_id);
        if (!category) {
          throw new Error('الفئة المحددة غير موجودة');
        }
      }
      
      // Validate supplier exists (if provided)
      if (productData.supplier_id) {
        const supplier = await this.supplierRepository.findById(productData.supplier_id);
        if (!supplier) {
          throw new Error('المورد المحدد غير موجود');
        }
      }
      
      // Validate pricing logic
      this.validatePricing(productData);
      
      // Create product
      const newProduct = await this.productRepository.create(productData);
      
      // Return complete product data
      return await this.getProductById(newProduct.id);
    } catch (error) {
      throw new Error(`خطأ في إضافة المنتج: ${error.message}`);
    }
  }

  // Update existing product
  async updateProduct(id, productData) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('معرف المنتج غير صحيح');
      }

      // Check if product exists
      const existingProduct = await this.productRepository.findById(parseInt(id));
      if (!existingProduct) {
        throw new Error('المنتج غير موجود');
      }

      // Validate product data
      this.validateProductData(productData);
      
      // Check if barcode is unique (excluding current product)
      if (productData.barcode && productData.barcode !== existingProduct.barcode) {
        const duplicateProduct = await this.productRepository.findByBarcode(productData.barcode);
        if (duplicateProduct && duplicateProduct.id !== parseInt(id)) {
          throw new Error('الباركود موجود مسبقاً');
        }
      }
      
      // Validate category exists
      if (productData.category_id) {
        const category = await this.categoryRepository.findById(productData.category_id);
        if (!category) {
          throw new Error('الفئة المحددة غير موجودة');
        }
      }
      
      // Validate supplier exists (if provided)
      if (productData.supplier_id) {
        const supplier = await this.supplierRepository.findById(productData.supplier_id);
        if (!supplier) {
          throw new Error('المورد المحدد غير موجود');
        }
      }
      
      // Validate pricing logic
      this.validatePricing(productData);
      
      // Update product
      await this.productRepository.update(parseInt(id), productData);
      
      // Return updated product data
      return await this.getProductById(parseInt(id));
    } catch (error) {
      throw new Error(`خطأ في تحديث المنتج: ${error.message}`);
    }
  }

  // Delete product
  async deleteProduct(id) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('معرف المنتج غير صحيح');
      }

      // Check if product exists
      const product = await this.productRepository.findById(parseInt(id));
      if (!product) {
        throw new Error('المنتج غير موجود');
      }

      // Check if product has transactions
      const hasTransactions = await this.productRepository.hasTransactions(parseInt(id));
      if (hasTransactions) {
        throw new Error('لا يمكن حذف المنتج لوجود معاملات مرتبطة به');
      }

      // Delete product
      await this.productRepository.delete(parseInt(id));
      
      return { message: 'تم حذف المنتج بنجاح' };
    } catch (error) {
      throw new Error(`خطأ في حذف المنتج: ${error.message}`);
    }
  }

  // Get low stock products
  async getLowStockProducts(threshold = 10) {
    try {
      const products = await this.productRepository.findLowStock(threshold);
      return products;
    } catch (error) {
      throw new Error(`خطأ في جلب المنتجات منخفضة المخزون: ${error.message}`);
    }
  }

  // Validate product data
  validateProductData(productData) {
    const errors = [];

    if (!productData.name || !productData.name.trim()) {
      errors.push('اسم المنتج مطلوب');
    }

    if (!productData.category_id || productData.category_id === 0) {
      errors.push('الفئة مطلوبة');
    }

    if (!productData.default_cost_price || productData.default_cost_price <= 0) {
      errors.push('التكلفة يجب أن تكون أكبر من صفر');
    }

    if (!productData.default_selling_price || productData.default_selling_price <= 0) {
      errors.push('سعر البيع يجب أن يكون أكبر من صفر');
    }

    if (!productData.min_selling_price || productData.min_selling_price <= 0) {
      errors.push('الحد الأدنى للسعر يجب أن يكون أكبر من صفر');
    }

    if (!productData.barcode || !productData.barcode.trim()) {
      errors.push('الباركود مطلوب');
    }

    if (errors.length > 0) {
      throw new Error(errors.join(', '));
    }
  }

  // Validate pricing logic
  validatePricing(productData) {
    const { default_cost_price, default_selling_price, min_selling_price } = productData;

    if (default_cost_price >= default_selling_price) {
      throw new Error('التكلفة يجب أن تكون أقل من سعر البيع');
    }

    if (min_selling_price > default_selling_price) {
      throw new Error('الحد الأدنى للسعر يجب أن يكون أقل من أو يساوي سعر البيع');
    }

    if (min_selling_price <= default_cost_price) {
      throw new Error('الحد الأدنى للسعر يجب أن يكون أكبر من التكلفة');
    }
  }

  // Calculate profit margin
  calculateProfitMargin(costPrice, sellingPrice) {
    if (sellingPrice === 0) return 0;
    return ((sellingPrice - costPrice) / sellingPrice * 100);
  }

  // Get product statistics
  async getProductStatistics() {
    try {
      const stats = await this.productRepository.getStatistics();
      return stats;
    } catch (error) {
      throw new Error(`خطأ في جلب إحصائيات المنتجات: ${error.message}`);
    }
  }
}

module.exports = ProductService;
