const express = require('express');
const router = express.Router();
const { executeQuery, sql } = require('../config/database-sqlserver');

// GET /api/sales - Get all sales invoices
router.get('/', async (req, res) => {
  try {
    const { payment_method, date_from, date_to, page = 1, limit = 50 } = req.query;
    
    let query = `
      SELECT 
        si.id,
        si.invoice_number,
        si.customer_name,
        si.invoice_date,
        si.discount_total,
        si.total_amount,
        si.payment_method,
        u.username as user_name,
        ou.username as override_user_name
      FROM sales_invoices si
      LEFT JOIN users u ON si.user_id = u.id
      LEFT JOIN users ou ON si.override_by_user_id = ou.id
      WHERE 1=1
    `;
    
    const params = {};
    
    if (payment_method && payment_method !== 'all') {
      query += ' AND si.payment_method = @payment_method';
      params.payment_method = payment_method;
    }
    
    if (date_from) {
      query += ' AND CAST(si.invoice_date AS DATE) >= @date_from';
      params.date_from = date_from;
    }
    
    if (date_to) {
      query += ' AND CAST(si.invoice_date AS DATE) <= @date_to';
      params.date_to = date_to;
    }
    
    query += ' ORDER BY si.invoice_date DESC';
    
    // Add pagination
    const offset = (page - 1) * limit;
    query += ` OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY`;
    params.offset = offset;
    params.limit = parseInt(limit);
    
    const result = await executeQuery(query, params);
    
    // Get total count for pagination
    let countQuery = `SELECT COUNT(*) as total FROM sales_invoices si WHERE 1=1`;
    const countParams = {};
    
    if (payment_method && payment_method !== 'all') {
      countQuery += ' AND si.payment_method = @payment_method';
      countParams.payment_method = payment_method;
    }
    
    if (date_from) {
      countQuery += ' AND CAST(si.invoice_date AS DATE) >= @date_from';
      countParams.date_from = date_from;
    }
    
    if (date_to) {
      countQuery += ' AND CAST(si.invoice_date AS DATE) <= @date_to';
      countParams.date_to = date_to;
    }
    
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult.recordset[0].total;
    
    res.json({
      success: true,
      data: result.recordset,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching sales:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المبيعات'
    });
  }
});

// GET /api/sales/:id - Get single sale with details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get sale header
    const saleQuery = `
      SELECT 
        si.id,
        si.invoice_number,
        si.customer_name,
        si.invoice_date,
        si.discount_total,
        si.total_amount,
        si.payment_method,
        u.username as user_name,
        ou.username as override_user_name,
        si.override_date
      FROM sales_invoices si
      LEFT JOIN users u ON si.user_id = u.id
      LEFT JOIN users ou ON si.override_by_user_id = ou.id
      WHERE si.id = @id
    `;
    
    const saleResult = await executeQuery(saleQuery, { id: parseInt(id) });
    
    if (saleResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الفاتورة غير موجودة'
      });
    }
    
    // Get sale details
    const detailsQuery = `
      SELECT 
        sid.id,
        sid.product_id,
        p.name as product_name,
        p.barcode,
        sid.quantity,
        sid.unit_price,
        sid.discount_amount,
        sid.line_total
      FROM sales_invoice_details sid
      JOIN products p ON sid.product_id = p.id
      WHERE sid.sales_invoice_id = @id
      ORDER BY sid.id
    `;
    
    const detailsResult = await executeQuery(detailsQuery, { id: parseInt(id) });
    
    const sale = saleResult.recordset[0];
    sale.items = detailsResult.recordset;
    
    res.json({
      success: true,
      data: sale
    });
  } catch (error) {
    console.error('Error fetching sale:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفاتورة'
    });
  }
});

// POST /api/sales - Create new sale
router.post('/', async (req, res) => {
  const transaction = new sql.Transaction();
  
  try {
    const {
      customer_name,
      items,
      discount_total = 0,
      total_amount,
      payment_method,
      user_id,
      override_by_user_id = null
    } = req.body;
    
    // Validation
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد منتجات في الفاتورة'
      });
    }
    
    if (!total_amount || !payment_method || !user_id) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة مفقودة'
      });
    }
    
    // Start transaction
    await transaction.begin();
    
    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}`;
    
    // Create sales invoice
    const invoiceQuery = `
      INSERT INTO sales_invoices (
        invoice_number, customer_name, discount_total, 
        total_amount, payment_method, user_id, override_by_user_id
      )
      OUTPUT INSERTED.id
      VALUES (
        @invoice_number, @customer_name, @discount_total,
        @total_amount, @payment_method, @user_id, @override_by_user_id
      )
    `;
    
    const invoiceRequest = new sql.Request(transaction);
    invoiceRequest.input('invoice_number', invoiceNumber);
    invoiceRequest.input('customer_name', customer_name || null);
    invoiceRequest.input('discount_total', parseFloat(discount_total));
    invoiceRequest.input('total_amount', parseFloat(total_amount));
    invoiceRequest.input('payment_method', payment_method);
    invoiceRequest.input('user_id', parseInt(user_id));
    invoiceRequest.input('override_by_user_id', override_by_user_id ? parseInt(override_by_user_id) : null);
    
    const invoiceResult = await invoiceRequest.query(invoiceQuery);
    const invoiceId = invoiceResult.recordset[0].id;
    
    // Create invoice details and inventory logs
    for (const item of items) {
      // Insert invoice detail
      const detailQuery = `
        INSERT INTO sales_invoice_details (
          sales_invoice_id, product_id, quantity, unit_price, discount_amount
        )
        VALUES (@sales_invoice_id, @product_id, @quantity, @unit_price, @discount_amount)
      `;
      
      const detailRequest = new sql.Request(transaction);
      detailRequest.input('sales_invoice_id', invoiceId);
      detailRequest.input('product_id', parseInt(item.product_id));
      detailRequest.input('quantity', parseInt(item.quantity));
      detailRequest.input('unit_price', parseFloat(item.unit_price));
      detailRequest.input('discount_amount', parseFloat(item.discount_amount || 0));
      
      await detailRequest.query(detailQuery);
      
      // Get product cost for inventory log
      const costQuery = `SELECT default_cost_price FROM products WHERE id = @product_id`;
      const costRequest = new sql.Request(transaction);
      costRequest.input('product_id', parseInt(item.product_id));
      const costResult = await costRequest.query(costQuery);
      const unitCost = costResult.recordset[0].default_cost_price;
      
      // Create inventory log (negative quantity for sale)
      const logQuery = `
        INSERT INTO inventory_logs (
          product_id, movement_type, quantity, unit_cost,
          reference_tbl, reference_id, user_id
        )
        VALUES (
          @product_id, 'sale', @quantity, @unit_cost,
          'sales_invoice', @reference_id, @user_id
        )
      `;
      
      const logRequest = new sql.Request(transaction);
      logRequest.input('product_id', parseInt(item.product_id));
      logRequest.input('quantity', -parseInt(item.quantity)); // Negative for sale
      logRequest.input('unit_cost', parseFloat(unitCost));
      logRequest.input('reference_id', invoiceId);
      logRequest.input('user_id', parseInt(user_id));
      
      await logRequest.query(logQuery);
    }
    
    // Commit transaction
    await transaction.commit();
    
    // Get the complete sale data
    const completeSaleQuery = `
      SELECT 
        si.id,
        si.invoice_number,
        si.customer_name,
        si.invoice_date,
        si.discount_total,
        si.total_amount,
        si.payment_method,
        u.username as user_name
      FROM sales_invoices si
      LEFT JOIN users u ON si.user_id = u.id
      WHERE si.id = @id
    `;
    
    const completeSaleResult = await executeQuery(completeSaleQuery, { id: invoiceId });
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء الفاتورة بنجاح',
      data: completeSaleResult.recordset[0]
    });
  } catch (error) {
    // Rollback transaction on error
    if (transaction._aborted === false) {
      await transaction.rollback();
    }
    console.error('Error creating sale:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الفاتورة'
    });
  }
});

// GET /api/sales/reports/daily - Get daily sales report
router.get('/reports/daily', async (req, res) => {
  try {
    const { date = new Date().toISOString().split('T')[0] } = req.query;
    
    const dailyStatsQuery = `
      SELECT 
        COUNT(*) as total_sales,
        ISNULL(SUM(total_amount), 0) as total_revenue,
        ISNULL(SUM(discount_total), 0) as total_discount,
        ISNULL(AVG(total_amount), 0) as avg_sale_amount,
        COUNT(CASE WHEN payment_method = 'cash' THEN 1 END) as cash_sales,
        COUNT(CASE WHEN payment_method = 'card' THEN 1 END) as card_sales,
        COUNT(CASE WHEN payment_method = 'deferred' THEN 1 END) as deferred_sales
      FROM sales_invoices 
      WHERE CAST(invoice_date AS DATE) = @date
    `;
    
    const dailyStatsResult = await executeQuery(dailyStatsQuery, { date });
    
    const topProductsQuery = `
      SELECT TOP 10
        p.name as product_name,
        SUM(sid.quantity) as total_quantity,
        SUM(sid.line_total) as total_revenue
      FROM sales_invoice_details sid
      JOIN products p ON sid.product_id = p.id
      JOIN sales_invoices si ON sid.sales_invoice_id = si.id
      WHERE CAST(si.invoice_date AS DATE) = @date
      GROUP BY p.id, p.name
      ORDER BY total_quantity DESC
    `;
    
    const topProductsResult = await executeQuery(topProductsQuery, { date });
    
    res.json({
      success: true,
      data: {
        date,
        stats: dailyStatsResult.recordset[0],
        topProducts: topProductsResult.recordset
      }
    });
  } catch (error) {
    console.error('Error fetching daily sales report:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات اليومي'
    });
  }
});

// GET /api/sales/reports/inventory-low - Get low stock products
router.get('/reports/inventory-low', async (req, res) => {
  try {
    const query = `
      SELECT 
        p.id,
        p.name,
        p.barcode,
        c.name as category_name,
        ISNULL(iv.current_quantity, 0) as current_quantity
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      WHERE ISNULL(iv.current_quantity, 0) <= 10
      ORDER BY ISNULL(iv.current_quantity, 0) ASC
    `;
    
    const result = await executeQuery(query);
    
    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتجات منخفضة المخزون'
    });
  }
});

module.exports = router;
