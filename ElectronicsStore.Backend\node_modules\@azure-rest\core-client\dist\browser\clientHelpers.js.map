{"version": 3, "file": "clientHelpers.js", "sourceRoot": "", "sources": ["../../src/clientHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,+BAA+B,EAC/B,uBAAuB,EACvB,yBAAyB,GAC1B,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAGrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,iCAAiC,EAAE,MAAM,wCAAwC,CAAC;AAE3F,IAAI,gBAAwC,CAAC;AAgB7C;;GAEG;AACH,MAAM,UAAU,2BAA2B,CACzC,QAAkB,EAClB,QAAgB,EAChB,UAA8C,EAAE;;IAEhD,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;IAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO;IACT,CAAC;IAED,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,+BAA+B,CAAC;YAClD,UAAU;YACV,MAAM,EAAE,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,WAAW,0CAAE,MAAM,mCAAI,GAAG,QAAQ,WAAW;SACrE,CAAC,CAAC;QACH,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;SAAM,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;QACvC,IAAI,CAAC,CAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,WAAW,0CAAE,gBAAgB,CAAA,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QACD,MAAM,SAAS,GAAG,iCAAiC,CACjD,UAAU,EACV,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,WAAW,0CAAE,gBAAgB,CAC7C,CAAC;QACF,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CACnC,QAAgB,EAChB,UAA4C,EAC5C,UAAyB,EAAE;IAE3B,MAAM,QAAQ,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAEpD,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9C,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;IACxF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,eAAe,CAAC,UAAe;IACtC,OAAQ,UAA4B,CAAC,GAAG,KAAK,SAAS,CAAC;AACzD,CAAC;AAED,MAAM,UAAU,2BAA2B;IACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,uBAAuB,EAAE,CAAC;IAC/C,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, Pipeline } from \"@azure/core-rest-pipeline\";\nimport {\n  bearerTokenAuthenticationPolicy,\n  createDefaultHttpClient,\n  createPipelineFromOptions,\n} from \"@azure/core-rest-pipeline\";\nimport type { KeyCredential, TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\n\nimport type { ClientOptions } from \"./common.js\";\nimport { apiVersionPolicy } from \"./apiVersionPolicy.js\";\nimport { keyCredentialAuthenticationPolicy } from \"./keyCredentialAuthenticationPolicy.js\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\n/**\n * Optional parameters for adding a credential policy to the pipeline.\n */\nexport interface AddCredentialPipelinePolicyOptions {\n  /**\n   * Options related to the client.\n   */\n  clientOptions?: ClientOptions;\n  /**\n   * The credential to use.\n   */\n  credential?: TokenCredential | KeyCredential;\n}\n\n/**\n * Adds a credential policy to the pipeline if a credential is provided. If none is provided, no policy is added.\n */\nexport function addCredentialPipelinePolicy(\n  pipeline: Pipeline,\n  endpoint: string,\n  options: AddCredentialPipelinePolicyOptions = {},\n): void {\n  const { credential, clientOptions } = options;\n  if (!credential) {\n    return;\n  }\n\n  if (isTokenCredential(credential)) {\n    const tokenPolicy = bearerTokenAuthenticationPolicy({\n      credential,\n      scopes: clientOptions?.credentials?.scopes ?? `${endpoint}/.default`,\n    });\n    pipeline.addPolicy(tokenPolicy);\n  } else if (isKeyCredential(credential)) {\n    if (!clientOptions?.credentials?.apiKeyHeaderName) {\n      throw new Error(`Missing API Key Header Name`);\n    }\n    const keyPolicy = keyCredentialAuthenticationPolicy(\n      credential,\n      clientOptions?.credentials?.apiKeyHeaderName,\n    );\n    pipeline.addPolicy(keyPolicy);\n  }\n}\n\n/**\n * Creates a default rest pipeline to re-use accross Rest Level Clients\n */\nexport function createDefaultPipeline(\n  endpoint: string,\n  credential?: TokenCredential | KeyCredential,\n  options: ClientOptions = {},\n): Pipeline {\n  const pipeline = createPipelineFromOptions(options);\n\n  pipeline.addPolicy(apiVersionPolicy(options));\n\n  addCredentialPipelinePolicy(pipeline, endpoint, { credential, clientOptions: options });\n  return pipeline;\n}\n\nfunction isKeyCredential(credential: any): credential is KeyCredential {\n  return (credential as KeyCredential).key !== undefined;\n}\n\nexport function getCachedDefaultHttpsClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"]}