"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "m16 14.2-.8.8h-4.6v-4.6l3.68-3.68C13.64 6.26 12.85 6 12 6c-2.21 0-4 1.79-4 4v7h8z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "m17.58 6.25 1.77 1.77L14.37 13H12.6v-1.77zm3.27-.44-1.06-1.06c-.2-.2-.51-.2-.71 0l-.85.85L20 7.37l.85-.85c.2-.2.2-.52 0-.71M18 12.2V17h2v2H4v-2h2v-7c0-2.79 1.91-5.14 4.5-5.8v-.7c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v.7c.82.21 1.57.59 2.21 1.09l-1.43 1.43C13.64 6.26 12.85 6 12 6c-2.21 0-4 1.79-4 4v7h8v-2.8zM10 20h4c0 1.1-.9 2-2 2s-2-.9-2-2"
}, "1")], 'EditNotificationsTwoTone');