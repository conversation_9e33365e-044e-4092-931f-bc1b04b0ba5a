{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC;;;;GAIG;AACH,yDAK6B;AAJ3B,0HAAA,qBAAqB,OAAA;AAMvB,0FAOkD;AANhD,+IAAA,8BAA8B,OAAA;AAC9B,iJAAA,gCAAgC,OAAA;AAIhC,qIAAA,oBAAoB,OAAA;AAItB,kFAAkF;AAAzE,uIAAA,0BAA0B,OAAA;AACnC,+DAA2D;AAAlD,yHAAA,iBAAiB,OAAA;AAC1B,qCAQmB;AADjB,4GAAA,iBAAiB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A Shim Library that provides compatibility between Core V1 & V2 Packages.\n *\n * @packageDocumentation\n */\nexport {\n  ExtendedServiceClient,\n  ExtendedServiceClientOptions,\n  ExtendedCommonClientOptions,\n  ExtendedClientOptions,\n} from \"./extendedClient.js\";\nexport { CompatResponse } from \"./response.js\";\nexport {\n  requestPolicyFactoryPolicyName,\n  createRequestPolicyFactoryPolicy,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptionsLike,\n  HttpPipelineLogLevel,\n} from \"./policies/requestPolicyFactoryPolicy.js\";\nexport { KeepAliveOptions } from \"./policies/keepAliveOptions.js\";\nexport { RedirectOptions } from \"./policies/redirectOptions.js\";\nexport { disableKeepAlivePolicyName } from \"./policies/disableKeepAlivePolicy.js\";\nexport { convertHttpClient } from \"./httpClientAdapter.js\";\nexport {\n  Agent,\n  WebResourceLike,\n  HttpHeadersLike,\n  RawHttpHeaders,\n  HttpHeader,\n  TransferProgressEvent,\n  toHttpHeadersLike,\n} from \"./util.js\";\n"]}