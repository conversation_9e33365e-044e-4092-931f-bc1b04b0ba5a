# 🏪 Electronics Store Dashboard

## 🚀 **تشغيل التطبيق**

### 1. تثبيت التبعيات:
```bash
npm install
```

### 2. تشغيل التطبيق:
```bash
npm run dev
```

### 3. فتح المتصفح:
```
http://localhost:3000
```

## 🎨 **الميزات المتاحة**

### ✅ **Dashboard الرئيسي:**
- 📊 إحصائيات شاملة (المبيعات، المنتجات، الأرباح)
- 📈 رسوم بيانية تفاعلية للمبيعات
- 🏆 أفضل المنتجات مبيعاً
- 🧾 آخر المبيعات
- ⚠️ تنبيهات المخزون المنخفض

### 🎯 **التصميم:**
- 🌙 دعم الوضع الليلي والنهاري
- 📱 تصميم متجاوب (Responsive)
- 🎨 Material-UI مع تخصيصات عربية
- ⚡ أداء عالي مع React 18
- 🔄 تحديثات فورية للبيانات

### 🛠️ **التقنيات المستخدمة:**
- React 18 + TypeScript
- Material-UI (MUI)
- Recharts للرسوم البيانية
- React Query لإدارة البيانات
- Vite للبناء السريع

## 📋 **الصفحات المتاحة:**
- ✅ Dashboard (مكتمل)
- 🔄 Products (قريباً)
- 🔄 POS (قريباً)
- 🔄 Reports (قريباً)
- 🔄 Settings (قريباً)

## 🔗 **ربط Backend:**
تأكد من تشغيل Backend API على المنفذ 5226:
```bash
dotnet run --project ../ElectronicsStore.API
```
