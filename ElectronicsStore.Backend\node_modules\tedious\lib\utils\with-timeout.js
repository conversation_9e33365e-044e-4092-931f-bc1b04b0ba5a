"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withTimeout = withTimeout;
var _nodeAbortController = require("node-abort-controller");
var _timeoutError = _interopRequireDefault(require("../errors/timeout-error"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Run the function `func` with an `AbortSignal` that will automatically abort after the time specified
 * by `timeout` or when the given `signal` is aborted.
 *
 * On timeout, the `timeoutSignal` will be aborted and a `TimeoutError` will be thrown.
 */
async function withTimeout(timeout, func, signal) {
  const timeoutController = new _nodeAbortController.AbortController();
  const abortCurrentAttempt = () => {
    timeoutController.abort();
  };
  const timer = setTimeout(abortCurrentAttempt, timeout);
  signal === null || signal === void 0 ? void 0 : signal.addEventListener('abort', abortCurrentAttempt, {
    once: true
  });
  try {
    return await func(timeoutController.signal);
  } catch (err) {
    if (err instanceof Error && err.name === 'AbortError' && !(signal && signal.aborted)) {
      throw new _timeoutError.default();
    }
    throw err;
  } finally {
    signal === null || signal === void 0 ? void 0 : signal.removeEventListener('abort', abortCurrentAttempt);
    clearTimeout(timer);
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfbm9kZUFib3J0Q29udHJvbGxlciIsInJlcXVpcmUiLCJfdGltZW91dEVycm9yIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsIm9iaiIsIl9fZXNNb2R1bGUiLCJkZWZhdWx0Iiwid2l0aFRpbWVvdXQiLCJ0aW1lb3V0IiwiZnVuYyIsInNpZ25hbCIsInRpbWVvdXRDb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwiYWJvcnRDdXJyZW50QXR0ZW1wdCIsImFib3J0IiwidGltZXIiLCJzZXRUaW1lb3V0IiwiYWRkRXZlbnRMaXN0ZW5lciIsIm9uY2UiLCJlcnIiLCJFcnJvciIsIm5hbWUiLCJhYm9ydGVkIiwiVGltZW91dEVycm9yIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNsZWFyVGltZW91dCJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy91dGlscy93aXRoLXRpbWVvdXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQWJvcnRDb250cm9sbGVyLCBBYm9ydFNpZ25hbCB9IGZyb20gJ25vZGUtYWJvcnQtY29udHJvbGxlcic7XG5pbXBvcnQgVGltZW91dEVycm9yIGZyb20gJy4uL2Vycm9ycy90aW1lb3V0LWVycm9yJztcblxuLyoqXG4gKiBSdW4gdGhlIGZ1bmN0aW9uIGBmdW5jYCB3aXRoIGFuIGBBYm9ydFNpZ25hbGAgdGhhdCB3aWxsIGF1dG9tYXRpY2FsbHkgYWJvcnQgYWZ0ZXIgdGhlIHRpbWUgc3BlY2lmaWVkXG4gKiBieSBgdGltZW91dGAgb3Igd2hlbiB0aGUgZ2l2ZW4gYHNpZ25hbGAgaXMgYWJvcnRlZC5cbiAqXG4gKiBPbiB0aW1lb3V0LCB0aGUgYHRpbWVvdXRTaWduYWxgIHdpbGwgYmUgYWJvcnRlZCBhbmQgYSBgVGltZW91dEVycm9yYCB3aWxsIGJlIHRocm93bi5cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdpdGhUaW1lb3V0PFQ+KHRpbWVvdXQ6IG51bWJlciwgZnVuYzogKHRpbWVvdXRTaWduYWw6IEFib3J0U2lnbmFsKSA9PiBQcm9taXNlPFQ+LCBzaWduYWw/OiBBYm9ydFNpZ25hbCk6IFByb21pc2U8VD4ge1xuICBjb25zdCB0aW1lb3V0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgY29uc3QgYWJvcnRDdXJyZW50QXR0ZW1wdCA9ICgpID0+IHsgdGltZW91dENvbnRyb2xsZXIuYWJvcnQoKTsgfTtcblxuICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoYWJvcnRDdXJyZW50QXR0ZW1wdCwgdGltZW91dCk7XG4gIHNpZ25hbD8uYWRkRXZlbnRMaXN0ZW5lcignYWJvcnQnLCBhYm9ydEN1cnJlbnRBdHRlbXB0LCB7IG9uY2U6IHRydWUgfSk7XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4gYXdhaXQgZnVuYyh0aW1lb3V0Q29udHJvbGxlci5zaWduYWwpO1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICBpZiAoZXJyIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyLm5hbWUgPT09ICdBYm9ydEVycm9yJyAmJiAhKHNpZ25hbCAmJiBzaWduYWwuYWJvcnRlZCkpIHtcbiAgICAgIHRocm93IG5ldyBUaW1lb3V0RXJyb3IoKTtcbiAgICB9XG5cbiAgICB0aHJvdyBlcnI7XG4gIH0gZmluYWxseSB7XG4gICAgc2lnbmFsPy5yZW1vdmVFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0Q3VycmVudEF0dGVtcHQpO1xuICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gIH1cbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsSUFBQUEsb0JBQUEsR0FBQUMsT0FBQTtBQUNBLElBQUFDLGFBQUEsR0FBQUMsc0JBQUEsQ0FBQUYsT0FBQTtBQUFtRCxTQUFBRSx1QkFBQUMsR0FBQSxXQUFBQSxHQUFBLElBQUFBLEdBQUEsQ0FBQUMsVUFBQSxHQUFBRCxHQUFBLEtBQUFFLE9BQUEsRUFBQUYsR0FBQTtBQUVuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxlQUFlRyxXQUFXQSxDQUFJQyxPQUFlLEVBQUVDLElBQWdELEVBQUVDLE1BQW9CLEVBQWM7RUFDeEksTUFBTUMsaUJBQWlCLEdBQUcsSUFBSUMsb0NBQWUsQ0FBQyxDQUFDO0VBQy9DLE1BQU1DLG1CQUFtQixHQUFHQSxDQUFBLEtBQU07SUFBRUYsaUJBQWlCLENBQUNHLEtBQUssQ0FBQyxDQUFDO0VBQUUsQ0FBQztFQUVoRSxNQUFNQyxLQUFLLEdBQUdDLFVBQVUsQ0FBQ0gsbUJBQW1CLEVBQUVMLE9BQU8sQ0FBQztFQUN0REUsTUFBTSxhQUFOQSxNQUFNLHVCQUFOQSxNQUFNLENBQUVPLGdCQUFnQixDQUFDLE9BQU8sRUFBRUosbUJBQW1CLEVBQUU7SUFBRUssSUFBSSxFQUFFO0VBQUssQ0FBQyxDQUFDO0VBRXRFLElBQUk7SUFDRixPQUFPLE1BQU1ULElBQUksQ0FBQ0UsaUJBQWlCLENBQUNELE1BQU0sQ0FBQztFQUM3QyxDQUFDLENBQUMsT0FBT1MsR0FBRyxFQUFFO0lBQ1osSUFBSUEsR0FBRyxZQUFZQyxLQUFLLElBQUlELEdBQUcsQ0FBQ0UsSUFBSSxLQUFLLFlBQVksSUFBSSxFQUFFWCxNQUFNLElBQUlBLE1BQU0sQ0FBQ1ksT0FBTyxDQUFDLEVBQUU7TUFDcEYsTUFBTSxJQUFJQyxxQkFBWSxDQUFDLENBQUM7SUFDMUI7SUFFQSxNQUFNSixHQUFHO0VBQ1gsQ0FBQyxTQUFTO0lBQ1JULE1BQU0sYUFBTkEsTUFBTSx1QkFBTkEsTUFBTSxDQUFFYyxtQkFBbUIsQ0FBQyxPQUFPLEVBQUVYLG1CQUFtQixDQUFDO0lBQ3pEWSxZQUFZLENBQUNWLEtBQUssQ0FBQztFQUNyQjtBQUNGIn0=