"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _events = require("events");
var _errors = require("./errors");
var _types = require("./always-encrypted/types");
/**
 * The callback is called when the request has completed, either successfully or with an error.
 * If an error occurs during execution of the statement(s), then `err` will describe the error.
 *
 * As only one request at a time may be executed on a connection, another request should not
 * be initiated until this callback is called.
 *
 * This callback is called before `requestCompleted` is emitted.
 */

/**
 * ```js
 * const { Request } = require('tedious');
 * const request = new Request("select 42, 'hello world'", (err, rowCount) {
 *   // Request completion callback...
 * });
 * connection.execSql(request);
 * ```
 */
class Request extends _events.EventEmitter {
  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * This event, describing result set columns, will be emitted before row
   * events are emitted. This event may be emitted multiple times when more
   * than one recordset is produced by the statement.
   *
   * An array like object, where the columns can be accessed either by index
   * or name. Columns with a name that is an integer are not accessible by name,
   * as it would be interpreted as an array index.
   */

  /**
   * The request has been prepared and can be used in subsequent calls to execute and unprepare.
   */

  /**
   * The request encountered an error and has not been prepared.
   */

  /**
   * A row resulting from execution of the SQL statement.
   */

  /**
   * All rows from a result set have been provided (through `row` events).
   *
   * This token is used to indicate the completion of a SQL statement.
   * As multiple SQL statements can be sent to the server in a single SQL batch, multiple `done` can be generated.
   * An `done` event is emitted for each SQL statement in the SQL batch except variable declarations.
   * For execution of SQL statements within stored procedures, `doneProc` and `doneInProc` events are used in place of `done`.
   *
   * If you are using [[Connection.execSql]] then SQL server may treat the multiple calls with the same query as a stored procedure.
   * When this occurs, the `doneProc` and `doneInProc` events may be emitted instead. You must handle both events to ensure complete coverage.
   */

  /**
   * `request.on('doneInProc', function (rowCount, more, rows) { });`
   *
   * Indicates the completion status of a SQL statement within a stored procedure. All rows from a statement
   * in a stored procedure have been provided (through `row` events).
   *
   * This event may also occur when executing multiple calls with the same query using [[execSql]].
   */

  /**
   * Indicates the completion status of a stored procedure. This is also generated for stored procedures
   * executed through SQL statements.\
   * This event may also occur when executing multiple calls with the same query using [[execSql]].
   */

  /**
   * A value for an output parameter (that was added to the request with [[addOutputParameter]]).
   * See also `Using Parameters`.
   */

  /**
   * This event gives the columns by which data is ordered, if `ORDER BY` clause is executed in SQL Server.
   */

  on(event, listener) {
    return super.on(event, listener);
  }

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  emit(event, ...args) {
    return super.emit(event, ...args);
  }

  /**
   * @param sqlTextOrProcedure
   *   The SQL statement to be executed
   *
   * @param callback
   *   The callback to execute once the request has been fully completed.
   */
  constructor(sqlTextOrProcedure, callback, options) {
    super();
    this.sqlTextOrProcedure = sqlTextOrProcedure;
    this.parameters = [];
    this.parametersByName = {};
    this.preparing = false;
    this.handle = undefined;
    this.canceled = false;
    this.paused = false;
    this.error = undefined;
    this.connection = undefined;
    this.timeout = undefined;
    this.userCallback = callback;
    this.statementColumnEncryptionSetting = options && options.statementColumnEncryptionSetting || _types.SQLServerStatementColumnEncryptionSetting.UseConnectionSetting;
    this.cryptoMetadataLoaded = false;
    this.callback = function (err, rowCount, rows) {
      if (this.preparing) {
        this.preparing = false;
        if (err) {
          this.emit('error', err);
        } else {
          this.emit('prepared');
        }
      } else {
        this.userCallback(err, rowCount, rows);
        this.emit('requestCompleted');
      }
    };
  }

  /**
   * @param name
   *   The parameter name. This should correspond to a parameter in the SQL,
   *   or a parameter that a called procedure expects. The name should not start with `@`.
   *
   * @param type
   *   One of the supported data types.
   *
   * @param value
   *   The value that the parameter is to be given. The Javascript type of the
   *   argument should match that documented for data types.
   *
   * @param options
   *   Additional type options. Optional.
   */
  // TODO: `type` must be a valid TDS value type
  addParameter(name, type, value, options) {
    const {
      output = false,
      length,
      precision,
      scale
    } = options ?? {};
    const parameter = {
      type: type,
      name: name,
      value: value,
      output: output,
      length: length,
      precision: precision,
      scale: scale
    };
    this.parameters.push(parameter);
    this.parametersByName[name] = parameter;
  }

  /**
   * @param name
   *   The parameter name. This should correspond to a parameter in the SQL,
   *   or a parameter that a called procedure expects.
   *
   * @param type
   *   One of the supported data types.
   *
   * @param value
   *   The value that the parameter is to be given. The Javascript type of the
   *   argument should match that documented for data types
   *
   * @param options
   *   Additional type options. Optional.
   */
  addOutputParameter(name, type, value, options) {
    this.addParameter(name, type, value, {
      ...options,
      output: true
    });
  }

  /**
   * @private
   */
  makeParamsParameter(parameters) {
    let paramsParameter = '';
    for (let i = 0, len = parameters.length; i < len; i++) {
      const parameter = parameters[i];
      if (paramsParameter.length > 0) {
        paramsParameter += ', ';
      }
      paramsParameter += '@' + parameter.name + ' ';
      paramsParameter += parameter.type.declaration(parameter);
      if (parameter.output) {
        paramsParameter += ' OUTPUT';
      }
    }
    return paramsParameter;
  }

  /**
   * @private
   */
  validateParameters(collation) {
    for (let i = 0, len = this.parameters.length; i < len; i++) {
      const parameter = this.parameters[i];
      try {
        parameter.value = parameter.type.validate(parameter.value, collation);
      } catch (error) {
        throw new _errors.RequestError('Validation failed for parameter \'' + parameter.name + '\'. ' + error.message, 'EPARAM');
      }
    }
  }

  /**
   * Temporarily suspends the flow of data from the database. No more `row` events will be emitted until [[resume] is called.
   * If this request is already in a paused state, calling [[pause]] has no effect.
   */
  pause() {
    if (this.paused) {
      return;
    }
    this.emit('pause');
    this.paused = true;
  }

  /**
   * Resumes the flow of data from the database.
   * If this request is not in a paused state, calling [[resume]] has no effect.
   */
  resume() {
    if (!this.paused) {
      return;
    }
    this.paused = false;
    this.emit('resume');
  }

  /**
   * Cancels a request while waiting for a server response.
   */
  cancel() {
    if (this.canceled) {
      return;
    }
    this.canceled = true;
    this.emit('cancel');
  }

  /**
   * Sets a timeout for this request.
   *
   * @param timeout
   *   The number of milliseconds before the request is considered failed,
   *   or `0` for no timeout. When no timeout is set for the request,
   *   the [[ConnectionOptions.requestTimeout]] of the [[Connection]] is used.
   */
  setTimeout(timeout) {
    this.timeout = timeout;
  }
}
var _default = Request;
exports.default = _default;
module.exports = Request;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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