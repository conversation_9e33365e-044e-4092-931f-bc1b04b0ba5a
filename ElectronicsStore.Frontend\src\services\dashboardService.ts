import { apiService } from './apiClient';
import { 
  DashboardStats, 
  SalesData, 
  TopProduct, 
  RecentSale, 
  LowStockAlert 
} from '../types';

// Dashboard API endpoints
const ENDPOINTS = {
  DASHBOARD_STATS: '/dashboard/stats',
  SALES_DATA: '/dashboard/sales-data',
  TOP_PRODUCTS: '/dashboard/top-products',
  RECENT_SALES: '/dashboard/recent-sales',
  LOW_STOCK_ALERTS: '/dashboard/low-stock-alerts',
};

// Dashboard Service
export const dashboardService = {
  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      // For now, return mock data until API is implemented
      return {
        totalSales: 125000,
        totalProducts: 450,
        lowStockProducts: 12,
        totalCustomers: 89,
        todaySales: 8500,
        monthlyRevenue: 125000,
        totalProfit: 35000,
        totalExpenses: 90000,
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  // Get sales data for charts
  getSalesData: async (days: number = 30): Promise<SalesData[]> => {
    try {
      // Mock data for now
      const mockData: SalesData[] = [];
      const today = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        mockData.push({
          date: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 5000) + 2000,
          profit: Math.floor(Math.random() * 1500) + 500,
          orders: Math.floor(Math.random() * 50) + 10,
        });
      }
      
      return mockData;
    } catch (error) {
      console.error('Error fetching sales data:', error);
      throw error;
    }
  },

  // Get top selling products
  getTopProducts: async (limit: number = 5): Promise<TopProduct[]> => {
    try {
      // Mock data for now
      return [
        {
          id: 1,
          name: 'iPhone 15 Pro',
          totalSold: 45,
          revenue: 67500,
          profit: 13500,
        },
        {
          id: 2,
          name: 'Samsung Galaxy S24',
          totalSold: 38,
          revenue: 45600,
          profit: 9120,
        },
        {
          id: 3,
          name: 'MacBook Air M3',
          totalSold: 22,
          revenue: 48400,
          profit: 9680,
        },
        {
          id: 4,
          name: 'iPad Pro 12.9',
          totalSold: 31,
          revenue: 37200,
          profit: 7440,
        },
        {
          id: 5,
          name: 'AirPods Pro',
          totalSold: 67,
          revenue: 20100,
          profit: 6030,
        },
      ];
    } catch (error) {
      console.error('Error fetching top products:', error);
      throw error;
    }
  },

  // Get recent sales
  getRecentSales: async (limit: number = 10): Promise<RecentSale[]> => {
    try {
      // Mock data for now
      const mockSales: RecentSale[] = [];
      const customers = ['أحمد محمد', 'فاطمة علي', 'محمد سعد', 'نورا أحمد', 'خالد عبدالله'];
      const paymentMethods = ['نقدي', 'بطاقة', 'آجل'];
      
      for (let i = 0; i < limit; i++) {
        const date = new Date();
        date.setHours(date.getHours() - Math.floor(Math.random() * 24));
        
        mockSales.push({
          id: i + 1,
          invoiceNumber: `INV-${String(1000 + i).padStart(4, '0')}`,
          customerName: customers[Math.floor(Math.random() * customers.length)],
          totalAmount: Math.floor(Math.random() * 5000) + 500,
          paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
          createdAt: date.toISOString(),
          itemsCount: Math.floor(Math.random() * 5) + 1,
        });
      }
      
      return mockSales;
    } catch (error) {
      console.error('Error fetching recent sales:', error);
      throw error;
    }
  },

  // Get low stock alerts
  getLowStockAlerts: async (): Promise<LowStockAlert[]> => {
    try {
      // Mock data for now
      return [
        {
          id: 1,
          productName: 'iPhone 15 Pro Max',
          currentStock: 2,
          minimumStock: 10,
          categoryName: 'هواتف ذكية',
          urgencyLevel: 'critical',
        },
        {
          id: 2,
          productName: 'Samsung Galaxy Buds',
          currentStock: 5,
          minimumStock: 15,
          categoryName: 'إكسسوارات',
          urgencyLevel: 'warning',
        },
        {
          id: 3,
          productName: 'MacBook Pro 16"',
          currentStock: 8,
          minimumStock: 12,
          categoryName: 'لابتوب',
          urgencyLevel: 'low',
        },
        {
          id: 4,
          productName: 'iPad Air',
          currentStock: 3,
          minimumStock: 8,
          categoryName: 'أجهزة لوحية',
          urgencyLevel: 'warning',
        },
      ];
    } catch (error) {
      console.error('Error fetching low stock alerts:', error);
      throw error;
    }
  },

  // Get all dashboard data at once
  getAllDashboardData: async () => {
    try {
      const [stats, salesData, topProducts, recentSales, lowStockAlerts] = await Promise.all([
        dashboardService.getDashboardStats(),
        dashboardService.getSalesData(),
        dashboardService.getTopProducts(),
        dashboardService.getRecentSales(),
        dashboardService.getLowStockAlerts(),
      ]);

      return {
        stats,
        salesData,
        topProducts,
        recentSales,
        lowStockAlerts,
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  },
};

export default dashboardService;
