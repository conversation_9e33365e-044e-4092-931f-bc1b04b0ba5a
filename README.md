# 🏪 نظام إدارة متجر الإلكترونيات

نظام إدارة متكامل لمتاجر الإلكترونيات مع واجهة عربية حديثة وقاعدة بيانات SQL Server.

## ✨ المميزات

### 📦 إدارة المنتجات
- ✅ إضافة وتعديل وحذف المنتجات
- ✅ تصنيف المنتجات حسب الفئات
- ✅ ربط المنتجات بالموردين
- ✅ إدارة الأسعار (التكلفة، سعر البيع، الحد الأدنى)
- ✅ حساب هامش الربح تلقائياً
- ✅ نظام الباركود
- ✅ رفع صور المنتجات

### 💰 نقاط البيع (POS)
- ✅ واجهة بيع سهلة الاستخدام
- ✅ البحث بالاسم أو الباركود
- ✅ إدارة السلة
- ✅ حساب الضرائب والخصومات
- ✅ طرق دفع متعددة (نقدي، بطاقة، آجل)
- ✅ طباعة الفواتير

### 📊 إدارة المخزون
- ✅ تتبع المخزون الحالي
- ✅ تنبيهات المخزون المنخفض
- ✅ سجل حركة المخزون
- ✅ تقييم قيمة المخزون

### 👥 إدارة العملاء
- ✅ إضافة وتعديل العملاء
- ✅ عملاء أفراد وشركات
- ✅ إدارة الحد الائتماني
- ✅ تتبع تاريخ المشتريات

### 📈 التقارير والإحصائيات
- ✅ لوحة تحكم تفاعلية
- ✅ تقارير المبيعات اليومية
- ✅ إحصائيات المنتجات الأكثر مبيعاً
- ✅ تحليل طرق الدفع
- ✅ مخططات بيانية

### 🔐 إدارة المستخدمين
- ✅ نظام أدوار وصلاحيات
- ✅ تسجيل دخول آمن
- ✅ تتبع العمليات

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Material-UI (MUI)** للواجهة
- **React Router** للتنقل
- **TanStack Query** لإدارة البيانات
- **Axios** للـ API calls

### Backend
- **Node.js** مع Express
- **SQL Server** قاعدة البيانات
- **JWT** للمصادقة
- **Multer** لرفع الملفات
- **CORS** و **Helmet** للأمان

## 📋 متطلبات النظام

- **Node.js** 16+ 
- **SQL Server** 2019+
- **npm** أو **yarn**

## 🚀 تعليمات التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- تشغيل السكريبت في SQL Server Management Studio
-- الملف موجود في: database-schema.sql
```

### 2. إعداد Backend

```bash
cd ElectronicsStore.Backend

# تثبيت المكتبات
npm install

# إعداد متغيرات البيئة
cp .env.example .env

# تعديل إعدادات قاعدة البيانات في .env
DB_SERVER=localhost
DB_NAME=ElectronicsStoreDB
DB_USER=sa
DB_PASSWORD=your_password

# تشغيل الخادم
npm run start-sqlserver
```

### 3. إعداد Frontend

```bash
cd ElectronicsStore.Frontend

# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm start
```

## 🔧 إعداد قاعدة البيانات

### إنشاء قاعدة البيانات والجداول

```sql
-- 1. إنشاء قاعدة البيانات
CREATE DATABASE ElectronicsStoreDB;
GO
USE ElectronicsStoreDB;
GO

-- 2. تشغيل السكريبت الكامل من الملف المرفق
```

### إدخال البيانات الأساسية

```sql
-- إضافة الفئات
INSERT INTO categories (name) VALUES 
('هواتف ذكية'),
('لابتوب'),
('أجهزة لوحية'),
('إكسسوارات');

-- إضافة الموردين
INSERT INTO suppliers (name, phone, email) VALUES 
('شركة التقنية المتقدمة', '0501234567', '<EMAIL>'),
('مؤسسة الإلكترونيات', '0507654321', '<EMAIL>');

-- إضافة الأدوار
INSERT INTO roles (name) VALUES 
('admin'),
('manager'),
('cashier');

-- إضافة المستخدمين
INSERT INTO users (username, password, role_id) VALUES 
('admin', 'hashed_password', 1);
```

## 📱 كيفية الاستخدام

### 1. تسجيل الدخول
- افتح المتصفح على `http://localhost:3001`
- استخدم بيانات المدير للدخول

### 2. إضافة المنتجات
- اذهب إلى "المنتجات" → "إضافة منتج جديد"
- املأ البيانات المطلوبة
- احفظ المنتج

### 3. البيع
- اذهب إلى "نقاط البيع"
- ابحث عن المنتجات وأضفها للسلة
- اختر طريقة الدفع واتمم البيع

### 4. المتابعة
- راجع التقارير في لوحة التحكم
- تابع المخزون والتنبيهات

## 🔗 الروابط المهمة

- **Frontend:** http://localhost:3001
- **Backend API:** http://localhost:5000
- **API Health:** http://localhost:5000/api/health
- **Products API:** http://localhost:5000/api/products
- **Sales API:** http://localhost:5000/api/sales

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطويره بـ ❤️ للمجتمع العربي**
