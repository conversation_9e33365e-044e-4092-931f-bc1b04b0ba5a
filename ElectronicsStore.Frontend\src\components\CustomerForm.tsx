import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  InputAdornment,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';

interface Customer {
  id?: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  customerType: 'individual' | 'business';
  businessName?: string;
  taxNumber?: string;
  status: 'active' | 'inactive' | 'vip';
  creditLimit: number;
  notes: string;
  birthDate?: string;
  joinDate: string;
}

interface CustomerFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => void;
  customer?: Customer | null;
  loading?: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  open,
  onClose,
  onSave,
  customer,
  loading = false,
}) => {
  const [formData, setFormData] = useState<Customer>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    customerType: 'individual',
    businessName: '',
    taxNumber: '',
    status: 'active',
    creditLimit: 0,
    notes: '',
    birthDate: '',
    joinDate: new Date().toISOString().split('T')[0],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const cities = [
    'الرياض',
    'جدة',
    'مكة المكرمة',
    'المدينة المنورة',
    'الدمام',
    'الخبر',
    'الظهران',
    'الطائف',
    'بريدة',
    'تبوك',
    'خميس مشيط',
    'حائل',
    'المجمعة',
    'الجبيل',
    'نجران',
    'جازان',
    'ينبع',
    'القطيف',
    'الأحساء',
    'أبها',
    'أخرى',
  ];

  useEffect(() => {
    if (customer) {
      setFormData(customer);
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        postalCode: '',
        customerType: 'individual',
        businessName: '',
        taxNumber: '',
        status: 'active',
        creditLimit: 0,
        notes: '',
        birthDate: '',
        joinDate: new Date().toISOString().split('T')[0],
      });
    }
    setErrors({});
  }, [customer, open]);

  const handleChange = (field: keyof Customer, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم العميل مطلوب';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^(\+966|0)?[5][0-9]{8}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'رقم الهاتف غير صحيح';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.city) {
      newErrors.city = 'المدينة مطلوبة';
    }

    if (formData.customerType === 'business') {
      if (!formData.businessName?.trim()) {
        newErrors.businessName = 'اسم الشركة مطلوب للعملاء التجاريين';
      }
      if (formData.taxNumber && !/^[0-9]{15}$/.test(formData.taxNumber)) {
        newErrors.taxNumber = 'الرقم الضريبي يجب أن يكون 15 رقم';
      }
    }

    if (formData.creditLimit < 0) {
      newErrors.creditLimit = 'الحد الائتماني لا يمكن أن يكون سالب';
    }

    if (formData.postalCode && !/^[0-9]{5}$/.test(formData.postalCode)) {
      newErrors.postalCode = 'الرمز البريدي يجب أن يكون 5 أرقام';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  const formatPhoneNumber = (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('966')) {
      return `+${cleaned}`;
    } else if (cleaned.startsWith('05')) {
      return `+966${cleaned.substring(1)}`;
    } else if (cleaned.startsWith('5')) {
      return `+966${cleaned}`;
    }
    return phone;
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    handleChange('phone', formatted);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'vip': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'vip': return 'عميل مميز';
      default: return status;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <PersonIcon />
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {customer ? 'تعديل العميل' : 'إضافة عميل جديد'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Customer Type */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>نوع العميل</InputLabel>
              <Select
                value={formData.customerType}
                onChange={(e) => handleChange('customerType', e.target.value)}
                label="نوع العميل"
              >
                <MenuItem value="individual">فردي</MenuItem>
                <MenuItem value="business">تجاري</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              <PersonIcon sx={{ mr: 1 }} />
              المعلومات الأساسية
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={formData.customerType === 'business' ? 'اسم المسؤول *' : 'اسم العميل *'}
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>حالة العميل</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                label="حالة العميل"
                renderValue={(value) => (
                  <Chip
                    label={getStatusText(value)}
                    color={getStatusColor(value) as any}
                    size="small"
                  />
                )}
              >
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="inactive">غير نشط</MenuItem>
                <MenuItem value="vip">عميل مميز</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Business Information */}
          {formData.customerType === 'business' && (
            <>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="اسم الشركة *"
                  value={formData.businessName}
                  onChange={(e) => handleChange('businessName', e.target.value)}
                  error={!!errors.businessName}
                  helperText={errors.businessName}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <BusinessIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="الرقم الضريبي"
                  value={formData.taxNumber}
                  onChange={(e) => handleChange('taxNumber', e.target.value)}
                  error={!!errors.taxNumber}
                  helperText={errors.taxNumber || '15 رقم'}
                  placeholder="300000000000003"
                />
              </Grid>
            </>
          )}

          {/* Contact Information */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              <PhoneIcon sx={{ mr: 1 }} />
              معلومات الاتصال
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="رقم الهاتف *"
              value={formData.phone}
              onChange={(e) => handlePhoneChange(e.target.value)}
              error={!!errors.phone}
              helperText={errors.phone}
              placeholder="+966501234567"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="البريد الإلكتروني"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              error={!!errors.email}
              helperText={errors.email}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          {/* Address Information */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              <LocationIcon sx={{ mr: 1 }} />
              معلومات العنوان
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="العنوان"
              multiline
              rows={2}
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              placeholder="الشارع، الحي، رقم المبنى..."
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth error={!!errors.city}>
              <InputLabel>المدينة *</InputLabel>
              <Select
                value={formData.city}
                onChange={(e) => handleChange('city', e.target.value)}
                label="المدينة *"
              >
                {cities.map((city) => (
                  <MenuItem key={city} value={city}>
                    {city}
                  </MenuItem>
                ))}
              </Select>
              {errors.city && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                  {errors.city}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="الرمز البريدي"
              value={formData.postalCode}
              onChange={(e) => handleChange('postalCode', e.target.value)}
              error={!!errors.postalCode}
              helperText={errors.postalCode || '5 أرقام'}
              placeholder="12345"
            />
          </Grid>

          {/* Additional Information */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات إضافية
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="الحد الائتماني"
              type="number"
              value={formData.creditLimit}
              onChange={(e) => handleChange('creditLimit', Number(e.target.value))}
              error={!!errors.creditLimit}
              helperText={errors.creditLimit}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          {formData.customerType === 'individual' && (
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="تاريخ الميلاد"
                type="date"
                value={formData.birthDate}
                onChange={(e) => handleChange('birthDate', e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          )}

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات"
              multiline
              rows={3}
              value={formData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
              placeholder="أي ملاحظات إضافية عن العميل..."
            />
          </Grid>

          {/* Credit Limit Warning */}
          {formData.creditLimit > 50000 && (
            <Grid item xs={12}>
              <Alert severity="warning">
                الحد الائتماني مرتفع ({formData.creditLimit.toLocaleString()} ريال). تأكد من الموافقة الإدارية.
              </Alert>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={loading}
        >
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          disabled={loading}
        >
          {loading ? 'جاري الحفظ...' : 'حفظ'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerForm;
