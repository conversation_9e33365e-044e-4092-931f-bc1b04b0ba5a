const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize database connection
const { initializeDatabase } = require('./config/database-sqlserver');

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Routes
app.use('/api/products', require('./routes/products-sqlserver'));
app.use('/api/sales', require('./routes/sales-sqlserver'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    database: 'SQL Server',
    message: 'خادم API يعمل بشكل طبيعي'
  });
});

// Categories endpoint
app.get('/api/categories', async (req, res) => {
  try {
    const { executeQuery } = require('./config/database-sqlserver');
    const result = await executeQuery('SELECT id, name FROM categories ORDER BY name');
    
    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفئات'
    });
  }
});

// Suppliers endpoint
app.get('/api/suppliers', async (req, res) => {
  try {
    const { executeQuery } = require('./config/database-sqlserver');
    const result = await executeQuery('SELECT id, name, phone, email FROM suppliers ORDER BY name');
    
    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الموردين'
    });
  }
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const { executeQuery } = require('./config/database-sqlserver');
    
    const today = new Date().toISOString().split('T')[0];
    
    // Today's sales
    const todayStatsQuery = `
      SELECT 
        COUNT(*) as total_sales,
        ISNULL(SUM(total_amount), 0) as total_revenue,
        ISNULL(AVG(total_amount), 0) as avg_sale_amount
      FROM sales_invoices 
      WHERE CAST(invoice_date AS DATE) = @today
    `;
    
    const todayStats = await executeQuery(todayStatsQuery, { today });
    
    // Product statistics
    const productStatsQuery = `
      SELECT 
        COUNT(*) as total_products,
        COUNT(CASE WHEN iv.current_quantity > 0 THEN 1 END) as products_in_stock,
        COUNT(CASE WHEN ISNULL(iv.current_quantity, 0) <= 10 THEN 1 END) as low_stock_products,
        ISNULL(SUM(iv.current_quantity * p.default_cost_price), 0) as total_stock_value
      FROM products p
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
    `;
    
    const productStats = await executeQuery(productStatsQuery);
    
    // Recent sales
    const recentSalesQuery = `
      SELECT TOP 5
        id, invoice_number, customer_name, total_amount, 
        payment_method, invoice_date
      FROM sales_invoices 
      ORDER BY invoice_date DESC
    `;
    
    const recentSales = await executeQuery(recentSalesQuery);
    
    // Low stock products
    const lowStockQuery = `
      SELECT TOP 5
        p.name, 
        ISNULL(iv.current_quantity, 0) as stock,
        c.name as category_name
      FROM products p
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE ISNULL(iv.current_quantity, 0) <= 10
      ORDER BY ISNULL(iv.current_quantity, 0) ASC
    `;
    
    const lowStockProducts = await executeQuery(lowStockQuery);
    
    // Sales trend (last 7 days)
    const salesTrendQuery = `
      SELECT 
        CAST(invoice_date AS DATE) as date,
        COUNT(*) as sales_count,
        ISNULL(SUM(total_amount), 0) as revenue
      FROM sales_invoices 
      WHERE invoice_date >= DATEADD(day, -7, GETDATE())
      GROUP BY CAST(invoice_date AS DATE)
      ORDER BY date ASC
    `;
    
    const salesTrend = await executeQuery(salesTrendQuery);
    
    res.json({
      success: true,
      data: {
        today: todayStats.recordset[0],
        products: productStats.recordset[0],
        recentSales: recentSales.recordset,
        lowStockProducts: lowStockProducts.recordset,
        salesTrend: salesTrend.recordset
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات لوحة التحكم'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();
    
    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 Server running on http://localhost:${PORT}`);
      console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
      console.log(`📦 Products: http://localhost:${PORT}/api/products`);
      console.log(`💰 Sales: http://localhost:${PORT}/api/sales`);
      console.log(`📈 Dashboard: http://localhost:${PORT}/api/dashboard/stats`);
      console.log(`🗄️ Database: SQL Server`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

module.exports = app;
