{"version": 3, "file": "message.js", "names": ["_stream", "require", "Message", "PassThrough", "constructor", "type", "resetConnection", "ignore", "_default", "exports", "default", "module"], "sources": ["../src/message.ts"], "sourcesContent": ["import { PassThrough } from 'stream';\n\nclass Message extends PassThrough {\n  declare type: number;\n  declare resetConnection: boolean;\n  declare ignore: boolean;\n\n  constructor({ type, resetConnection = false }: { type: number, resetConnection?: boolean | undefined }) {\n    super();\n\n    this.type = type;\n    this.resetConnection = resetConnection;\n    this.ignore = false;\n  }\n}\n\nexport default Message;\nmodule.exports = Message;\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,MAAMC,OAAO,SAASC,mBAAW,CAAC;EAKhCC,WAAWA,CAAC;IAAEC,IAAI;IAAEC,eAAe,GAAG;EAA+D,CAAC,EAAE;IACtG,KAAK,CAAC,CAAC;IAEP,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,MAAM,GAAG,KAAK;EACrB;AACF;AAAC,IAAAC,QAAA,GAEcN,OAAO;AAAAO,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACtBG,MAAM,CAACF,OAAO,GAAGP,OAAO"}