ElectronicsStore/
├── 📁 ElectronicsStore.sln                    # Solution File
├── 📁 ElectronicsStore.Domain/                # طبقة النماذج والكيانات
│   ├── 📁 Entities/
│   │   ├── 📄 User.cs                         # كيان المستخدم
│   │   ├── 📄 Role.cs                         # كيان الدور
│   │   ├── 📄 Permission.cs                   # كيان الصلاحية
│   │   ├── 📄 Category.cs                     # كيان الصنف
│   │   ├── 📄 Supplier.cs                     # كيان المورد
│   │   ├── 📄 Product.cs                      # كيان المنتج
│   │   ├── 📄 SalesInvoice.cs                 # كيان فاتورة البيع
│   │   ├── 📄 SalesInvoiceDetail.cs           # تفاصيل فاتورة البيع
│   │   ├── 📄 PurchaseInvoice.cs              # كيان فاتورة الشراء
│   │   ├── 📄 PurchaseInvoiceDetail.cs        # تفاصيل فاتورة الشراء
│   │   ├── 📄 InventoryLog.cs                 # سجل حركة المخزون
│   │   ├── 📄 SalesReturn.cs                  # مرتجعات المبيعات
│   │   ├── 📄 PurchaseReturn.cs               # مرتجعات المشتريات
│   │   ├── 📄 Expense.cs                      # المصروفات
│   │   ├── 📄 InventoryView.cs                # عرض المخزون
│   │   ├── 📄 InventoryValuationView.cs       # عرض تقييم المخزون
│   │   └── 📄 CogsView.cs                     # عرض تكلفة البضاعة المباعة
│   └── 📄 ElectronicsStore.Domain.csproj
│
├── 📁 ElectronicsStore.Application/           # طبقة منطق الأعمال
│   ├── 📁 DTOs/                              # كائنات نقل البيانات
│   │   ├── 📄 UserDto.cs                      # DTO المستخدم
│   │   ├── 📄 ProductDto.cs                   # DTO المنتج
│   │   ├── 📄 SalesInvoiceDto.cs              # DTO فاتورة البيع
│   │   ├── 📄 LoginDto.cs                     # DTO تسجيل الدخول
│   │   └── 📄 ...                             # باقي DTOs
│   ├── 📁 Services/                          # الخدمات
│   │   ├── 📄 IUserService.cs                 # واجهة خدمة المستخدم
│   │   ├── 📄 UserService.cs                  # تنفيذ خدمة المستخدم
│   │   ├── 📄 IProductService.cs              # واجهة خدمة المنتج
│   │   ├── 📄 ProductService.cs               # تنفيذ خدمة المنتج
│   │   ├── 📄 ISalesService.cs                # واجهة خدمة المبيعات
│   │   ├── 📄 SalesService.cs                 # تنفيذ خدمة المبيعات
│   │   ├── 📄 IInventoryService.cs            # واجهة خدمة المخزون
│   │   ├── 📄 InventoryService.cs             # تنفيذ خدمة المخزون
│   │   └── 📄 ...                             # باقي الخدمات
│   ├── 📁 Interfaces/                        # الواجهات
│   │   ├── 📄 IUnitOfWork.cs                  # واجهة Unit of Work
│   │   ├── 📄 IGenericRepository.cs           # واجهة Repository العامة
│   │   └── 📄 ...                             # واجهات المستودعات
│   ├── 📁 Mappings/                          # تعيين البيانات
│   │   └── 📄 MappingProfile.cs               # ملف تعيين AutoMapper
│   ├── 📁 Configuration/                     # الإعدادات
│   │   └── 📄 JwtSettings.cs                  # إعدادات JWT
│   └── 📄 ElectronicsStore.Application.csproj
│
├── 📁 ElectronicsStore.Infrastructure/        # طبقة الخدمات الخارجية
│   ├── 📁 Services/                          # الخدمات الخارجية
│   │   ├── 📄 IJwtService.cs                  # واجهة خدمة JWT
│   │   ├── 📄 JwtService.cs                   # تنفيذ خدمة JWT
│   │   └── 📄 SeedDataService.cs              # خدمة بذر البيانات
│   └── 📄 ElectronicsStore.Infrastructure.csproj
│
├── 📁 ElectronicsStore.Persistence/           # طبقة الوصول للبيانات
│   ├── 📁 Repositories/                      # المستودعات
│   │   ├── 📄 GenericRepository.cs            # المستودع العام
│   │   ├── 📄 UnitOfWork.cs                   # Unit of Work
│   │   ├── 📄 UserRepository.cs               # مستودع المستخدمين
│   │   ├── 📄 ProductRepository.cs            # مستودع المنتجات
│   │   ├── 📄 SalesRepository.cs              # مستودع المبيعات
│   │   ├── 📄 InventoryRepository.cs          # مستودع المخزون
│   │   └── 📄 ...                             # باقي المستودعات
│   ├── 📄 ElectronicsDbContext.cs             # سياق قاعدة البيانات
│   └── 📄 ElectronicsStore.Persistence.csproj
│
├── 📁 ElectronicsStore.API/                   # طبقة العرض (Web API)
│   ├── 📁 Controllers/                       # المتحكمات
│   │   ├── 📄 AuthController.cs               # متحكم المصادقة
│   │   ├── 📄 UsersController.cs              # متحكم المستخدمين
│   │   ├── 📄 CategoriesController.cs         # متحكم الأصناف
│   │   ├── 📄 ProductsController.cs           # متحكم المنتجات
│   │   ├── 📄 SalesController.cs              # متحكم المبيعات
│   │   ├── 📄 InventoryController.cs          # متحكم المخزون
│   │   └── 📄 RolesController.cs              # متحكم الأدوار
│   ├── 📁 Middleware/                        # الوسطاء
│   │   └── 📄 GlobalExceptionHandlerMiddleware.cs # معالج الأخطاء العام
│   ├── 📁 Properties/                        # خصائص المشروع
│   │   └── 📄 launchSettings.json             # إعدادات التشغيل
│   ├── 📁 logs/                              # ملفات السجلات
│   │   └── 📄 electronics-store-YYYYMMDD.txt  # سجلات يومية
│   ├── 📄 Program.cs                          # نقطة دخول التطبيق
│   ├── 📄 appsettings.json                    # إعدادات التطبيق
│   ├── 📄 appsettings.Development.json        # إعدادات التطوير
│   └── 📄 ElectronicsStore.API.csproj
│
└── 📁 ElectronicsStore.Frontend/              # الواجهة الأمامية (مستقبلية)
    ├── 📁 src/
    │   ├── 📁 components/                     # المكونات
    │   ├── 📁 pages/                          # الصفحات
    │   ├── 📁 services/                       # الخدمات
    │   ├── 📁 utils/                          # الأدوات المساعدة
    │   └── 📄 App.tsx                         # المكون الرئيسي
    ├── 📄 package.json                        # تبعيات Node.js
    └── 📄 tsconfig.json                       # إعدادات TypeScript



