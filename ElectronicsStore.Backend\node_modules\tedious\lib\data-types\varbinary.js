"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const MAX = (1 << 16) - 1;
const UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);
const PLP_TERMINATOR = Buffer.from([0x00, 0x00, 0x00, 0x00]);
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const MAX_NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF]);
const VarBinary = {
  id: 0xA5,
  type: 'BIGVARBIN',
  name: 'VarBinary',
  maximumLength: 8000,
  declaration: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.
    let length;
    if (parameter.length) {
      length = parameter.length;
    } else if (value != null) {
      length = value.length || 1;
    } else if (value === null && !parameter.output) {
      length = 1;
    } else {
      length = this.maximumLength;
    }
    if (length <= this.maximumLength) {
      return 'varbinary(' + length + ')';
    } else {
      return 'varbinary(max)';
    }
  },
  resolveLength: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.
    if (parameter.length != null) {
      return parameter.length;
    } else if (value != null) {
      return value.length;
    } else {
      return this.maximumLength;
    }
  },
  generateTypeInfo: function (parameter) {
    const buffer = Buffer.alloc(3);
    buffer.writeUInt8(this.id, 0);
    if (parameter.length <= this.maximumLength) {
      buffer.writeUInt16LE(parameter.length, 1);
    } else {
      buffer.writeUInt16LE(MAX, 1);
    }
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      if (parameter.length <= this.maximumLength) {
        return NULL_LENGTH;
      } else {
        return MAX_NULL_LENGTH;
      }
    }
    let value = parameter.value;
    if (!Buffer.isBuffer(value)) {
      value = value.toString();
    }
    const length = Buffer.byteLength(value, 'ucs2');
    if (parameter.length <= this.maximumLength) {
      const buffer = Buffer.alloc(2);
      buffer.writeUInt16LE(length, 0);
      return buffer;
    } else {
      // writePLPBody
      return UNKNOWN_PLP_LEN;
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    let value = parameter.value;
    if (parameter.length <= this.maximumLength) {
      if (Buffer.isBuffer(value)) {
        yield value;
      } else {
        yield Buffer.from(value.toString(), 'ucs2');
      }
    } else {
      // writePLPBody
      if (!Buffer.isBuffer(value)) {
        value = value.toString();
      }
      const length = Buffer.byteLength(value, 'ucs2');
      if (length > 0) {
        const buffer = Buffer.alloc(4);
        buffer.writeUInt32LE(length, 0);
        yield buffer;
        if (Buffer.isBuffer(value)) {
          yield value;
        } else {
          yield Buffer.from(value, 'ucs2');
        }
      }
      yield PLP_TERMINATOR;
    }
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (!Buffer.isBuffer(value)) {
      throw new TypeError('Invalid buffer.');
    }
    return value;
  }
};
var _default = VarBinary;
exports.default = _default;
module.exports = VarBinary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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