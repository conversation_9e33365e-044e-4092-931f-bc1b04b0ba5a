"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _nativeDuplexpair = _interopRequireDefault(require("native-duplexpair"));
var tls = _interopRequireWildcard(require("tls"));
var _events = require("events");
var _message = _interopRequireDefault(require("./message"));
var _packet = require("./packet");
var _incomingMessageStream = _interopRequireDefault(require("./incoming-message-stream"));
var _outgoingMessageStream = _interopRequireDefault(require("./outgoing-message-stream"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class MessageIO extends _events.EventEmitter {
  constructor(socket, packetSize, debug) {
    super();
    this.socket = socket;
    this.debug = debug;
    this.tlsNegotiationComplete = false;
    this.incomingMessageStream = new _incomingMessageStream.default(this.debug);
    this.incomingMessageIterator = this.incomingMessageStream[Symbol.asyncIterator]();
    this.outgoingMessageStream = new _outgoingMessageStream.default(this.debug, {
      packetSize: packetSize
    });
    this.socket.pipe(this.incomingMessageStream);
    this.outgoingMessageStream.pipe(this.socket);
  }
  packetSize(...args) {
    if (args.length > 0) {
      const packetSize = args[0];
      this.debug.log('Packet size changed from ' + this.outgoingMessageStream.packetSize + ' to ' + packetSize);
      this.outgoingMessageStream.packetSize = packetSize;
    }
    if (this.securePair) {
      this.securePair.cleartext.setMaxSendFragment(this.outgoingMessageStream.packetSize);
    }
    return this.outgoingMessageStream.packetSize;
  }

  // Negotiate TLS encryption.
  startTls(credentialsDetails, hostname, trustServerCertificate) {
    if (!credentialsDetails.maxVersion || !['TLSv1.2', 'TLSv1.1', 'TLSv1'].includes(credentialsDetails.maxVersion)) {
      credentialsDetails.maxVersion = 'TLSv1.2';
    }
    const secureContext = tls.createSecureContext(credentialsDetails);
    return new Promise((resolve, reject) => {
      const duplexpair = new _nativeDuplexpair.default();
      const securePair = this.securePair = {
        cleartext: tls.connect({
          socket: duplexpair.socket1,
          servername: hostname,
          secureContext: secureContext,
          rejectUnauthorized: !trustServerCertificate
        }),
        encrypted: duplexpair.socket2
      };
      const onSecureConnect = () => {
        securePair.encrypted.removeListener('readable', onReadable);
        securePair.cleartext.removeListener('error', onError);
        securePair.cleartext.removeListener('secureConnect', onSecureConnect);

        // If we encounter any errors from this point on,
        // we just forward them to the actual network socket.
        securePair.cleartext.once('error', err => {
          this.socket.destroy(err);
        });
        const cipher = securePair.cleartext.getCipher();
        if (cipher) {
          this.debug.log('TLS negotiated (' + cipher.name + ', ' + cipher.version + ')');
        }
        this.emit('secure', securePair.cleartext);
        securePair.cleartext.setMaxSendFragment(this.outgoingMessageStream.packetSize);
        this.outgoingMessageStream.unpipe(this.socket);
        this.socket.unpipe(this.incomingMessageStream);
        this.socket.pipe(securePair.encrypted);
        securePair.encrypted.pipe(this.socket);
        securePair.cleartext.pipe(this.incomingMessageStream);
        this.outgoingMessageStream.pipe(securePair.cleartext);
        this.tlsNegotiationComplete = true;
        resolve();
      };
      const onError = err => {
        securePair.encrypted.removeListener('readable', onReadable);
        securePair.cleartext.removeListener('error', onError);
        securePair.cleartext.removeListener('secureConnect', onSecureConnect);
        securePair.cleartext.destroy();
        securePair.encrypted.destroy();
        reject(err);
      };
      const onReadable = () => {
        // When there is handshake data on the encrypted stream of the secure pair,
        // we wrap it into a `PRELOGIN` message and send it to the server.
        //
        // For each `PRELOGIN` message we sent we get back exactly one response message
        // that contains the server's handshake response data.
        const message = new _message.default({
          type: _packet.TYPE.PRELOGIN,
          resetConnection: false
        });
        let chunk;
        while (chunk = securePair.encrypted.read()) {
          message.write(chunk);
        }
        this.outgoingMessageStream.write(message);
        message.end();
        this.readMessage().then(async response => {
          // Setup readable handler for the next round of handshaking.
          // If we encounter a `secureConnect` on the cleartext side
          // of the secure pair, the `readable` handler is cleared
          // and no further handshake handling will happen.
          securePair.encrypted.once('readable', onReadable);
          for await (const data of response) {
            // We feed the server's handshake response back into the
            // encrypted end of the secure pair.
            securePair.encrypted.write(data);
          }
        }).catch(onError);
      };
      securePair.cleartext.once('error', onError);
      securePair.cleartext.once('secureConnect', onSecureConnect);
      securePair.encrypted.once('readable', onReadable);
    });
  }

  // TODO listen for 'drain' event when socket.write returns false.
  // TODO implement incomplete request cancelation (2.2.1.6)
  sendMessage(packetType, data, resetConnection) {
    const message = new _message.default({
      type: packetType,
      resetConnection: resetConnection
    });
    message.end(data);
    this.outgoingMessageStream.write(message);
    return message;
  }

  /**
   * Read the next incoming message from the socket.
   */
  async readMessage() {
    const result = await this.incomingMessageIterator.next();
    if (result.done) {
      throw new Error('unexpected end of message stream');
    }
    return result.value;
  }
}
var _default = MessageIO;
exports.default = _default;
module.exports = MessageIO;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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