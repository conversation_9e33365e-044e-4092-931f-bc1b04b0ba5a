"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const NULL_LENGTH = Buffer.from([0x00]);
const Time = {
  id: 0x29,
  type: 'TIMEN',
  name: 'Time',
  declaration: function (parameter) {
    return 'time(' + this.resolveScale(parameter) + ')';
  },
  resolveScale: function (parameter) {
    if (parameter.scale != null) {
      return parameter.scale;
    } else if (parameter.value === null) {
      return 0;
    } else {
      return 7;
    }
  },
  generateTypeInfo(parameter) {
    return Buffer.from([this.id, parameter.scale]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    switch (parameter.scale) {
      case 0:
      case 1:
      case 2:
        return Buffer.from([0x03]);
      case 3:
      case 4:
        return Buffer.from([0x04]);
      case 5:
      case 6:
      case 7:
        return Buffer.from([0x05]);
      default:
        throw new Error('invalid scale');
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = new _writableTrackingBuffer.default(16);
    const time = parameter.value;
    let timestamp;
    if (options.useUTC) {
      timestamp = ((time.getUTCHours() * 60 + time.getUTCMinutes()) * 60 + time.getUTCSeconds()) * 1000 + time.getUTCMilliseconds();
    } else {
      timestamp = ((time.getHours() * 60 + time.getMinutes()) * 60 + time.getSeconds()) * 1000 + time.getMilliseconds();
    }
    timestamp = timestamp * Math.pow(10, parameter.scale - 3);
    timestamp += (parameter.value.nanosecondDelta != null ? parameter.value.nanosecondDelta : 0) * Math.pow(10, parameter.scale);
    timestamp = Math.round(timestamp);
    switch (parameter.scale) {
      case 0:
      case 1:
      case 2:
        buffer.writeUInt24LE(timestamp);
        break;
      case 3:
      case 4:
        buffer.writeUInt32LE(timestamp);
        break;
      case 5:
      case 6:
      case 7:
        buffer.writeUInt40LE(timestamp);
    }
    yield buffer.data;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (!(value instanceof Date)) {
      value = new Date(Date.parse(value));
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid time.');
    }
    return value;
  }
};
var _default = Time;
exports.default = _default;
module.exports = Time;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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