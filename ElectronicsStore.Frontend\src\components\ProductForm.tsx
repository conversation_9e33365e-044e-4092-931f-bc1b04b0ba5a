import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  InputAdornment,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

interface Product {
  id?: number;
  name: string;
  description: string;
  default_selling_price: number;
  default_cost_price: number;
  min_selling_price: number;
  stock?: number;
  category_id: number;
  category_name?: string;
  supplier_id?: number;
  supplier_name?: string;
  barcode: string;
  image?: string;
}

interface ProductFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (product: Product) => void;
  product?: Product | null;
  loading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
  open,
  onClose,
  onSave,
  product,
  loading = false,
}) => {
  const [formData, setFormData] = useState<Product>({
    name: '',
    description: '',
    default_selling_price: 0,
    default_cost_price: 0,
    min_selling_price: 0,
    category_id: 0,
    supplier_id: 0,
    barcode: '',
    image: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [imagePreview, setImagePreview] = useState<string>('');

  const [categories, setCategories] = useState<Array<{id: number, name: string}>>([]);
  const [suppliers, setSuppliers] = useState<Array<{id: number, name: string}>>([]);

  useEffect(() => {
    // Load categories and suppliers
    const loadData = async () => {
      try {
        const [categoriesRes, suppliersRes] = await Promise.all([
          fetch('http://localhost:5000/api/categories'),
          fetch('http://localhost:5000/api/suppliers')
        ]);

        if (categoriesRes.ok) {
          const categoriesData = await categoriesRes.json();
          setCategories(categoriesData.data || []);
        }

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json();
          setSuppliers(suppliersData.data || []);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, []);

  useEffect(() => {
    if (product) {
      setFormData(product);
      setImagePreview(product.image || '');
    } else {
      setFormData({
        name: '',
        description: '',
        default_selling_price: 0,
        default_cost_price: 0,
        min_selling_price: 0,
        category_id: 0,
        supplier_id: 0,
        barcode: '',
        image: '',
      });
      setImagePreview('');
    }
    setErrors({});
  }, [product, open]);

  const handleChange = (field: keyof Product, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        handleChange('image', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const generateBarcode = () => {
    const barcode = Date.now().toString() + Math.floor(Math.random() * 1000).toString();
    handleChange('barcode', barcode);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.category_id || formData.category_id === 0) {
      newErrors.category_id = 'الفئة مطلوبة';
    }

    if (formData.default_selling_price <= 0) {
      newErrors.default_selling_price = 'سعر البيع يجب أن يكون أكبر من صفر';
    }

    if (formData.default_cost_price <= 0) {
      newErrors.default_cost_price = 'التكلفة يجب أن تكون أكبر من صفر';
    }

    if (formData.min_selling_price <= 0) {
      newErrors.min_selling_price = 'الحد الأدنى للسعر يجب أن يكون أكبر من صفر';
    }

    if (formData.default_cost_price >= formData.default_selling_price) {
      newErrors.default_cost_price = 'التكلفة يجب أن تكون أقل من سعر البيع';
    }

    if (formData.min_selling_price > formData.default_selling_price) {
      newErrors.min_selling_price = 'الحد الأدنى للسعر يجب أن يكون أقل من أو يساوي سعر البيع';
    }

    if (!formData.barcode.trim()) {
      newErrors.barcode = 'الباركود مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  const calculateProfit = () => {
    return formData.default_selling_price - formData.default_cost_price;
  };

  const calculateProfitMargin = () => {
    if (formData.default_selling_price === 0) return 0;
    return ((formData.default_selling_price - formData.default_cost_price) / formData.default_selling_price * 100);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Product Image */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 2,
                textAlign: 'center',
                minHeight: 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {imagePreview ? (
                <img
                  src={imagePreview}
                  alt="Product Preview"
                  style={{
                    maxWidth: '100%',
                    maxHeight: 150,
                    objectFit: 'contain',
                    marginBottom: 10,
                  }}
                />
              ) : (
                <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
              )}
              
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="image-upload"
                type="file"
                onChange={handleImageUpload}
              />
              <label htmlFor="image-upload">
                <Button variant="outlined" component="span" size="small">
                  {imagePreview ? 'تغيير الصورة' : 'رفع صورة'}
                </Button>
              </label>
            </Box>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم المنتج *"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="وصف المنتج"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                />
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth error={!!errors.category_id}>
                  <InputLabel>الفئة *</InputLabel>
                  <Select
                    value={formData.category_id}
                    onChange={(e) => handleChange('category_id', e.target.value)}
                    label="الفئة *"
                  >
                    <MenuItem value={0}>اختر الفئة</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.category_id && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                      {errors.category_id}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>المورد</InputLabel>
                  <Select
                    value={formData.supplier_id || 0}
                    onChange={(e) => handleChange('supplier_id', e.target.value)}
                    label="المورد"
                  >
                    <MenuItem value={0}>بدون مورد</MenuItem>
                    {suppliers.map((supplier) => (
                      <MenuItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Grid>

          {/* Pricing */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات التسعير
            </Typography>
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="التكلفة *"
              type="number"
              value={formData.default_cost_price}
              onChange={(e) => handleChange('default_cost_price', Number(e.target.value))}
              error={!!errors.default_cost_price}
              helperText={errors.default_cost_price}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="سعر البيع *"
              type="number"
              value={formData.default_selling_price}
              onChange={(e) => handleChange('default_selling_price', Number(e.target.value))}
              error={!!errors.default_selling_price}
              helperText={errors.default_selling_price}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="الحد الأدنى للسعر *"
              type="number"
              value={formData.min_selling_price}
              onChange={(e) => handleChange('min_selling_price', Number(e.target.value))}
              error={!!errors.min_selling_price}
              helperText={errors.min_selling_price}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="الربح"
              value={calculateProfit().toFixed(2)}
              InputProps={{
                readOnly: true,
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfit() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="هامش الربح"
              value={`${calculateProfitMargin().toFixed(1)}%`}
              InputProps={{
                readOnly: true,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfitMargin() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>



          {/* Barcode */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات إضافية
            </Typography>
          </Grid>

          <Grid item xs={8}>
            <TextField
              fullWidth
              label="الباركود *"
              value={formData.barcode}
              onChange={(e) => handleChange('barcode', e.target.value)}
              error={!!errors.barcode}
              helperText={errors.barcode}
            />
          </Grid>

          <Grid item xs={4}>
            <Button
              fullWidth
              variant="outlined"
              onClick={generateBarcode}
              sx={{ height: '56px' }}
            >
              توليد باركود
            </Button>
          </Grid>

          {/* Profit Alert */}
          {formData.default_selling_price > 0 && formData.default_cost_price > 0 && (
            <Grid item xs={12}>
              {calculateProfitMargin() < 10 ? (
                <Alert severity="warning">
                  هامش الربح منخفض ({calculateProfitMargin().toFixed(1)}%). يُنصح بزيادة السعر أو تقليل التكلفة.
                </Alert>
              ) : (
                <Alert severity="success">
                  هامش ربح جيد ({calculateProfitMargin().toFixed(1)}%)
                </Alert>
              )}
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={loading}
        >
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          disabled={loading}
        >
          {loading ? 'جاري الحفظ...' : 'حفظ'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductForm;
