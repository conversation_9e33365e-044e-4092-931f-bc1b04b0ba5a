import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  InputAdornment,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

interface Product {
  id?: number;
  name: string;
  description: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  category: string;
  brand: string;
  barcode: string;
  image: string;
  status: 'active' | 'inactive';
}

interface ProductFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (product: Product) => void;
  product?: Product | null;
  loading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
  open,
  onClose,
  onSave,
  product,
  loading = false,
}) => {
  const [formData, setFormData] = useState<Product>({
    name: '',
    description: '',
    price: 0,
    cost: 0,
    stock: 0,
    minStock: 5,
    category: '',
    brand: '',
    barcode: '',
    image: '',
    status: 'active',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [imagePreview, setImagePreview] = useState<string>('');

  const categories = [
    'هواتف ذكية',
    'لابتوب',
    'أجهزة لوحية',
    'إكسسوارات',
    'سماعات',
    'شواحن',
    'كابلات',
    'حافظات',
    'أخرى',
  ];

  const brands = [
    'Apple',
    'Samsung',
    'Huawei',
    'Xiaomi',
    'Dell',
    'HP',
    'Lenovo',
    'Asus',
    'Sony',
    'JBL',
    'Anker',
    'أخرى',
  ];

  useEffect(() => {
    if (product) {
      setFormData(product);
      setImagePreview(product.image);
    } else {
      setFormData({
        name: '',
        description: '',
        price: 0,
        cost: 0,
        stock: 0,
        minStock: 5,
        category: '',
        brand: '',
        barcode: '',
        image: '',
        status: 'active',
      });
      setImagePreview('');
    }
    setErrors({});
  }, [product, open]);

  const handleChange = (field: keyof Product, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        handleChange('image', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const generateBarcode = () => {
    const barcode = Date.now().toString() + Math.floor(Math.random() * 1000).toString();
    handleChange('barcode', barcode);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.category) {
      newErrors.category = 'الفئة مطلوبة';
    }

    if (!formData.brand) {
      newErrors.brand = 'العلامة التجارية مطلوبة';
    }

    if (formData.price <= 0) {
      newErrors.price = 'السعر يجب أن يكون أكبر من صفر';
    }

    if (formData.cost <= 0) {
      newErrors.cost = 'التكلفة يجب أن تكون أكبر من صفر';
    }

    if (formData.cost >= formData.price) {
      newErrors.cost = 'التكلفة يجب أن تكون أقل من سعر البيع';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'الكمية لا يمكن أن تكون سالبة';
    }

    if (formData.minStock < 0) {
      newErrors.minStock = 'الحد الأدنى للمخزون لا يمكن أن يكون سالب';
    }

    if (!formData.barcode.trim()) {
      newErrors.barcode = 'الباركود مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  const calculateProfit = () => {
    return formData.price - formData.cost;
  };

  const calculateProfitMargin = () => {
    if (formData.price === 0) return 0;
    return ((formData.price - formData.cost) / formData.price * 100);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Product Image */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 2,
                textAlign: 'center',
                minHeight: 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {imagePreview ? (
                <img
                  src={imagePreview}
                  alt="Product Preview"
                  style={{
                    maxWidth: '100%',
                    maxHeight: 150,
                    objectFit: 'contain',
                    marginBottom: 10,
                  }}
                />
              ) : (
                <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
              )}
              
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="image-upload"
                type="file"
                onChange={handleImageUpload}
              />
              <label htmlFor="image-upload">
                <Button variant="outlined" component="span" size="small">
                  {imagePreview ? 'تغيير الصورة' : 'رفع صورة'}
                </Button>
              </label>
            </Box>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم المنتج *"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="وصف المنتج"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                />
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth error={!!errors.category}>
                  <InputLabel>الفئة *</InputLabel>
                  <Select
                    value={formData.category}
                    onChange={(e) => handleChange('category', e.target.value)}
                    label="الفئة *"
                  >
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.category && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                      {errors.category}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth error={!!errors.brand}>
                  <InputLabel>العلامة التجارية *</InputLabel>
                  <Select
                    value={formData.brand}
                    onChange={(e) => handleChange('brand', e.target.value)}
                    label="العلامة التجارية *"
                  >
                    {brands.map((brand) => (
                      <MenuItem key={brand} value={brand}>
                        {brand}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.brand && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                      {errors.brand}
                    </Typography>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Grid>

          {/* Pricing */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات التسعير
            </Typography>
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="التكلفة *"
              type="number"
              value={formData.cost}
              onChange={(e) => handleChange('cost', Number(e.target.value))}
              error={!!errors.cost}
              helperText={errors.cost}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="سعر البيع *"
              type="number"
              value={formData.price}
              onChange={(e) => handleChange('price', Number(e.target.value))}
              error={!!errors.price}
              helperText={errors.price}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="الربح"
              value={calculateProfit().toFixed(2)}
              InputProps={{
                readOnly: true,
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfit() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="هامش الربح"
              value={`${calculateProfitMargin().toFixed(1)}%`}
              InputProps={{
                readOnly: true,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfitMargin() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>

          {/* Inventory */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات المخزون
            </Typography>
          </Grid>

          <Grid item xs={6} md={4}>
            <TextField
              fullWidth
              label="الكمية الحالية"
              type="number"
              value={formData.stock}
              onChange={(e) => handleChange('stock', Number(e.target.value))}
              error={!!errors.stock}
              helperText={errors.stock}
              InputProps={{
                endAdornment: <InputAdornment position="end">قطعة</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={4}>
            <TextField
              fullWidth
              label="الحد الأدنى للمخزون"
              type="number"
              value={formData.minStock}
              onChange={(e) => handleChange('minStock', Number(e.target.value))}
              error={!!errors.minStock}
              helperText={errors.minStock}
              InputProps={{
                endAdornment: <InputAdornment position="end">قطعة</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>حالة المنتج</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                label="حالة المنتج"
              >
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="inactive">غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Barcode */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات إضافية
            </Typography>
          </Grid>

          <Grid item xs={8}>
            <TextField
              fullWidth
              label="الباركود *"
              value={formData.barcode}
              onChange={(e) => handleChange('barcode', e.target.value)}
              error={!!errors.barcode}
              helperText={errors.barcode}
            />
          </Grid>

          <Grid item xs={4}>
            <Button
              fullWidth
              variant="outlined"
              onClick={generateBarcode}
              sx={{ height: '56px' }}
            >
              توليد باركود
            </Button>
          </Grid>

          {/* Profit Alert */}
          {formData.price > 0 && formData.cost > 0 && (
            <Grid item xs={12}>
              {calculateProfitMargin() < 10 ? (
                <Alert severity="warning">
                  هامش الربح منخفض ({calculateProfitMargin().toFixed(1)}%). يُنصح بزيادة السعر أو تقليل التكلفة.
                </Alert>
              ) : (
                <Alert severity="success">
                  هامش ربح جيد ({calculateProfitMargin().toFixed(1)}%)
                </Alert>
              )}
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={loading}
        >
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          disabled={loading}
        >
          {loading ? 'جاري الحفظ...' : 'حفظ'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductForm;
