import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  InputAdornment,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { productService } from '../services/productService';
import { categoryService } from '../services/categoryService';
import { Product, ProductFormData, Category } from '../types';

// Remove local interface - using imported types from ../types

interface ProductFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (product: Product) => void;
  product?: Product | null;
  loading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
  open,
  onClose,
  onSave,
  product,
  loading = false,
}) => {
  console.log('🔄 ProductForm rendered with open:', open);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    defaultSellingPrice: 0,
    defaultCostPrice: 0,
    minSellingPrice: 0,
    categoryId: 0,
    supplierId: 0,
    barcode: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [imagePreview, setImagePreview] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Array<{id: number, name: string}>>([]);

  useEffect(() => {
    // Load categories using the service
    const loadCategories = async () => {
      try {
        const categoriesData = await categoryService.getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };

    loadCategories();
  }, []);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description || '',
        defaultSellingPrice: product.defaultSellingPrice,
        defaultCostPrice: product.defaultCostPrice,
        minSellingPrice: product.minSellingPrice,
        categoryId: product.categoryId,
        supplierId: product.supplierId || 0,
        barcode: product.barcode,
      });
    } else {
      setFormData({
        name: '',
        description: '',
        defaultSellingPrice: 0,
        defaultCostPrice: 0,
        minSellingPrice: 0,
        categoryId: 0,
        supplierId: 0,
        barcode: '',
      });
    }
    setErrors({});
    setImagePreview('');
  }, [product, open]);

  const handleChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        handleChange('image', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const generateBarcode = () => {
    const barcode = Date.now().toString() + Math.floor(Math.random() * 1000).toString();
    handleChange('barcode', barcode);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.categoryId || formData.categoryId === 0) {
      newErrors.categoryId = 'الفئة مطلوبة';
    }

    if (formData.defaultSellingPrice <= 0) {
      newErrors.defaultSellingPrice = 'سعر البيع يجب أن يكون أكبر من صفر';
    }

    if (formData.defaultCostPrice <= 0) {
      newErrors.defaultCostPrice = 'التكلفة يجب أن تكون أكبر من صفر';
    }

    if (formData.minSellingPrice <= 0) {
      newErrors.minSellingPrice = 'الحد الأدنى للسعر يجب أن يكون أكبر من صفر';
    }

    if (formData.defaultCostPrice >= formData.defaultSellingPrice) {
      newErrors.defaultCostPrice = 'التكلفة يجب أن تكون أقل من سعر البيع';
    }

    if (formData.minSellingPrice > formData.defaultSellingPrice) {
      newErrors.minSellingPrice = 'الحد الأدنى للسعر يجب أن يكون أقل من أو يساوي سعر البيع';
    }

    if (!formData.barcode.trim()) {
      newErrors.barcode = 'الباركود مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      let savedProduct: Product;

      if (product?.id) {
        // Update existing product
        savedProduct = await productService.updateProduct(product.id, formData);
      } else {
        // Create new product
        savedProduct = await productService.createProduct(formData);
      }

      onSave(savedProduct);
      onClose();
    } catch (error: any) {
      console.error('Error saving product:', error);
      // Handle specific validation errors from API
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: error.message || 'حدث خطأ أثناء حفظ المنتج' });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateProfit = () => {
    return formData.defaultSellingPrice - formData.defaultCostPrice;
  };

  const calculateProfitMargin = () => {
    if (formData.defaultSellingPrice === 0) return 0;
    return ((formData.defaultSellingPrice - formData.defaultCostPrice) / formData.defaultSellingPrice * 100);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Product Image */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 2,
                textAlign: 'center',
                minHeight: 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {imagePreview ? (
                <img
                  src={imagePreview}
                  alt="Product Preview"
                  style={{
                    maxWidth: '100%',
                    maxHeight: 150,
                    objectFit: 'contain',
                    marginBottom: 10,
                  }}
                />
              ) : (
                <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
              )}
              
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="image-upload"
                type="file"
                onChange={handleImageUpload}
              />
              <label htmlFor="image-upload">
                <Button variant="outlined" component="span" size="small">
                  {imagePreview ? 'تغيير الصورة' : 'رفع صورة'}
                </Button>
              </label>
            </Box>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم المنتج *"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="وصف المنتج"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                />
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth error={!!errors.categoryId}>
                  <InputLabel>الفئة *</InputLabel>
                  <Select
                    value={formData.categoryId}
                    onChange={(e) => handleChange('categoryId', e.target.value)}
                    label="الفئة *"
                  >
                    <MenuItem value={0}>اختر الفئة</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.categoryId && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                      {errors.categoryId}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="الباركود *"
                  value={formData.barcode}
                  onChange={(e) => handleChange('barcode', e.target.value)}
                  error={!!errors.barcode}
                  helperText={errors.barcode}
                />
              </Grid>
            </Grid>
          </Grid>

          {/* Pricing */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات التسعير
            </Typography>
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="التكلفة *"
              type="number"
              value={formData.defaultCostPrice}
              onChange={(e) => handleChange('defaultCostPrice', Number(e.target.value))}
              error={!!errors.defaultCostPrice}
              helperText={errors.defaultCostPrice}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="سعر البيع *"
              type="number"
              value={formData.defaultSellingPrice}
              onChange={(e) => handleChange('defaultSellingPrice', Number(e.target.value))}
              error={!!errors.defaultSellingPrice}
              helperText={errors.defaultSellingPrice}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="الحد الأدنى للسعر *"
              type="number"
              value={formData.minSellingPrice}
              onChange={(e) => handleChange('minSellingPrice', Number(e.target.value))}
              error={!!errors.minSellingPrice}
              helperText={errors.minSellingPrice}
              InputProps={{
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="الربح"
              value={calculateProfit().toFixed(2)}
              InputProps={{
                readOnly: true,
                endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfit() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <TextField
              fullWidth
              label="هامش الربح"
              value={`${calculateProfitMargin().toFixed(1)}%`}
              InputProps={{
                readOnly: true,
              }}
              sx={{
                '& .MuiInputBase-input': {
                  color: calculateProfitMargin() > 0 ? 'success.main' : 'error.main',
                  fontWeight: 600,
                },
              }}
            />
          </Grid>



          {/* Barcode */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              معلومات إضافية
            </Typography>
          </Grid>

          {/* Profit Alert */}
          {formData.defaultSellingPrice > 0 && formData.defaultCostPrice > 0 && (
            <Grid item xs={12}>
              {calculateProfitMargin() < 10 ? (
                <Alert severity="warning">
                  هامش الربح منخفض ({calculateProfitMargin().toFixed(1)}%). يُنصح بزيادة السعر أو تقليل التكلفة.
                </Alert>
              ) : (
                <Alert severity="success">
                  هامش ربح جيد ({calculateProfitMargin().toFixed(1)}%)
                </Alert>
              )}
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={isSubmitting}
        >
          إلغاء
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : <SaveIcon />}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'جاري الحفظ...' : (product ? 'تحديث' : 'حفظ')}
        </Button>

        {/* Show general error */}
        {errors.general && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errors.general}
          </Alert>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ProductForm;
