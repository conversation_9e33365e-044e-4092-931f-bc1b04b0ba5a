{"version": 3, "file": "varbinary.js", "names": ["MAX", "UNKNOWN_PLP_LEN", "<PERSON><PERSON><PERSON>", "from", "PLP_TERMINATOR", "NULL_LENGTH", "MAX_NULL_LENGTH", "VarBinary", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "output", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "generateParameterLength", "options", "<PERSON><PERSON><PERSON><PERSON>", "toString", "byteLength", "generateParameterData", "writeUInt32LE", "validate", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/varbinary.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst MAX = (1 << 16) - 1;\nconst UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);\nconst PLP_TERMINATOR = Buffer.from([0x00, 0x00, 0x00, 0x00]);\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\nconst MAX_NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst VarBinary: { maximumLength: number } & DataType = {\n  id: 0xA5,\n  type: 'BIGVARBIN',\n  name: 'VarBinary',\n  maximumLength: 8000,\n\n  declaration: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (value != null) {\n      length = value.length || 1;\n    } else if (value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    if (length <= this.maximumLength) {\n      return 'varbinary(' + length + ')';\n    } else {\n      return 'varbinary(max)';\n    }\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n    if (parameter.length != null) {\n      return parameter.length;\n    } else if (value != null) {\n      return value.length;\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo: function(parameter) {\n    const buffer = Buffer.alloc(3);\n    buffer.writeUInt8(this.id, 0);\n\n    if (parameter.length! <= this.maximumLength) {\n      buffer.writeUInt16LE(parameter.length!, 1);\n    } else {\n      buffer.writeUInt16LE(MAX, 1);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      if (parameter.length! <= this.maximumLength) {\n        return NULL_LENGTH;\n      } else {\n        return MAX_NULL_LENGTH;\n      }\n    }\n\n    let value = parameter.value;\n    if (!Buffer.isBuffer(value)) {\n      value = value.toString();\n    }\n\n    const length = Buffer.byteLength(value, 'ucs2');\n\n    if (parameter.length! <= this.maximumLength) {\n      const buffer = Buffer.alloc(2);\n      buffer.writeUInt16LE(length, 0);\n      return buffer;\n    } else { // writePLPBody\n      return UNKNOWN_PLP_LEN;\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    let value = parameter.value;\n\n    if (parameter.length! <= this.maximumLength) {\n      if (Buffer.isBuffer(value)) {\n        yield value;\n      } else {\n        yield Buffer.from(value.toString(), 'ucs2');\n      }\n    } else { // writePLPBody\n      if (!Buffer.isBuffer(value)) {\n        value = value.toString();\n      }\n\n      const length = Buffer.byteLength(value, 'ucs2');\n\n      if (length > 0) {\n        const buffer = Buffer.alloc(4);\n        buffer.writeUInt32LE(length, 0);\n        yield buffer;\n\n        if (Buffer.isBuffer(value)) {\n          yield value;\n        } else {\n          yield Buffer.from(value, 'ucs2');\n        }\n      }\n\n      yield PLP_TERMINATOR;\n    }\n  },\n\n  validate: function(value): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n    if (!Buffer.isBuffer(value)) {\n      throw new TypeError('Invalid buffer.');\n    }\n    return value;\n  }\n};\n\nexport default VarBinary;\nmodule.exports = VarBinary;\n"], "mappings": ";;;;;;AAEA,MAAMA,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AACzB,MAAMC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACrF,MAAMC,cAAc,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAE5D,MAAME,WAAW,GAAGH,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,MAAMG,eAAe,GAAGJ,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAErF,MAAMI,SAA+C,GAAG;EACtDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;IACtC,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxBC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MAC9CD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,IAAII,MAAM,IAAI,IAAI,CAACJ,aAAa,EAAE;MAChC,OAAO,YAAY,GAAGI,MAAM,GAAG,GAAG;IACpC,CAAC,MAAM;MACL,OAAO,gBAAgB;IACzB;EACF,CAAC;EAEDE,aAAa,EAAE,SAAAA,CAASJ,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;IACtC,IAAID,SAAS,CAACE,MAAM,IAAI,IAAI,EAAE;MAC5B,OAAOF,SAAS,CAACE,MAAM;IACzB,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAOA,KAAK,CAACC,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDO,gBAAgB,EAAE,SAAAA,CAASL,SAAS,EAAE;IACpC,MAAMM,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAE7B,IAAIK,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3CQ,MAAM,CAACG,aAAa,CAACT,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLI,MAAM,CAACG,aAAa,CAACtB,GAAG,EAAE,CAAC,CAAC;IAC9B;IAEA,OAAOmB,MAAM;EACf,CAAC;EAEDI,uBAAuBA,CAACV,SAAS,EAAEW,OAAO,EAAE;IAC1C,IAAIX,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;QAC3C,OAAON,WAAW;MACpB,CAAC,MAAM;QACL,OAAOC,eAAe;MACxB;IACF;IAEA,IAAIQ,KAAK,GAAGD,SAAS,CAACC,KAAK;IAC3B,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC3BA,KAAK,GAAGA,KAAK,CAACY,QAAQ,CAAC,CAAC;IAC1B;IAEA,MAAMX,MAAM,GAAGb,MAAM,CAACyB,UAAU,CAACb,KAAK,EAAE,MAAM,CAAC;IAE/C,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,MAAMQ,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACG,aAAa,CAACP,MAAM,EAAE,CAAC,CAAC;MAC/B,OAAOI,MAAM;IACf,CAAC,MAAM;MAAE;MACP,OAAOlB,eAAe;IACxB;EACF,CAAC;EAED,CAAE2B,qBAAqBA,CAACf,SAAS,EAAEW,OAAO,EAAE;IAC1C,IAAIX,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,IAAIA,KAAK,GAAGD,SAAS,CAACC,KAAK;IAE3B,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,IAAIT,MAAM,CAACuB,QAAQ,CAACX,KAAK,CAAC,EAAE;QAC1B,MAAMA,KAAK;MACb,CAAC,MAAM;QACL,MAAMZ,MAAM,CAACC,IAAI,CAACW,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;MAC7C;IACF,CAAC,MAAM;MAAE;MACP,IAAI,CAACxB,MAAM,CAACuB,QAAQ,CAACX,KAAK,CAAC,EAAE;QAC3BA,KAAK,GAAGA,KAAK,CAACY,QAAQ,CAAC,CAAC;MAC1B;MAEA,MAAMX,MAAM,GAAGb,MAAM,CAACyB,UAAU,CAACb,KAAK,EAAE,MAAM,CAAC;MAE/C,IAAIC,MAAM,GAAG,CAAC,EAAE;QACd,MAAMI,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;QAC9BD,MAAM,CAACU,aAAa,CAACd,MAAM,EAAE,CAAC,CAAC;QAC/B,MAAMI,MAAM;QAEZ,IAAIjB,MAAM,CAACuB,QAAQ,CAACX,KAAK,CAAC,EAAE;UAC1B,MAAMA,KAAK;QACb,CAAC,MAAM;UACL,MAAMZ,MAAM,CAACC,IAAI,CAACW,KAAK,EAAE,MAAM,CAAC;QAClC;MACF;MAEA,MAAMV,cAAc;IACtB;EACF,CAAC;EAED0B,QAAQ,EAAE,SAAAA,CAAShB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIiB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOjB,KAAK;EACd;AACF,CAAC;AAAC,IAAAkB,QAAA,GAEazB,SAAS;AAAA0B,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACxBG,MAAM,CAACF,OAAO,GAAG1B,SAAS"}