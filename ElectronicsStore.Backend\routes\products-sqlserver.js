const express = require('express');
const router = express.Router();
const { executeQuery, sql } = require('../config/database-sqlserver');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/products');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'product-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// GET /api/products - Get all products with inventory
router.get('/', async (req, res) => {
  try {
    const { category_id, search, page = 1, limit = 50 } = req.query;
    
    let query = `
      SELECT 
        p.id,
        p.name,
        p.barcode,
        p.description,
        p.default_cost_price,
        p.default_selling_price,
        p.min_selling_price,
        p.created_at,
        c.name as category_name,
        c.id as category_id,
        s.name as supplier_name,
        s.id as supplier_id,
        ISNULL(iv.current_quantity, 0) as stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      WHERE 1=1
    `;
    
    const params = {};
    
    if (category_id && category_id !== 'all') {
      query += ' AND p.category_id = @category_id';
      params.category_id = parseInt(category_id);
    }
    
    if (search) {
      query += ' AND (p.name LIKE @search OR p.barcode LIKE @search OR p.description LIKE @search)';
      params.search = `%${search}%`;
    }
    
    query += ' ORDER BY p.created_at DESC';
    
    // Add pagination
    const offset = (page - 1) * limit;
    query += ` OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY`;
    params.offset = offset;
    params.limit = parseInt(limit);
    
    const result = await executeQuery(query, params);
    
    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE 1=1
    `;
    
    const countParams = {};
    
    if (category_id && category_id !== 'all') {
      countQuery += ' AND p.category_id = @category_id';
      countParams.category_id = parseInt(category_id);
    }
    
    if (search) {
      countQuery += ' AND (p.name LIKE @search OR p.barcode LIKE @search OR p.description LIKE @search)';
      countParams.search = `%${search}%`;
    }
    
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult.recordset[0].total;
    
    res.json({
      success: true,
      data: result.recordset,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتجات'
    });
  }
});

// GET /api/products/:id - Get single product
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT 
        p.id,
        p.name,
        p.barcode,
        p.description,
        p.default_cost_price,
        p.default_selling_price,
        p.min_selling_price,
        p.created_at,
        c.name as category_name,
        c.id as category_id,
        s.name as supplier_name,
        s.id as supplier_id,
        ISNULL(iv.current_quantity, 0) as stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      WHERE p.id = @id
    `;
    
    const result = await executeQuery(query, { id: parseInt(id) });
    
    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    res.json({
      success: true,
      data: result.recordset[0]
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتج'
    });
  }
});

// POST /api/products - Create new product
router.post('/', upload.single('image'), async (req, res) => {
  try {
    const {
      name,
      barcode,
      description,
      category_id,
      supplier_id,
      default_cost_price,
      default_selling_price,
      min_selling_price
    } = req.body;
    
    // Validation
    if (!name || !category_id || !default_cost_price || !default_selling_price || !min_selling_price) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة مفقودة'
      });
    }
    
    // Check if barcode already exists
    if (barcode) {
      const existingProduct = await executeQuery(
        'SELECT id FROM products WHERE barcode = @barcode',
        { barcode }
      );
      
      if (existingProduct.recordset.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'الباركود موجود مسبقاً'
        });
      }
    }
    
    const query = `
      INSERT INTO products (
        name, barcode, description, category_id, supplier_id,
        default_cost_price, default_selling_price, min_selling_price
      )
      OUTPUT INSERTED.id
      VALUES (
        @name, @barcode, @description, @category_id, @supplier_id,
        @default_cost_price, @default_selling_price, @min_selling_price
      )
    `;
    
    const params = {
      name,
      barcode: barcode || null,
      description: description || null,
      category_id: parseInt(category_id),
      supplier_id: supplier_id ? parseInt(supplier_id) : null,
      default_cost_price: parseFloat(default_cost_price),
      default_selling_price: parseFloat(default_selling_price),
      min_selling_price: parseFloat(min_selling_price)
    };
    
    const result = await executeQuery(query, params);
    const newProductId = result.recordset[0].id;
    
    // Get the complete product data
    const newProductQuery = `
      SELECT 
        p.id,
        p.name,
        p.barcode,
        p.description,
        p.default_cost_price,
        p.default_selling_price,
        p.min_selling_price,
        p.created_at,
        c.name as category_name,
        c.id as category_id,
        s.name as supplier_name,
        s.id as supplier_id,
        ISNULL(iv.current_quantity, 0) as stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      WHERE p.id = @id
    `;
    
    const newProductResult = await executeQuery(newProductQuery, { id: newProductId });
    
    res.status(201).json({
      success: true,
      message: 'تم إضافة المنتج بنجاح',
      data: newProductResult.recordset[0]
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة المنتج'
    });
  }
});

// PUT /api/products/:id - Update product
router.put('/:id', upload.single('image'), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      barcode,
      description,
      category_id,
      supplier_id,
      default_cost_price,
      default_selling_price,
      min_selling_price
    } = req.body;
    
    // Check if product exists
    const existingProduct = await executeQuery(
      'SELECT id FROM products WHERE id = @id',
      { id: parseInt(id) }
    );
    
    if (existingProduct.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    // Check if barcode is unique (excluding current product)
    if (barcode) {
      const duplicateBarcode = await executeQuery(
        'SELECT id FROM products WHERE barcode = @barcode AND id != @id',
        { barcode, id: parseInt(id) }
      );
      
      if (duplicateBarcode.recordset.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'الباركود موجود مسبقاً'
        });
      }
    }
    
    const query = `
      UPDATE products SET
        name = @name,
        barcode = @barcode,
        description = @description,
        category_id = @category_id,
        supplier_id = @supplier_id,
        default_cost_price = @default_cost_price,
        default_selling_price = @default_selling_price,
        min_selling_price = @min_selling_price
      WHERE id = @id
    `;
    
    const params = {
      id: parseInt(id),
      name,
      barcode: barcode || null,
      description: description || null,
      category_id: parseInt(category_id),
      supplier_id: supplier_id ? parseInt(supplier_id) : null,
      default_cost_price: parseFloat(default_cost_price),
      default_selling_price: parseFloat(default_selling_price),
      min_selling_price: parseFloat(min_selling_price)
    };
    
    await executeQuery(query, params);
    
    // Get updated product data
    const updatedProductQuery = `
      SELECT 
        p.id,
        p.name,
        p.barcode,
        p.description,
        p.default_cost_price,
        p.default_selling_price,
        p.min_selling_price,
        p.created_at,
        c.name as category_name,
        c.id as category_id,
        s.name as supplier_name,
        s.id as supplier_id,
        ISNULL(iv.current_quantity, 0) as stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN inventory_view iv ON p.id = iv.product_id
      WHERE p.id = @id
    `;
    
    const updatedResult = await executeQuery(updatedProductQuery, { id: parseInt(id) });
    
    res.json({
      success: true,
      message: 'تم تحديث المنتج بنجاح',
      data: updatedResult.recordset[0]
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المنتج'
    });
  }
});

// DELETE /api/products/:id - Delete product
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if product exists
    const product = await executeQuery(
      'SELECT id FROM products WHERE id = @id',
      { id: parseInt(id) }
    );
    
    if (product.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    // Check if product has transactions
    const hasTransactions = await executeQuery(`
      SELECT COUNT(*) as count
      FROM (
        SELECT product_id FROM purchase_invoice_details WHERE product_id = @id
        UNION ALL
        SELECT product_id FROM sales_invoice_details WHERE product_id = @id
        UNION ALL
        SELECT product_id FROM inventory_logs WHERE product_id = @id
      ) t
    `, { id: parseInt(id) });
    
    if (hasTransactions.recordset[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف المنتج لوجود معاملات مرتبطة به'
      });
    }
    
    await executeQuery('DELETE FROM products WHERE id = @id', { id: parseInt(id) });
    
    res.json({
      success: true,
      message: 'تم حذف المنتج بنجاح'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف المنتج'
    });
  }
});

// GET /api/products/categories - Get all categories
router.get('/categories/list', async (req, res) => {
  try {
    const result = await executeQuery('SELECT id, name FROM categories ORDER BY name');
    
    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الفئات'
    });
  }
});

// GET /api/products/suppliers - Get all suppliers
router.get('/suppliers/list', async (req, res) => {
  try {
    const result = await executeQuery('SELECT id, name FROM suppliers ORDER BY name');
    
    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الموردين'
    });
  }
});

module.exports = router;
