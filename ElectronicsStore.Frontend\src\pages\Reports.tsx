import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Chip,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Print as PrintIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as ReportIcon,
  <PERSON><PERSON>hart as ChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import PageContainer from '../components/PageContainer';
import DashboardHeader from '../components/DashboardHeader';

interface ReportData {
  salesByMonth: Array<{ month: string; sales: number; orders: number }>;
  salesByCategory: Array<{ category: string; sales: number; percentage: number }>;
  topProducts: Array<{ name: string; sales: number; quantity: number }>;
  salesTrend: Array<{ date: string; sales: number; profit: number }>;
}

const Reports: React.FC = () => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [reportType, setReportType] = useState('sales');
  const [dateRange, setDateRange] = useState('month');

  // Mock data
  useEffect(() => {
    const mockReportData: ReportData = {
      salesByMonth: [
        { month: 'يناير', sales: 125000, orders: 45 },
        { month: 'فبراير', sales: 98000, orders: 38 },
        { month: 'مارس', sales: 142000, orders: 52 },
        { month: 'أبريل', sales: 118000, orders: 41 },
        { month: 'مايو', sales: 156000, orders: 58 },
        { month: 'يونيو', sales: 134000, orders: 47 },
      ],
      salesByCategory: [
        { category: 'هواتف ذكية', sales: 450000, percentage: 45 },
        { category: 'لابتوب', sales: 320000, percentage: 32 },
        { category: 'إكسسوارات', sales: 150000, percentage: 15 },
        { category: 'أجهزة لوحية', sales: 80000, percentage: 8 },
      ],
      topProducts: [
        { name: 'iPhone 15 Pro', sales: 180000, quantity: 40 },
        { name: 'MacBook Pro M3', sales: 170000, quantity: 20 },
        { name: 'Samsung Galaxy S24', sales: 152000, quantity: 40 },
        { name: 'Dell XPS 13', sales: 126000, quantity: 30 },
        { name: 'AirPods Pro', sales: 95000, quantity: 100 },
      ],
      salesTrend: [
        { date: '2024-01-01', sales: 12000, profit: 3600 },
        { date: '2024-01-02', sales: 15000, profit: 4500 },
        { date: '2024-01-03', sales: 8000, profit: 2400 },
        { date: '2024-01-04', sales: 18000, profit: 5400 },
        { date: '2024-01-05', sales: 22000, profit: 6600 },
        { date: '2024-01-06', sales: 16000, profit: 4800 },
        { date: '2024-01-07', sales: 20000, profit: 6000 },
      ],
    };

    setTimeout(() => {
      setReportData(mockReportData);
      setLoading(false);
    }, 1000);
  }, []);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotalSales = () => {
    if (!reportData) return 0;
    return reportData.salesByCategory.reduce((sum, item) => sum + item.sales, 0);
  };

  const calculateGrowthRate = () => {
    if (!reportData || reportData.salesByMonth.length < 2) return 0;
    const current = reportData.salesByMonth[reportData.salesByMonth.length - 1].sales;
    const previous = reportData.salesByMonth[reportData.salesByMonth.length - 2].sales;
    return ((current - previous) / previous * 100);
  };

  const handleExportReport = () => {
    // Export functionality would be implemented here
    console.log('Exporting report...');
  };

  const handlePrintReport = () => {
    // Print functionality would be implemented here
    window.print();
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Typography variant="h6">جاري تحميل التقارير...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <DashboardHeader
        title="التقارير والتحليلات"
        subtitle="تقارير مفصلة عن أداء المتجر والمبيعات"
        showBreadcrumbs={true}
        showLastUpdate={true}
      />

      {/* Report Controls */}
      <Card sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>نوع التقرير</InputLabel>
              <Select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                label="نوع التقرير"
              >
                <MenuItem value="sales">تقرير المبيعات</MenuItem>
                <MenuItem value="products">تقرير المنتجات</MenuItem>
                <MenuItem value="customers">تقرير العملاء</MenuItem>
                <MenuItem value="inventory">تقرير المخزون</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>الفترة الزمنية</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                label="الفترة الزمنية"
              >
                <MenuItem value="week">هذا الأسبوع</MenuItem>
                <MenuItem value="month">هذا الشهر</MenuItem>
                <MenuItem value="quarter">هذا الربع</MenuItem>
                <MenuItem value="year">هذا العام</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" justifyContent="flex-end" gap={2}>
              <Button
                variant="outlined"
                startIcon={<PrintIcon />}
                onClick={handlePrintReport}
              >
                طباعة
              </Button>
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleExportReport}
              >
                تصدير
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Card>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="primary">
                    {formatCurrency(calculateTotalSales())}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المبيعات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <TrendingUpIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="success.main">
                    {reportData?.salesByMonth.reduce((sum, item) => sum + item.orders, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <ReportIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="info.main">
                    {calculateGrowthRate().toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل النمو
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  {calculateGrowthRate() >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {reportData?.topProducts.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    أفضل المنتجات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <ChartIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Sales by Month Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardHeader
              title="المبيعات الشهرية"
              avatar={
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <ChartIcon />
                </Avatar>
              }
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={reportData?.salesByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Bar dataKey="sales" fill="#1976d2" name="المبيعات" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Sales by Category Pie Chart */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardHeader
              title="المبيعات حسب الفئة"
              avatar={
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <PieChartIcon />
                </Avatar>
              }
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={reportData?.salesByCategory}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ percentage }) => `${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="sales"
                  >
                    {reportData?.salesByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sales Trend Chart */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="اتجاه المبيعات والأرباح"
              avatar={
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TimelineIcon />
                </Avatar>
              }
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={reportData?.salesTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Line type="monotone" dataKey="sales" stroke="#1976d2" name="المبيعات" />
                  <Line type="monotone" dataKey="profit" stroke="#2e7d32" name="الأرباح" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Products Table */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="أفضل المنتجات مبيعاً"
              avatar={
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <TrendingUpIcon />
                </Avatar>
              }
            />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>المنتج</TableCell>
                      <TableCell>المبيعات</TableCell>
                      <TableCell>الكمية</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reportData?.topProducts.map((product, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Chip
                              label={index + 1}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            {product.name}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" color="success.main">
                            {formatCurrency(product.sales)}
                          </Typography>
                        </TableCell>
                        <TableCell>{product.quantity}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Sales by Category Table */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader
              title="المبيعات حسب الفئة"
              avatar={
                <Avatar sx={{ bgcolor: 'secondary.main' }}>
                  <PieChartIcon />
                </Avatar>
              }
            />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الفئة</TableCell>
                      <TableCell>المبيعات</TableCell>
                      <TableCell>النسبة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reportData?.salesByCategory.map((category, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                bgcolor: COLORS[index % COLORS.length],
                              }}
                            />
                            {category.category}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" color="success.main">
                            {formatCurrency(category.sales)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${category.percentage}%`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default Reports;
