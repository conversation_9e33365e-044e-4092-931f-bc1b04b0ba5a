import { getPickersLocalization } from './utils/getPickersLocalization';
// maps TimeView to its translation
const timeViews = {
  hours: '<PERSON>ra',
  minutes: 'Perc',
  seconds: '<PERSON><PERSON>odper<PERSON>',
  meridiem: '<PERSON><PERSON><PERSON><PERSON>'
};
const huHUPickers = {
  // Calendar navigation
  previousMonth: '<PERSON><PERSON><PERSON><PERSON> hónap',
  nextMonth: 'Következő hónap',
  // View navigation
  openPreviousView: '<PERSON>őző nézet megnyitása',
  openNextView: 'Következő nézet megnyitása',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'az évválasztó már nyitva, váltson a naptárnézetre' : 'a naptárnézet már nyitva, váltson az évválasztóra',
  // DateRange placeholders
  start: 'Kezd<PERSON> dátum',
  end: '<PERSON><PERSON><PERSON><PERSON> d<PERSON>',
  // Action bar
  cancelButtonLabel: '<PERSON>é<PERSON><PERSON>',
  clearButtonLabel: 'Törl<PERSON>',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Ma',
  // Toolbar titles
  datePickerToolbarTitle: 'Dátum kiválasztása',
  dateTimePickerToolbarTitle: 'Dátum és idő kiválasztása',
  timePickerToolbarTitle: 'Idő kiválasztása',
  dateRangePickerToolbarTitle: 'Dátumhatárok kiválasztása',
  // Clock labels
  clockLabelText: (view, time, adapter) => `${timeViews[view] ?? view} kiválasztása. ${time === null ? 'Nincs kiválasztva idő' : `A kiválasztott idő ${adapter.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} ${timeViews.hours.toLowerCase()}`,
  minutesClockNumberText: minutes => `${minutes} ${timeViews.minutes.toLowerCase()}`,
  secondsClockNumberText: seconds => `${seconds}  ${timeViews.seconds.toLowerCase()}`,
  // Digital clock labels
  selectViewText: view => `${timeViews[view]} kiválasztása`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Hét',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `${weekNumber}. hét`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Válasszon dátumot, a kiválasztott dátum: ${utils.format(value, 'fullDate')}` : 'Válasszon dátumot',
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Válasszon időt, a kiválasztott idő: ${utils.format(value, 'fullTime')}` : 'Válasszon időt',
  fieldClearLabel: 'Tartalom ürítése',
  // Table labels
  timeTableLabel: 'válasszon időt',
  dateTableLabel: 'válasszon dátumot',
  // Field section placeholders
  fieldYearPlaceholder: params => 'É'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'HHHH' : 'HH',
  fieldDayPlaceholder: () => 'NN',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => 'óó',
  fieldMinutesPlaceholder: () => 'pp',
  fieldSecondsPlaceholder: () => 'mm',
  fieldMeridiemPlaceholder: () => 'dd'
};
export const huHU = getPickersLocalization(huHUPickers);