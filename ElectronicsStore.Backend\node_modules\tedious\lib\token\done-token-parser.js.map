{"version": 3, "file": "done-token-parser.js", "names": ["_token", "require", "_helpers", "STATUS", "MORE", "ERROR", "INXACT", "COUNT", "ATTN", "SRVERROR", "readToken", "buf", "offset", "options", "status", "value", "readUInt16LE", "more", "sqlError", "rowCount<PERSON><PERSON><PERSON>", "attention", "serverError", "curCmd", "rowCount", "tdsVersion", "readUInt32LE", "readBigUInt64LE", "Result", "Number", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "DoneToken", "doneInProcParser", "DoneInProcToken", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DoneProcToken"], "sources": ["../../src/token/done-token-parser.ts"], "sourcesContent": ["import { type ParserOptions } from './stream-parser';\nimport { <PERSON>Token, DoneInProcToken, DoneProcToken } from './token';\nimport { Result, readBigUInt64LE, readUInt16LE, readUInt32LE } from './helpers';\n\n// s2.2.7.5/6/7\n\nconst STATUS = {\n  MORE: 0x0001,\n  ERROR: 0x0002,\n  // This bit is not yet in use by SQL Server, so is not exposed in the returned token\n  INXACT: 0x0004,\n  COUNT: 0x0010,\n  ATTN: 0x0020,\n  SRVERROR: 0x0100\n};\n\ninterface TokenData {\n  more: boolean;\n  sqlError: boolean;\n  attention: boolean;\n  serverError: boolean;\n  rowCount: number | undefined;\n  curCmd: number;\n}\n\nfunction readToken(buf: Buffer, offset: number, options: ParserOptions): Result<TokenData> {\n  let status;\n  ({ offset, value: status } = readUInt16LE(buf, offset));\n\n  const more = !!(status & STATUS.MORE);\n  const sqlError = !!(status & STATUS.ERROR);\n  const rowCountValid = !!(status & STATUS.COUNT);\n  const attention = !!(status & STATUS.ATTN);\n  const serverError = !!(status & STATUS.SRVERROR);\n\n  let curCmd;\n  ({ offset, value: curCmd } = readUInt16LE(buf, offset));\n\n  let rowCount;\n  ({ offset, value: rowCount } = (options.tdsVersion < '7_2' ? readUInt32LE : readBigUInt64LE)(buf, offset));\n\n  return new Result({\n    more: more,\n    sqlError: sqlError,\n    attention: attention,\n    serverError: serverError,\n    rowCount: rowCountValid ? Number(rowCount) : undefined,\n    curCmd: curCmd\n  }, offset);\n}\n\nexport function doneParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneToken> {\n  let value;\n  ({ offset, value } = readToken(buf, offset, options));\n  return new Result(new DoneToken(value), offset);\n}\n\nexport function doneInProcParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneInProcToken> {\n  let value;\n  ({ offset, value } = readToken(buf, offset, options));\n  return new Result(new DoneInProcToken(value), offset);\n}\n\nexport function doneProcParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneProcToken> {\n  let value;\n  ({ offset, value } = readToken(buf, offset, options));\n  return new Result(new DoneProcToken(value), offset);\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAEA;;AAEA,MAAME,MAAM,GAAG;EACbC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,MAAM;EACb;EACAC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC;AAWD,SAASC,SAASA,CAACC,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAAqB;EACzF,IAAIC,MAAM;EACV,CAAC;IAAEF,MAAM;IAAEG,KAAK,EAAED;EAAO,CAAC,GAAG,IAAAE,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAEtD,MAAMK,IAAI,GAAG,CAAC,EAAEH,MAAM,GAAGX,MAAM,CAACC,IAAI,CAAC;EACrC,MAAMc,QAAQ,GAAG,CAAC,EAAEJ,MAAM,GAAGX,MAAM,CAACE,KAAK,CAAC;EAC1C,MAAMc,aAAa,GAAG,CAAC,EAAEL,MAAM,GAAGX,MAAM,CAACI,KAAK,CAAC;EAC/C,MAAMa,SAAS,GAAG,CAAC,EAAEN,MAAM,GAAGX,MAAM,CAACK,IAAI,CAAC;EAC1C,MAAMa,WAAW,GAAG,CAAC,EAAEP,MAAM,GAAGX,MAAM,CAACM,QAAQ,CAAC;EAEhD,IAAIa,MAAM;EACV,CAAC;IAAEV,MAAM;IAAEG,KAAK,EAAEO;EAAO,CAAC,GAAG,IAAAN,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAEtD,IAAIW,QAAQ;EACZ,CAAC;IAAEX,MAAM;IAAEG,KAAK,EAAEQ;EAAS,CAAC,GAAG,CAACV,OAAO,CAACW,UAAU,GAAG,KAAK,GAAGC,qBAAY,GAAGC,wBAAe,EAAEf,GAAG,EAAEC,MAAM,CAAC;EAEzG,OAAO,IAAIe,eAAM,CAAC;IAChBV,IAAI,EAAEA,IAAI;IACVC,QAAQ,EAAEA,QAAQ;IAClBE,SAAS,EAAEA,SAAS;IACpBC,WAAW,EAAEA,WAAW;IACxBE,QAAQ,EAAEJ,aAAa,GAAGS,MAAM,CAACL,QAAQ,CAAC,GAAGM,SAAS;IACtDP,MAAM,EAAEA;EACV,CAAC,EAAEV,MAAM,CAAC;AACZ;AAEO,SAASkB,UAAUA,CAACnB,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAAqB;EACjG,IAAIE,KAAK;EACT,CAAC;IAAEH,MAAM;IAAEG;EAAM,CAAC,GAAGL,SAAS,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;EACpD,OAAO,IAAIc,eAAM,CAAC,IAAII,gBAAS,CAAChB,KAAK,CAAC,EAAEH,MAAM,CAAC;AACjD;AAEO,SAASoB,gBAAgBA,CAACrB,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAA2B;EAC7G,IAAIE,KAAK;EACT,CAAC;IAAEH,MAAM;IAAEG;EAAM,CAAC,GAAGL,SAAS,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;EACpD,OAAO,IAAIc,eAAM,CAAC,IAAIM,sBAAe,CAAClB,KAAK,CAAC,EAAEH,MAAM,CAAC;AACvD;AAEO,SAASsB,cAAcA,CAACvB,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAAyB;EACzG,IAAIE,KAAK;EACT,CAAC;IAAEH,MAAM;IAAEG;EAAM,CAAC,GAAGL,SAAS,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;EACpD,OAAO,IAAIc,eAAM,CAAC,IAAIQ,oBAAa,CAACpB,KAAK,CAAC,EAAEH,MAAM,CAAC;AACrD"}