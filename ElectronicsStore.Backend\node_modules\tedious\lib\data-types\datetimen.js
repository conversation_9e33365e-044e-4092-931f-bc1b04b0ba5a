"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const DateTimeN = {
  id: 0x6F,
  type: 'DATETIMN',
  name: 'DateTimeN',
  declaration() {
    throw new Error('not implemented');
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = DateTimeN;
exports.default = _default;
module.exports = DateTimeN;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJEYXRlVGltZU4iLCJpZCIsInR5cGUiLCJuYW1lIiwiZGVjbGFyYXRpb24iLCJFcnJvciIsImdlbmVyYXRlVHlwZUluZm8iLCJnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCIsImdlbmVyYXRlUGFyYW1ldGVyRGF0YSIsInZhbGlkYXRlIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwiZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9kYXRhLXR5cGVzL2RhdGV0aW1lbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIERhdGFUeXBlIH0gZnJvbSAnLi4vZGF0YS10eXBlJztcblxuY29uc3QgRGF0ZVRpbWVOOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4NkYsXG4gIHR5cGU6ICdEQVRFVElNTicsXG4gIG5hbWU6ICdEYXRlVGltZU4nLFxuXG4gIGRlY2xhcmF0aW9uKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVUeXBlSW5mbygpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVQYXJhbWV0ZXJEYXRhKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgdmFsaWRhdGUoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgRGF0ZVRpbWVOO1xubW9kdWxlLmV4cG9ydHMgPSBEYXRlVGltZU47XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUVBLE1BQU1BLFNBQW1CLEdBQUc7RUFDMUJDLEVBQUUsRUFBRSxJQUFJO0VBQ1JDLElBQUksRUFBRSxVQUFVO0VBQ2hCQyxJQUFJLEVBQUUsV0FBVztFQUVqQkMsV0FBV0EsQ0FBQSxFQUFHO0lBQ1osTUFBTSxJQUFJQyxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVEQyxnQkFBZ0JBLENBQUEsRUFBRztJQUNqQixNQUFNLElBQUlELEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURFLHVCQUF1QkEsQ0FBQSxFQUFHO0lBQ3hCLE1BQU0sSUFBSUYsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREcscUJBQXFCQSxDQUFBLEVBQUc7SUFDdEIsTUFBTSxJQUFJSCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVESSxRQUFRQSxDQUFBLEVBQUc7SUFDVCxNQUFNLElBQUlKLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQztBQUNGLENBQUM7QUFBQyxJQUFBSyxRQUFBLEdBRWFWLFNBQVM7QUFBQVcsT0FBQSxDQUFBQyxPQUFBLEdBQUFGLFFBQUE7QUFDeEJHLE1BQU0sQ0FBQ0YsT0FBTyxHQUFHWCxTQUFTIn0=