{"version": 3, "file": "tvp.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "obj", "__esModule", "default", "TVP_ROW_TOKEN", "<PERSON><PERSON><PERSON>", "from", "TVP_END_TOKEN", "NULL_LENGTH", "TVP", "id", "type", "name", "declaration", "parameter", "value", "generateTypeInfo", "_parameter$value", "_parameter$value2", "databaseName", "schema", "typeName", "bufferLength", "byteLength", "buffer", "WritableTrackingBuffer", "writeUInt8", "writeBVarchar", "data", "generateParameterLength", "options", "columns", "alloc", "writeUInt16LE", "length", "generateParameterData", "rows", "i", "len", "column", "buff", "writeUInt32LE", "row", "k", "len2", "param", "validate", "collation", "scale", "precision", "TypeError", "Array", "isArray", "_default", "exports", "module"], "sources": ["../../src/data-types/tvp.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst TVP_ROW_TOKEN = Buffer.from([0x01]);\nconst TVP_END_TOKEN = Buffer.from([0x00]);\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\n\nconst TVP: DataType = {\n  id: 0xF3,\n  type: 'TVPTYPE',\n  name: 'TVP',\n\n  declaration: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n    return value.name + ' readonly';\n  },\n\n  generateTypeInfo(parameter) {\n    const databaseName = '';\n    const schema = parameter.value?.schema ?? '';\n    const typeName = parameter.value?.name ?? '';\n\n    const bufferLength = 1 +\n      1 + Buffer.byteLength(databaseName, 'ucs2') +\n      1 + Buffer.byteLength(schema, 'ucs2') +\n      1 + Buffer.byteLength(typeName, 'ucs2');\n\n    const buffer = new WritableTrackingBuffer(bufferLength, 'ucs2');\n    buffer.writeUInt8(this.id);\n    buffer.writeBVarchar(databaseName);\n    buffer.writeBVarchar(schema);\n    buffer.writeBVarchar(typeName);\n\n    return buffer.data;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const { columns } = parameter.value;\n    const buffer = Buffer.alloc(2);\n    buffer.writeUInt16LE(columns.length, 0);\n    return buffer;\n  },\n\n  *generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      yield TVP_END_TOKEN;\n      yield TVP_END_TOKEN;\n      return;\n    }\n\n    const { columns, rows } = parameter.value;\n\n    for (let i = 0, len = columns.length; i < len; i++) {\n      const column = columns[i];\n\n      const buff = Buffer.alloc(6);\n      // UserType\n      buff.writeUInt32LE(0x00000000, 0);\n\n      // Flags\n      buff.writeUInt16LE(0x0000, 4);\n      yield buff;\n\n      // TYPE_INFO\n      yield column.type.generateTypeInfo(column);\n\n      // ColName\n      yield Buffer.from([0x00]);\n    }\n\n    yield TVP_END_TOKEN;\n\n    for (let i = 0, length = rows.length; i < length; i++) {\n      yield TVP_ROW_TOKEN;\n\n      const row = rows[i];\n      for (let k = 0, len2 = row.length; k < len2; k++) {\n        const column = columns[k];\n        const value = row[k];\n\n        const param = {\n          value: column.type.validate(value, parameter.collation),\n          length: column.length,\n          scale: column.scale,\n          precision: column.precision\n        };\n\n        // TvpColumnData\n        yield column.type.generateParameterLength(param, options);\n        yield * column.type.generateParameterData(param, options);\n      }\n    }\n\n    yield TVP_END_TOKEN;\n  },\n\n  validate: function(value): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'object') {\n      throw new TypeError('Invalid table.');\n    }\n\n    if (!Array.isArray(value.columns)) {\n      throw new TypeError('Invalid table.');\n    }\n\n    if (!Array.isArray(value.rows)) {\n      throw new TypeError('Invalid table.');\n    }\n\n    return value;\n  }\n};\n\nexport default TVP;\nmodule.exports = TVP;\n"], "mappings": ";;;;;;AACA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,aAAa,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACzC,MAAMC,aAAa,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEzC,MAAME,WAAW,GAAGH,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE7C,MAAMG,GAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,KAAK;EAEXC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;IACtC,OAAOA,KAAK,CAACH,IAAI,GAAG,WAAW;EACjC,CAAC;EAEDI,gBAAgBA,CAACF,SAAS,EAAE;IAAA,IAAAG,gBAAA,EAAAC,iBAAA;IAC1B,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMC,MAAM,GAAG,EAAAH,gBAAA,GAAAH,SAAS,CAACC,KAAK,cAAAE,gBAAA,uBAAfA,gBAAA,CAAiBG,MAAM,KAAI,EAAE;IAC5C,MAAMC,QAAQ,GAAG,EAAAH,iBAAA,GAAAJ,SAAS,CAACC,KAAK,cAAAG,iBAAA,uBAAfA,iBAAA,CAAiBN,IAAI,KAAI,EAAE;IAE5C,MAAMU,YAAY,GAAG,CAAC,GACpB,CAAC,GAAGjB,MAAM,CAACkB,UAAU,CAACJ,YAAY,EAAE,MAAM,CAAC,GAC3C,CAAC,GAAGd,MAAM,CAACkB,UAAU,CAACH,MAAM,EAAE,MAAM,CAAC,GACrC,CAAC,GAAGf,MAAM,CAACkB,UAAU,CAACF,QAAQ,EAAE,MAAM,CAAC;IAEzC,MAAMG,MAAM,GAAG,IAAIC,+BAAsB,CAACH,YAAY,EAAE,MAAM,CAAC;IAC/DE,MAAM,CAACE,UAAU,CAAC,IAAI,CAAChB,EAAE,CAAC;IAC1Bc,MAAM,CAACG,aAAa,CAACR,YAAY,CAAC;IAClCK,MAAM,CAACG,aAAa,CAACP,MAAM,CAAC;IAC5BI,MAAM,CAACG,aAAa,CAACN,QAAQ,CAAC;IAE9B,OAAOG,MAAM,CAACI,IAAI;EACpB,CAAC;EAEDC,uBAAuBA,CAACf,SAAS,EAAEgB,OAAO,EAAE;IAC1C,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOP,WAAW;IACpB;IAEA,MAAM;MAAEuB;IAAQ,CAAC,GAAGjB,SAAS,CAACC,KAAK;IACnC,MAAMS,MAAM,GAAGnB,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC;IAC9BR,MAAM,CAACS,aAAa,CAACF,OAAO,CAACG,MAAM,EAAE,CAAC,CAAC;IACvC,OAAOV,MAAM;EACf,CAAC;EAED,CAACW,qBAAqBA,CAACrB,SAAS,EAAEgB,OAAO,EAAE;IACzC,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,MAAMR,aAAa;MACnB,MAAMA,aAAa;MACnB;IACF;IAEA,MAAM;MAAEwB,OAAO;MAAEK;IAAK,CAAC,GAAGtB,SAAS,CAACC,KAAK;IAEzC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGP,OAAO,CAACG,MAAM,EAAEG,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,MAAME,MAAM,GAAGR,OAAO,CAACM,CAAC,CAAC;MAEzB,MAAMG,IAAI,GAAGnC,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC;MAC5B;MACAQ,IAAI,CAACC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;;MAEjC;MACAD,IAAI,CAACP,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;MAC7B,MAAMO,IAAI;;MAEV;MACA,MAAMD,MAAM,CAAC5B,IAAI,CAACK,gBAAgB,CAACuB,MAAM,CAAC;;MAE1C;MACA,MAAMlC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B;IAEA,MAAMC,aAAa;IAEnB,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEH,MAAM,GAAGE,IAAI,CAACF,MAAM,EAAEG,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MACrD,MAAMjC,aAAa;MAEnB,MAAMsC,GAAG,GAAGN,IAAI,CAACC,CAAC,CAAC;MACnB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGF,GAAG,CAACR,MAAM,EAAES,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;QAChD,MAAMJ,MAAM,GAAGR,OAAO,CAACY,CAAC,CAAC;QACzB,MAAM5B,KAAK,GAAG2B,GAAG,CAACC,CAAC,CAAC;QAEpB,MAAME,KAAK,GAAG;UACZ9B,KAAK,EAAEwB,MAAM,CAAC5B,IAAI,CAACmC,QAAQ,CAAC/B,KAAK,EAAED,SAAS,CAACiC,SAAS,CAAC;UACvDb,MAAM,EAAEK,MAAM,CAACL,MAAM;UACrBc,KAAK,EAAET,MAAM,CAACS,KAAK;UACnBC,SAAS,EAAEV,MAAM,CAACU;QACpB,CAAC;;QAED;QACA,MAAMV,MAAM,CAAC5B,IAAI,CAACkB,uBAAuB,CAACgB,KAAK,EAAEf,OAAO,CAAC;QACzD,OAAQS,MAAM,CAAC5B,IAAI,CAACwB,qBAAqB,CAACU,KAAK,EAAEf,OAAO,CAAC;MAC3D;IACF;IAEA,MAAMvB,aAAa;EACrB,CAAC;EAEDuC,QAAQ,EAAE,SAAAA,CAAS/B,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAImC,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACrC,KAAK,CAACgB,OAAO,CAAC,EAAE;MACjC,MAAM,IAAImB,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACrC,KAAK,CAACqB,IAAI,CAAC,EAAE;MAC9B,MAAM,IAAIc,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,OAAOnC,KAAK;EACd;AACF,CAAC;AAAC,IAAAsC,QAAA,GAEa5C,GAAG;AAAA6C,OAAA,CAAAnD,OAAA,GAAAkD,QAAA;AAClBE,MAAM,CAACD,OAAO,GAAG7C,GAAG"}