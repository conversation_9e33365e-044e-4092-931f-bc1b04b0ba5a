const express = require('express');
const router = express.Router();
const { runQuery, getRow, getAllRows } = require('../config/database');

// GET /api/customers - Get all customers
router.get('/', async (req, res) => {
  try {
    const { status, search, page = 1, limit = 50 } = req.query;
    
    let sql = 'SELECT * FROM customers WHERE 1=1';
    let params = [];
    
    if (status && status !== 'all') {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    if (search) {
      sql += ' AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    // Add pagination
    const offset = (page - 1) * limit;
    sql += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const customers = await getAllRows(sql, params);
    
    // Get total count for pagination
    let countSql = 'SELECT COUNT(*) as total FROM customers WHERE 1=1';
    let countParams = [];
    
    if (status && status !== 'all') {
      countSql += ' AND status = ?';
      countParams.push(status);
    }
    
    if (search) {
      countSql += ' AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const { total } = await getRow(countSql, countParams);
    
    res.json({
      success: true,
      data: customers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب العملاء'
    });
  }
});

// GET /api/customers/:id - Get single customer
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const customer = await getRow('SELECT * FROM customers WHERE id = ?', [id]);
    
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'العميل غير موجود'
      });
    }
    
    // Get customer's recent orders
    const recentOrders = await getAllRows(`
      SELECT * FROM sales 
      WHERE customer_id = ? 
      ORDER BY sale_date DESC 
      LIMIT 10
    `, [id]);
    
    res.json({
      success: true,
      data: {
        ...customer,
        recentOrders
      }
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب العميل'
    });
  }
});

// POST /api/customers - Create new customer
router.post('/', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      address,
      city,
      postal_code,
      customer_type,
      business_name,
      tax_number,
      status,
      credit_limit,
      notes,
      birth_date,
      join_date
    } = req.body;
    
    // Validation
    if (!name || !phone || !city) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة مفقودة'
      });
    }
    
    // Check if phone already exists
    const existingCustomer = await getRow('SELECT id FROM customers WHERE phone = ?', [phone]);
    if (existingCustomer) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف موجود مسبقاً'
      });
    }
    
    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await getRow('SELECT id FROM customers WHERE email = ?', [email]);
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: 'البريد الإلكتروني موجود مسبقاً'
        });
      }
    }
    
    const result = await runQuery(`
      INSERT INTO customers (
        name, email, phone, address, city, postal_code,
        customer_type, business_name, tax_number, status,
        credit_limit, notes, birth_date, join_date, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [
      name, email, phone, address, city, postal_code,
      customer_type || 'individual', business_name, tax_number,
      status || 'active', parseFloat(credit_limit) || 0,
      notes, birth_date, join_date || new Date().toISOString().split('T')[0]
    ]);
    
    const newCustomer = await getRow('SELECT * FROM customers WHERE id = ?', [result.id]);
    
    res.status(201).json({
      success: true,
      message: 'تم إضافة العميل بنجاح',
      data: newCustomer
    });
  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة العميل'
    });
  }
});

// PUT /api/customers/:id - Update customer
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      address,
      city,
      postal_code,
      customer_type,
      business_name,
      tax_number,
      status,
      credit_limit,
      notes,
      birth_date
    } = req.body;
    
    // Check if customer exists
    const existingCustomer = await getRow('SELECT * FROM customers WHERE id = ?', [id]);
    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        message: 'العميل غير موجود'
      });
    }
    
    // Check if phone is unique (excluding current customer)
    const duplicatePhone = await getRow(
      'SELECT id FROM customers WHERE phone = ? AND id != ?',
      [phone, id]
    );
    if (duplicatePhone) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف موجود مسبقاً'
      });
    }
    
    // Check if email is unique (excluding current customer)
    if (email) {
      const duplicateEmail = await getRow(
        'SELECT id FROM customers WHERE email = ? AND id != ?',
        [email, id]
      );
      if (duplicateEmail) {
        return res.status(400).json({
          success: false,
          message: 'البريد الإلكتروني موجود مسبقاً'
        });
      }
    }
    
    await runQuery(`
      UPDATE customers SET
        name = ?, email = ?, phone = ?, address = ?, city = ?,
        postal_code = ?, customer_type = ?, business_name = ?,
        tax_number = ?, status = ?, credit_limit = ?, notes = ?,
        birth_date = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      name, email, phone, address, city, postal_code,
      customer_type, business_name, tax_number, status,
      parseFloat(credit_limit) || 0, notes, birth_date, id
    ]);
    
    const updatedCustomer = await getRow('SELECT * FROM customers WHERE id = ?', [id]);
    
    res.json({
      success: true,
      message: 'تم تحديث العميل بنجاح',
      data: updatedCustomer
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث العميل'
    });
  }
});

// DELETE /api/customers/:id - Delete customer
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if customer exists
    const customer = await getRow('SELECT * FROM customers WHERE id = ?', [id]);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'العميل غير موجود'
      });
    }
    
    // Check if customer has orders
    const hasOrders = await getRow('SELECT COUNT(*) as count FROM sales WHERE customer_id = ?', [id]);
    if (hasOrders.count > 0) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف العميل لوجود طلبات مرتبطة به'
      });
    }
    
    await runQuery('DELETE FROM customers WHERE id = ?', [id]);
    
    res.json({
      success: true,
      message: 'تم حذف العميل بنجاح'
    });
  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف العميل'
    });
  }
});

// GET /api/customers/reports/stats - Get customer statistics
router.get('/reports/stats', async (req, res) => {
  try {
    const stats = await getRow(`
      SELECT 
        COUNT(*) as total_customers,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_customers,
        COUNT(CASE WHEN status = 'vip' THEN 1 END) as vip_customers,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_customers,
        AVG(total_spent) as avg_spent,
        SUM(total_spent) as total_revenue
      FROM customers
    `);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching customer stats:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات العملاء'
    });
  }
});

module.exports = router;
