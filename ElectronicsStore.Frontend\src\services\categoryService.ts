import { apiService } from './apiClient';

// Category types
export interface Category {
  id: number;
  name: string;
  productCount?: number;
}

export interface CategoryFormData {
  name: string;
}

// Category API endpoints
const ENDPOINTS = {
  CATEGORIES: '/categories',
  CATEGORY_BY_ID: (id: number) => `/categories/${id}`,
};

// Category Service
export const categoryService = {
  // Get all categories
  getCategories: async (): Promise<Category[]> => {
    return await apiService.get<Category[]>(ENDPOINTS.CATEGORIES);
  },

  // Get category by ID
  getCategoryById: async (id: number): Promise<Category> => {
    return await apiService.get<Category>(ENDPOINTS.CATEGORY_BY_ID(id));
  },

  // Create new category
  createCategory: async (categoryData: CategoryFormData): Promise<Category> => {
    return await apiService.post<Category>(ENDPOINTS.CATEGORIES, categoryData);
  },

  // Update existing category
  updateCategory: async (id: number, categoryData: CategoryFormData): Promise<Category> => {
    return await apiService.put<Category>(ENDPOINTS.CATEGORY_BY_ID(id), categoryData);
  },

  // Delete category
  deleteCategory: async (id: number): Promise<void> => {
    return await apiService.delete<void>(ENDPOINTS.CATEGORY_BY_ID(id));
  },
};

export default categoryService;
