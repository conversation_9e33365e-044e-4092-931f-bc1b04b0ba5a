"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _guidParser = require("../guid-parser");
const NULL_LENGTH = Buffer.from([0x00]);
const DATA_LENGTH = Buffer.from([0x10]);
const UniqueIdentifier = {
  id: 0x24,
  type: 'GUIDN',
  name: 'UniqueIdentifier',
  declaration: function () {
    return 'uniqueidentifier';
  },
  resolveLength: function () {
    return 16;
  },
  generateTypeInfo() {
    return Buffer.from([this.id, 0x10]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  generateParameterData: function* (parameter, options) {
    if (parameter.value == null) {
      return;
    }
    yield Buffer.from((0, _guidParser.guidToArray)(parameter.value));
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'string') {
      throw new TypeError('Invalid string.');
    }
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
      throw new TypeError('Invalid GUID.');
    }
    return value;
  }
};
var _default = UniqueIdentifier;
exports.default = _default;
module.exports = UniqueIdentifier;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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