{"version": 3, "file": "connection.js", "names": ["_crypto", "_interopRequireDefault", "require", "_os", "tls", "_interopRequireWildcard", "net", "_dns", "_constants", "_stream", "_identity", "_bulkLoad", "_debug", "_events", "_instanceLookup", "_transientErrorLookup", "_packet", "_preloginPayload", "_login7Payload", "_ntlmPayload", "_request", "_rpcrequestPayload", "_sqlbatchPayload", "_messageIo", "_tokenStreamParser", "_transaction", "_errors", "_connector", "_library", "_tdsVersions", "_message", "_ntlm", "_nodeAbortController", "_dataType", "_bulkLoadPayload", "_specialStoredProcedure", "_esAggregateError", "_package", "_url", "_handler", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "KEEP_ALIVE_INITIAL_DELAY", "DEFAULT_CONNECT_TIMEOUT", "DEFAULT_CLIENT_REQUEST_TIMEOUT", "DEFAULT_CANCEL_TIMEOUT", "DEFAULT_CONNECT_RETRY_INTERVAL", "DEFAULT_PACKET_SIZE", "DEFAULT_TEXTSIZE", "DEFAULT_DATEFIRST", "DEFAULT_PORT", "DEFAULT_TDS_VERSION", "DEFAULT_LANGUAGE", "DEFAULT_DATEFORMAT", "CLEANUP_TYPE", "NORMAL", "REDIRECT", "RETRY", "Connection", "EventEmitter", "_cancelAfterRequestSent", "constructor", "config", "TypeError", "server", "fedAuthRequired", "authentication", "undefined", "type", "options", "domain", "userName", "password", "toUpperCase", "clientId", "tenantId", "token", "clientSecret", "abortTransactionOnError", "appName", "camelCaseColumns", "cancelTimeout", "columnEncryptionKeyCacheTTL", "columnEncryptionSetting", "columnNameReplacer", "connectionRetryInterval", "connectTimeout", "connector", "connectionIsolationLevel", "ISOLATION_LEVEL", "READ_COMMITTED", "cryptoCredentialsDetails", "database", "datefirst", "dateFormat", "debug", "data", "packet", "payload", "enableAnsiNull", "enableAnsiNullDefault", "enableAnsiPadding", "enableAnsiWarnings", "enableArithAbort", "enableConcatNullYieldsNull", "enableCursorCloseOnCommit", "enableImplicitTransactions", "enableNumericRoundabort", "enableQuotedIdentifier", "encrypt", "fallbackToDefaultDb", "encryptionKeyStoreProviders", "instanceName", "isolationLevel", "language", "localAddress", "maxRetriesOnTransientErrors", "multiSubnetFailover", "packetSize", "port", "readOnlyIntent", "requestTimeout", "rowCollectionOnDone", "rowCollectionOnRequestCompletion", "serverName", "serverSupportsColumnEncryption", "tdsVersion", "textsize", "trustedServerNameAE", "trustServerCertificate", "useColumnNames", "useUTC", "workstationId", "lowerCaseGuids", "Error", "assertValidIsolationLevel", "RangeError", "secureContextOptions", "secureOptions", "create", "value", "constants", "SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS", "createDebug", "inTransaction", "transactionDescriptors", "<PERSON><PERSON><PERSON>", "from", "transactionDepth", "isSqlBatch", "closed", "messageBuffer", "alloc", "curTransient<PERSON><PERSON>ry<PERSON>ount", "transientErrorLookup", "TransientErrorLookup", "state", "STATE", "INITIALIZED", "messageIo", "sendMessage", "TYPE", "ATTENTION", "createCancelTimer", "connect", "connectListener", "ConnectionError", "name", "onConnect", "err", "removeListener", "onError", "once", "transitionTo", "CONNECTING", "on", "event", "listener", "emit", "args", "close", "FINAL", "initialiseConnection", "signal", "createConnectTimer", "connectOnPort", "instanceLookup", "timeout", "then", "process", "nextTick", "clearConnectTimer", "aborted", "message", "cleanupConnection", "cleanupType", "clearRequestTimer", "clearRetryTimer", "closeConnection", "request", "RequestError", "callback", "loginError", "Debug", "createTokenStreamParser", "handler", "TokenStreamParser", "socketHandlingForSendPreLogin", "socket", "error", "socketError", "socketClose", "socketEnd", "setKeepAlive", "MessageIO", "cleartext", "log", "sendPreLogin", "SENT_PRELOGIN", "wrapWithTls", "throwIfAborted", "Promise", "resolve", "reject", "secureContext", "createSecureContext", "isIP", "encryptOptions", "host", "ALPNProtocols", "servername", "encryptsocket", "onAbort", "destroy", "reason", "removeEventListener", "addEventListener", "customConnector", "connectOpts", "routingData", "connectInParallel", "connectInSequence", "dns", "lookup", "end", "catch", "controller", "AbortController", "connectTimer", "setTimeout", "abort", "clearCancelTimer", "cancelTimer", "createRequestTimer", "requestTimer", "createRetryTimer", "retryTimer", "retryTimeout", "hostPostfix", "routingMessage", "dispatchEvent", "cancel", "clearTimeout", "newState", "exit", "enter", "apply", "getEventHandler", "eventName", "events", "SENT_TLSSSLNEGOTIATION", "code", "REROUTING", "TRANSIENT_FAILURE_RETRY", "major", "minor", "build", "exec", "version", "PreloginPayload", "Number", "subbuild", "PRELOGIN", "toString", "sendLogin7Packet", "Login7Payload", "versions", "clientProgVer", "clientPid", "pid", "connectionId", "clientTimeZone", "Date", "getTimezoneOffset", "clientLcid", "fedAuth", "echo", "workflow", "fedAuthToken", "sspi", "createNTLMRequest", "hostname", "os", "libraryName", "initDbFatal", "LOGIN7", "<PERSON><PERSON><PERSON><PERSON>", "sendFedAuthTokenMessage", "accessTokenLen", "byteLength", "offset", "writeUInt32LE", "write", "FEDAUTH_TOKEN", "SENT_LOGIN7_WITH_STANDARD_LOGIN", "sendInitialSql", "SqlBatchPayload", "getInitialSql", "currentTransactionDescriptor", "Message", "SQL_BATCH", "outgoingMessageStream", "Readable", "pipe", "push", "getIsolationLevelText", "join", "processedInitialSql", "execSqlBatch", "makeRequest", "sqlTextOrProcedure", "execSql", "validateParameters", "databaseCollation", "parameters", "TYPES", "NVarChar", "output", "length", "precision", "scale", "makeParamsParameter", "RPC_REQUEST", "RpcRequestPayload", "Procedures", "Sp_ExecuteSql", "newBulkLoad", "table", "callbackOrOptions", "BulkLoad", "execBulkLoad", "bulkLoad", "rows", "executionStarted", "streamingMode", "firstRow<PERSON><PERSON>ten", "rowStream", "rowToPacketTransform", "onCancel", "BulkLoadPayload", "Request", "getBulkInsertSql", "BULK_LOAD", "prepare", "Int", "preparing", "handle", "Sp_Prepare", "unprepare", "Sp_Unprepare", "execute", "executeParameters", "i", "len", "parameter", "validate", "Sp_Execute", "callProcedure", "beginTransaction", "transaction", "Transaction", "isolationLevelToTSQL", "TRANSACTION_MANAGER", "beginPayload", "commitTransaction", "commitPayload", "rollbackTransaction", "rollbackPayload", "saveTransaction", "savePayload", "cb", "useSavepoint", "crypto", "randomBytes", "txDone", "done", "LOGGED_IN", "txErr", "packetType", "canceled", "connection", "rowCount", "rst", "payloadStream", "unpipe", "ignore", "paused", "resume", "resetConnection", "resetConnectionOnNextRequest", "SENT_CLIENT_REQUEST", "reset", "READ_UNCOMMITTED", "REPEATABLE_READ", "SERIALIZABLE", "SNAPSHOT", "isTransientError", "AggregateError", "errors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_default", "exports", "module", "readMessage", "concat", "preloginPayload", "encryptionString", "_this$routingData", "startTls", "SENT_LOGIN7_WITH_FEDAUTH", "SENT_LOGIN7_WITH_NTLM", "reconnect", "retry", "Login7TokenHandler", "tokenStreamParser", "loginAckReceived", "LOGGED_IN_SENDING_INITIAL_SQL", "ntlmpacket", "NTLMResponsePayload", "NTLMAUTH_PKT", "fedAuthInfoToken", "<PERSON>url", "spn", "tokenScope", "URL", "credentials", "UsernamePasswordCredential", "msiArgs", "ManagedIdentityCredential", "managedIdentityClientId", "DefaultAzureCredential", "ClientSecretCredential", "tokenResponse", "getToken", "InitialSqlTokenHandler", "_this$request", "_this$request3", "_this$request10", "RequestTokenHandler", "SENT_ATTENTION", "onResume", "onPause", "_this$request2", "pause", "_this$request4", "_this$request5", "onEndOfMessage", "_this$request6", "_this$request7", "_this$request8", "_this$request9", "sqlRequest", "nextState", "AttentionTokenHandler", "attentionReceived"], "sources": ["../src/connection.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport os from 'os';\nimport * as tls from 'tls';\nimport * as net from 'net';\nimport dns from 'dns';\n\nimport constants from 'constants';\nimport { type SecureContextOptions } from 'tls';\n\nimport { Readable } from 'stream';\n\nimport {\n  DefaultAzureCredential,\n  ClientSecretCredential,\n  ManagedIdentityCredential,\n  UsernamePasswordCredential,\n} from '@azure/identity';\n\nimport BulkLoad, { type Options as BulkLoadOptions, type Callback as BulkLoadCallback } from './bulk-load';\nimport Debug from './debug';\nimport { EventEmitter, once } from 'events';\nimport { instanceLookup } from './instance-lookup';\nimport { TransientErrorLookup } from './transient-error-lookup';\nimport { TYPE } from './packet';\nimport PreloginPayload from './prelogin-payload';\nimport Login7Payload from './login7-payload';\nimport NTLMResponsePayload from './ntlm-payload';\nimport Request from './request';\nimport RpcRequestPayload from './rpcrequest-payload';\nimport SqlBatchPayload from './sqlbatch-payload';\nimport MessageIO from './message-io';\nimport { Parser as TokenStreamParser } from './token/token-stream-parser';\nimport { Transaction, ISOLATION_LEVEL, assertValidIsolationLevel } from './transaction';\nimport { ConnectionError, RequestError } from './errors';\nimport { connectInParallel, connectInSequence } from './connector';\nimport { name as libraryName } from './library';\nimport { versions } from './tds-versions';\nimport Message from './message';\nimport { type Metadata } from './metadata-parser';\nimport { createNTLMRequest } from './ntlm';\nimport { ColumnEncryptionAzureKeyVaultProvider } from './always-encrypted/keystore-provider-azure-key-vault';\n\nimport { AbortController, AbortSignal } from 'node-abort-controller';\nimport { type Parameter, TYPES } from './data-type';\nimport { BulkLoadPayload } from './bulk-load-payload';\nimport { Collation } from './collation';\nimport Procedures from './special-stored-procedure';\n\nimport AggregateError from 'es-aggregate-error';\nimport { version } from '../package.json';\nimport { URL } from 'url';\nimport { AttentionTokenHandler, InitialSqlTokenHandler, Login7TokenHandler, RequestTokenHandler, TokenHandler } from './token/handler';\n\ntype BeginTransactionCallback =\n  /**\n   * The callback is called when the request to start the transaction has completed,\n   * either successfully or with an error.\n   * If an error occurred then `err` will describe the error.\n   *\n   * As only one request at a time may be executed on a connection, another request should not\n   * be initiated until this callback is called.\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   * @param transactionDescriptor A Buffer that describe the transaction\n   */\n  (err: Error | null | undefined, transactionDescriptor?: Buffer) => void\n\ntype SaveTransactionCallback =\n  /**\n   * The callback is called when the request to set a savepoint within the\n   * transaction has completed, either successfully or with an error.\n   * If an error occurred then `err` will describe the error.\n   *\n   * As only one request at a time may be executed on a connection, another request should not\n   * be initiated until this callback is called.\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   */\n  (err: Error | null | undefined) => void;\n\ntype CommitTransactionCallback =\n  /**\n   * The callback is called when the request to commit the transaction has completed,\n   * either successfully or with an error.\n   * If an error occurred then `err` will describe the error.\n   *\n   * As only one request at a time may be executed on a connection, another request should not\n   * be initiated until this callback is called.\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   */\n  (err: Error | null | undefined) => void;\n\ntype RollbackTransactionCallback =\n  /**\n   * The callback is called when the request to rollback the transaction has\n   * completed, either successfully or with an error.\n   * If an error occurred then err will describe the error.\n   *\n   * As only one request at a time may be executed on a connection, another request should not\n   * be initiated until this callback is called.\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   */\n  (err: Error | null | undefined) => void;\n\ntype ResetCallback =\n  /**\n   * The callback is called when the connection reset has completed,\n   * either successfully or with an error.\n   *\n   * If an error occurred then `err` will describe the error.\n   *\n   * As only one request at a time may be executed on a connection, another\n   * request should not be initiated until this callback is called\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   */\n  (err: Error | null | undefined) => void;\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\ntype TransactionCallback<T extends (err: Error | null | undefined, ...args: any[]) => void> =\n  /**\n   * The callback is called when the request to start a transaction (or create a savepoint, in\n   * the case of a nested transaction) has completed, either successfully or with an error.\n   * If an error occurred, then `err` will describe the error.\n   * If no error occurred, the callback should perform its work and eventually call\n   * `done` with an error or null (to trigger a transaction rollback or a\n   * transaction commit) and an additional completion callback that will be called when the request\n   * to rollback or commit the current transaction has completed, either successfully or with an error.\n   * Additional arguments given to `done` will be passed through to this callback.\n   *\n   * As only one request at a time may be executed on a connection, another request should not\n   * be initiated until the completion callback is called.\n   *\n   * @param err If an error occurred, an [[Error]] object with details of the error.\n   * @param txDone If no error occurred, a function to be called to commit or rollback the transaction.\n   */\n  (err: Error | null | undefined, txDone?: TransactionDone<T>) => void;\n\ntype TransactionDoneCallback = (err: Error | null | undefined, ...args: any[]) => void;\ntype CallbackParameters<T extends (err: Error | null | undefined, ...args: any[]) => any> = T extends (err: Error | null | undefined, ...args: infer P) => any ? P : never;\n\ntype TransactionDone<T extends (err: Error | null | undefined, ...args: any[]) => void> =\n  /**\n   * If no error occurred, a function to be called to commit or rollback the transaction.\n   *\n   * @param err If an err occurred, a string with details of the error.\n   */\n  (err: Error | null | undefined, done: T, ...args: CallbackParameters<T>) => void;\n\n/**\n * @private\n */\nconst KEEP_ALIVE_INITIAL_DELAY = 30 * 1000;\n/**\n * @private\n */\nconst DEFAULT_CONNECT_TIMEOUT = 15 * 1000;\n/**\n * @private\n */\nconst DEFAULT_CLIENT_REQUEST_TIMEOUT = 15 * 1000;\n/**\n * @private\n */\nconst DEFAULT_CANCEL_TIMEOUT = 5 * 1000;\n/**\n * @private\n */\nconst DEFAULT_CONNECT_RETRY_INTERVAL = 500;\n/**\n * @private\n */\nconst DEFAULT_PACKET_SIZE = 4 * 1024;\n/**\n * @private\n */\nconst DEFAULT_TEXTSIZE = 2147483647;\n/**\n * @private\n */\nconst DEFAULT_DATEFIRST = 7;\n/**\n * @private\n */\nconst DEFAULT_PORT = 1433;\n/**\n * @private\n */\nconst DEFAULT_TDS_VERSION = '7_4';\n/**\n * @private\n */\nconst DEFAULT_LANGUAGE = 'us_english';\n/**\n * @private\n */\nconst DEFAULT_DATEFORMAT = 'mdy';\n\ninterface AzureActiveDirectoryMsiAppServiceAuthentication {\n  type: 'azure-active-directory-msi-app-service';\n  options: {\n    /**\n     * If you user want to connect to an Azure app service using a specific client account\n     * they need to provide `clientId` associate to their created identity.\n     *\n     * This is optional for retrieve token from azure web app service\n     */\n    clientId?: string;\n  };\n}\n\ninterface AzureActiveDirectoryMsiVmAuthentication {\n  type: 'azure-active-directory-msi-vm';\n  options: {\n    /**\n     * If you want to connect using a specific client account\n     * they need to provide `clientId` associated to their created identity.\n     *\n     * This is optional for retrieve a token\n     */\n    clientId?: string;\n  };\n}\n\ninterface AzureActiveDirectoryDefaultAuthentication {\n  type: 'azure-active-directory-default';\n  options: {\n    /**\n     * If you want to connect using a specific client account\n     * they need to provide `clientId` associated to their created identity.\n     *\n     * This is optional for retrieving a token\n     */\n    clientId?: string;\n  };\n}\n\n\ninterface AzureActiveDirectoryAccessTokenAuthentication {\n  type: 'azure-active-directory-access-token';\n  options: {\n    /**\n     * A user need to provide `token` which they retrieved else where\n     * to forming the connection.\n     */\n    token: string;\n  };\n}\n\ninterface AzureActiveDirectoryPasswordAuthentication {\n  type: 'azure-active-directory-password';\n  options: {\n    /**\n     * A user need to provide `userName` associate to their account.\n     */\n    userName: string;\n\n    /**\n     * A user need to provide `password` associate to their account.\n     */\n    password: string;\n\n    /**\n     * A client id to use.\n     */\n    clientId: string;\n\n    /**\n     * Optional parameter for specific Azure tenant ID\n     */\n    tenantId: string;\n  };\n}\n\ninterface AzureActiveDirectoryServicePrincipalSecret {\n  type: 'azure-active-directory-service-principal-secret';\n  options: {\n    /**\n     * Application (`client`) ID from your registered Azure application\n     */\n    clientId: string;\n    /**\n     * The created `client secret` for this registered Azure application\n     */\n    clientSecret: string;\n    /**\n     * Directory (`tenant`) ID from your registered Azure application\n     */\n    tenantId: string;\n  };\n}\n\ninterface NtlmAuthentication {\n  type: 'ntlm';\n  options: {\n    /**\n     * User name from your windows account.\n     */\n    userName: string;\n    /**\n     * Password from your windows account.\n     */\n    password: string;\n    /**\n     * Once you set domain for ntlm authentication type, driver will connect to SQL Server using domain login.\n     *\n     * This is necessary for forming a connection using ntlm type\n     */\n    domain: string;\n  };\n}\n\ninterface DefaultAuthentication {\n  type: 'default';\n  options: {\n    /**\n     * User name to use for sql server login.\n     */\n    userName?: string | undefined;\n    /**\n     * Password to use for sql server login.\n     */\n    password?: string | undefined;\n  };\n}\n\ninterface ErrorWithCode extends Error {\n  code?: string;\n}\n\ninterface InternalConnectionConfig {\n  server: string;\n  authentication: DefaultAuthentication | NtlmAuthentication | AzureActiveDirectoryPasswordAuthentication | AzureActiveDirectoryMsiAppServiceAuthentication | AzureActiveDirectoryMsiVmAuthentication | AzureActiveDirectoryAccessTokenAuthentication | AzureActiveDirectoryServicePrincipalSecret | AzureActiveDirectoryDefaultAuthentication;\n  options: InternalConnectionOptions;\n}\n\nexport interface InternalConnectionOptions {\n  abortTransactionOnError: boolean;\n  appName: undefined | string;\n  camelCaseColumns: boolean;\n  cancelTimeout: number;\n  columnEncryptionKeyCacheTTL: number;\n  columnEncryptionSetting: boolean;\n  columnNameReplacer: undefined | ((colName: string, index: number, metadata: Metadata) => string);\n  connectionRetryInterval: number;\n  connector: undefined | (() => Promise<net.Socket>);\n  connectTimeout: number;\n  connectionIsolationLevel: typeof ISOLATION_LEVEL[keyof typeof ISOLATION_LEVEL];\n  cryptoCredentialsDetails: SecureContextOptions;\n  database: undefined | string;\n  datefirst: number;\n  dateFormat: string;\n  debug: {\n    data: boolean;\n    packet: boolean;\n    payload: boolean;\n    token: boolean;\n  };\n  enableAnsiNull: null | boolean;\n  enableAnsiNullDefault: null | boolean;\n  enableAnsiPadding: null | boolean;\n  enableAnsiWarnings: null | boolean;\n  enableArithAbort: null | boolean;\n  enableConcatNullYieldsNull: null | boolean;\n  enableCursorCloseOnCommit: null | boolean;\n  enableImplicitTransactions: null | boolean;\n  enableNumericRoundabort: null | boolean;\n  enableQuotedIdentifier: null | boolean;\n  encrypt: string | boolean;\n  encryptionKeyStoreProviders: KeyStoreProviderMap | undefined;\n  fallbackToDefaultDb: boolean;\n  instanceName: undefined | string;\n  isolationLevel: typeof ISOLATION_LEVEL[keyof typeof ISOLATION_LEVEL];\n  language: string;\n  localAddress: undefined | string;\n  maxRetriesOnTransientErrors: number;\n  multiSubnetFailover: boolean;\n  packetSize: number;\n  port: undefined | number;\n  readOnlyIntent: boolean;\n  requestTimeout: number;\n  rowCollectionOnDone: boolean;\n  rowCollectionOnRequestCompletion: boolean;\n  serverName: undefined | string;\n  serverSupportsColumnEncryption: boolean;\n  tdsVersion: string;\n  textsize: number;\n  trustedServerNameAE: string | undefined;\n  trustServerCertificate: boolean;\n  useColumnNames: boolean;\n  useUTC: boolean;\n  workstationId: undefined | string;\n  lowerCaseGuids: boolean;\n}\n\ninterface KeyStoreProviderMap {\n  [key: string]: ColumnEncryptionAzureKeyVaultProvider;\n}\n\n/**\n * @private\n */\ninterface State {\n  name: string;\n  enter?(this: Connection): void;\n  exit?(this: Connection, newState: State): void;\n  events: {\n    socketError?(this: Connection, err: Error): void;\n    connectTimeout?(this: Connection): void;\n    message?(this: Connection, message: Message): void;\n    retry?(this: Connection): void;\n    reconnect?(this: Connection): void;\n  };\n}\n\ntype Authentication = DefaultAuthentication |\n  NtlmAuthentication |\n  AzureActiveDirectoryPasswordAuthentication |\n  AzureActiveDirectoryMsiAppServiceAuthentication |\n  AzureActiveDirectoryMsiVmAuthentication |\n  AzureActiveDirectoryAccessTokenAuthentication |\n  AzureActiveDirectoryServicePrincipalSecret |\n  AzureActiveDirectoryDefaultAuthentication;\n\ntype AuthenticationType = Authentication['type'];\n\nexport interface ConnectionConfiguration {\n  /**\n   * Hostname to connect to.\n   */\n  server: string;\n  /**\n   * Configuration options for forming the connection.\n   */\n  options?: ConnectionOptions;\n  /**\n   * Authentication related options for connection.\n   */\n  authentication?: AuthenticationOptions;\n}\n\ninterface DebugOptions {\n  /**\n   * A boolean, controlling whether [[debug]] events will be emitted with text describing packet data details\n   *\n   * (default: `false`)\n   */\n  data: boolean;\n  /**\n   * A boolean, controlling whether [[debug]] events will be emitted with text describing packet details\n   *\n   * (default: `false`)\n   */\n  packet: boolean;\n  /**\n   * A boolean, controlling whether [[debug]] events will be emitted with text describing packet payload details\n   *\n   * (default: `false`)\n   */\n  payload: boolean;\n  /**\n   * A boolean, controlling whether [[debug]] events will be emitted with text describing token stream tokens\n   *\n   * (default: `false`)\n   */\n  token: boolean;\n}\n\ninterface AuthenticationOptions {\n  /**\n   * Type of the authentication method, valid types are `default`, `ntlm`,\n   * `azure-active-directory-password`, `azure-active-directory-access-token`,\n   * `azure-active-directory-msi-vm`, `azure-active-directory-msi-app-service`,\n   * `azure-active-directory-default`\n   * or `azure-active-directory-service-principal-secret`\n   */\n  type?: AuthenticationType;\n  /**\n   * Different options for authentication types:\n   *\n   * * `default`: [[DefaultAuthentication.options]]\n   * * `ntlm` :[[NtlmAuthentication]]\n   * * `azure-active-directory-password` : [[AzureActiveDirectoryPasswordAuthentication.options]]\n   * * `azure-active-directory-access-token` : [[AzureActiveDirectoryAccessTokenAuthentication.options]]\n   * * `azure-active-directory-msi-vm` : [[AzureActiveDirectoryMsiVmAuthentication.options]]\n   * * `azure-active-directory-msi-app-service` : [[AzureActiveDirectoryMsiAppServiceAuthentication.options]]\n   * * `azure-active-directory-service-principal-secret` : [[AzureActiveDirectoryServicePrincipalSecret.options]]\n   * * `azure-active-directory-default` : [[AzureActiveDirectoryDefaultAuthentication.options]]\n   */\n  options?: any;\n}\n\nexport interface ConnectionOptions {\n  /**\n   * A boolean determining whether to rollback a transaction automatically if any error is encountered\n   * during the given transaction's execution. This sets the value for `SET XACT_ABORT` during the\n   * initial SQL phase of a connection [documentation](https://docs.microsoft.com/en-us/sql/t-sql/statements/set-xact-abort-transact-sql).\n   */\n  abortTransactionOnError?: boolean;\n\n  /**\n   * Application name used for identifying a specific application in profiling, logging or tracing tools of SQLServer.\n   *\n   * (default: `Tedious`)\n   */\n  appName?: string | undefined;\n\n  /**\n   * A boolean, controlling whether the column names returned will have the first letter converted to lower case\n   * (`true`) or not. This value is ignored if you provide a [[columnNameReplacer]].\n   *\n   * (default: `false`).\n   */\n  camelCaseColumns?: boolean;\n\n  /**\n   * The number of milliseconds before the [[Request.cancel]] (abort) of a request is considered failed\n   *\n   * (default: `5000`).\n   */\n  cancelTimeout?: number;\n\n  /**\n   * A function with parameters `(columnName, index, columnMetaData)` and returning a string. If provided,\n   * this will be called once per column per result-set. The returned value will be used instead of the SQL-provided\n   * column name on row and meta data objects. This allows you to dynamically convert between naming conventions.\n   *\n   * (default: `null`)\n   */\n  columnNameReplacer?: (colName: string, index: number, metadata: Metadata) => string;\n\n  /**\n   * Number of milliseconds before retrying to establish connection, in case of transient failure.\n   *\n   * (default:`500`)\n   */\n  connectionRetryInterval?: number;\n\n  /**\n   * Custom connector factory method.\n   *\n   * (default: `undefined`)\n   */\n  connector?: () => Promise<net.Socket>;\n\n  /**\n   * The number of milliseconds before the attempt to connect is considered failed\n   *\n   * (default: `15000`).\n   */\n  connectTimeout?: number;\n\n  /**\n   * The default isolation level for new connections. All out-of-transaction queries are executed with this setting.\n   *\n   * The isolation levels are available from `require('tedious').ISOLATION_LEVEL`.\n   * * `READ_UNCOMMITTED`\n   * * `READ_COMMITTED`\n   * * `REPEATABLE_READ`\n   * * `SERIALIZABLE`\n   * * `SNAPSHOT`\n   *\n   * (default: `READ_COMMITED`).\n   */\n  connectionIsolationLevel?: number;\n\n  /**\n   * When encryption is used, an object may be supplied that will be used\n   * for the first argument when calling [`tls.createSecurePair`](http://nodejs.org/docs/latest/api/tls.html#tls_tls_createsecurepair_credentials_isserver_requestcert_rejectunauthorized)\n   *\n   * (default: `{}`)\n   */\n  cryptoCredentialsDetails?: SecureContextOptions;\n\n  /**\n   * Database to connect to (default: dependent on server configuration).\n   */\n  database?: string | undefined;\n\n  /**\n   * Sets the first day of the week to a number from 1 through 7.\n   */\n  datefirst?: number;\n\n  /**\n   * A string representing position of month, day and year in temporal datatypes.\n   *\n   * (default: `mdy`)\n   */\n  dateFormat?: string;\n\n  debug?: DebugOptions;\n\n  /**\n   * A boolean, controls the way null values should be used during comparison operation.\n   *\n   * (default: `true`)\n   */\n  enableAnsiNull?: boolean;\n\n  /**\n   * If true, `SET ANSI_NULL_DFLT_ON ON` will be set in the initial sql. This means new columns will be\n   * nullable by default. See the [T-SQL documentation](https://msdn.microsoft.com/en-us/library/ms187375.aspx)\n   *\n   * (default: `true`).\n   */\n  enableAnsiNullDefault?: boolean;\n\n  /**\n   * A boolean, controls if padding should be applied for values shorter than the size of defined column.\n   *\n   * (default: `true`)\n   */\n  enableAnsiPadding?: boolean;\n\n  /**\n   * If true, SQL Server will follow ISO standard behavior during various error conditions. For details,\n   * see [documentation](https://docs.microsoft.com/en-us/sql/t-sql/statements/set-ansi-warnings-transact-sql)\n   *\n   * (default: `true`)\n   */\n  enableAnsiWarnings?: boolean;\n\n  /**\n   * Ends a query when an overflow or divide-by-zero error occurs during query execution.\n   * See [documentation](https://docs.microsoft.com/en-us/sql/t-sql/statements/set-arithabort-transact-sql?view=sql-server-2017)\n   * for more details.\n   *\n   * (default: `true`)\n   */\n  enableArithAbort?: boolean;\n\n  /**\n   * A boolean, determines if concatenation with NULL should result in NULL or empty string value, more details in\n   * [documentation](https://docs.microsoft.com/en-us/sql/t-sql/statements/set-concat-null-yields-null-transact-sql)\n   *\n   * (default: `true`)\n   */\n  enableConcatNullYieldsNull?: boolean;\n\n  /**\n   * A boolean, controls whether cursor should be closed, if the transaction opening it gets committed or rolled\n   * back.\n   *\n   * (default: `null`)\n   */\n  enableCursorCloseOnCommit?: boolean | null;\n\n  /**\n   * A boolean, sets the connection to either implicit or autocommit transaction mode.\n   *\n   * (default: `false`)\n   */\n  enableImplicitTransactions?: boolean;\n\n  /**\n   * If false, error is not generated during loss of precession.\n   *\n   * (default: `false`)\n   */\n  enableNumericRoundabort?: boolean;\n\n  /**\n   * If true, characters enclosed in single quotes are treated as literals and those enclosed double quotes are treated as identifiers.\n   *\n   * (default: `true`)\n   */\n  enableQuotedIdentifier?: boolean;\n\n  /**\n   * A string value that can be only set to 'strict', which indicates the usage TDS 8.0 protocol. Otherwise,\n   * a boolean determining whether or not the connection will be encrypted.\n   *\n   * (default: `true`)\n   */\n  encrypt?: string | boolean;\n\n  /**\n   * By default, if the database requested by [[database]] cannot be accessed,\n   * the connection will fail with an error. However, if [[fallbackToDefaultDb]] is\n   * set to `true`, then the user's default database will be used instead\n   *\n   * (default: `false`)\n   */\n  fallbackToDefaultDb?: boolean;\n\n  /**\n   * The instance name to connect to.\n   * The SQL Server Browser service must be running on the database server,\n   * and UDP port 1434 on the database server must be reachable.\n   *\n   * (no default)\n   *\n   * Mutually exclusive with [[port]].\n   */\n  instanceName?: string | undefined;\n\n  /**\n   * The default isolation level that transactions will be run with.\n   *\n   * The isolation levels are available from `require('tedious').ISOLATION_LEVEL`.\n   * * `READ_UNCOMMITTED`\n   * * `READ_COMMITTED`\n   * * `REPEATABLE_READ`\n   * * `SERIALIZABLE`\n   * * `SNAPSHOT`\n   *\n   * (default: `READ_COMMITED`).\n   */\n  isolationLevel?: number;\n\n  /**\n   * Specifies the language environment for the session. The session language determines the datetime formats and system messages.\n   *\n   * (default: `us_english`).\n   */\n  language?: string;\n\n  /**\n   * A string indicating which network interface (ip address) to use when connecting to SQL Server.\n   */\n  localAddress?: string | undefined;\n\n  /**\n   * A boolean determining whether to parse unique identifier type with lowercase case characters.\n   *\n   * (default: `false`).\n   */\n  lowerCaseGuids?: boolean;\n\n  /**\n   * The maximum number of connection retries for transient errors.、\n   *\n   * (default: `3`).\n   */\n  maxRetriesOnTransientErrors?: number;\n\n  /**\n   * Sets the MultiSubnetFailover = True parameter, which can help minimize the client recovery latency when failovers occur.\n   *\n   * (default: `false`).\n   */\n  multiSubnetFailover?: boolean;\n\n  /**\n   * The size of TDS packets (subject to negotiation with the server).\n   * Should be a power of 2.\n   *\n   * (default: `4096`).\n   */\n  packetSize?: number;\n\n  /**\n   * Port to connect to (default: `1433`).\n   *\n   * Mutually exclusive with [[instanceName]]\n   */\n  port?: number;\n\n  /**\n   * A boolean, determining whether the connection will request read only access from a SQL Server Availability\n   * Group. For more information, see [here](http://msdn.microsoft.com/en-us/library/hh710054.aspx \"Microsoft: Configure Read-Only Routing for an Availability Group (SQL Server)\")\n   *\n   * (default: `false`).\n   */\n  readOnlyIntent?: boolean;\n\n  /**\n   * The number of milliseconds before a request is considered failed, or `0` for no timeout.\n   *\n   * As soon as a response is received, the timeout is cleared. This means that queries that immediately return a response have ability to run longer than this timeout.\n   *\n   * (default: `15000`).\n   */\n  requestTimeout?: number;\n\n  /**\n   * A boolean, that when true will expose received rows in Requests done related events:\n   * * [[Request.Event_doneInProc]]\n   * * [[Request.Event_doneProc]]\n   * * [[Request.Event_done]]\n   *\n   * (default: `false`)\n   *\n   * Caution: If many row are received, enabling this option could result in\n   * excessive memory usage.\n   */\n  rowCollectionOnDone?: boolean;\n\n  /**\n   * A boolean, that when true will expose received rows in Requests' completion callback.See [[Request.constructor]].\n   *\n   * (default: `false`)\n   *\n   * Caution: If many row are received, enabling this option could result in\n   * excessive memory usage.\n   */\n  rowCollectionOnRequestCompletion?: boolean;\n\n  /**\n   * The version of TDS to use. If server doesn't support specified version, negotiated version is used instead.\n   *\n   * The versions are available from `require('tedious').TDS_VERSION`.\n   * * `7_1`\n   * * `7_2`\n   * * `7_3_A`\n   * * `7_3_B`\n   * * `7_4`\n   *\n   * (default: `7_4`)\n   */\n  tdsVersion?: string;\n\n  /**\n   * Specifies the size of varchar(max), nvarchar(max), varbinary(max), text, ntext, and image data returned by a SELECT statement.\n   *\n   * (default: `2147483647`)\n   */\n  textsize?: string;\n\n  /**\n   * If \"true\", the SQL Server SSL certificate is automatically trusted when the communication layer is encrypted using SSL.\n   *\n   * If \"false\", the SQL Server validates the server SSL certificate. If the server certificate validation fails,\n   * the driver raises an error and terminates the connection. Make sure the value passed to serverName exactly\n   * matches the Common Name (CN) or DNS name in the Subject Alternate Name in the server certificate for an SSL connection to succeed.\n   *\n   * (default: `true`)\n   */\n  trustServerCertificate?: boolean;\n\n  /**\n   *\n   */\n  serverName?: string;\n  /**\n   * A boolean determining whether to return rows as arrays or key-value collections.\n   *\n   * (default: `false`).\n   */\n  useColumnNames?: boolean;\n\n  /**\n   * A boolean determining whether to pass time values in UTC or local time.\n   *\n   * (default: `true`).\n   */\n  useUTC?: boolean;\n\n  /**\n   * The workstation ID (WSID) of the client, default os.hostname().\n   * Used for identifying a specific client in profiling, logging or\n   * tracing client activity in SQLServer.\n   *\n   * The value is reported by the TSQL function HOST_NAME().\n   */\n  workstationId?: string | undefined;\n}\n\n/**\n * @private\n */\nconst CLEANUP_TYPE = {\n  NORMAL: 0,\n  REDIRECT: 1,\n  RETRY: 2\n};\n\ninterface RoutingData {\n  server: string;\n  port: number;\n}\n\n/**\n * A [[Connection]] instance represents a single connection to a database server.\n *\n * ```js\n * var Connection = require('tedious').Connection;\n * var config = {\n *  \"authentication\": {\n *    ...,\n *    \"options\": {...}\n *  },\n *  \"options\": {...}\n * };\n * var connection = new Connection(config);\n * ```\n *\n * Only one request at a time may be executed on a connection. Once a [[Request]]\n * has been initiated (with [[Connection.callProcedure]], [[Connection.execSql]],\n * or [[Connection.execSqlBatch]]), another should not be initiated until the\n * [[Request]]'s completion callback is called.\n */\nclass Connection extends EventEmitter {\n  /**\n   * @private\n   */\n  declare fedAuthRequired: boolean;\n  /**\n   * @private\n   */\n  declare config: InternalConnectionConfig;\n  /**\n   * @private\n   */\n  declare secureContextOptions: SecureContextOptions;\n  /**\n   * @private\n   */\n  declare inTransaction: boolean;\n  /**\n   * @private\n   */\n  declare transactionDescriptors: Buffer[];\n  /**\n   * @private\n   */\n  declare transactionDepth: number;\n  /**\n   * @private\n   */\n  declare isSqlBatch: boolean;\n  /**\n   * @private\n   */\n  declare curTransientRetryCount: number;\n  /**\n   * @private\n   */\n  declare transientErrorLookup: TransientErrorLookup;\n  /**\n   * @private\n   */\n  declare closed: boolean;\n  /**\n   * @private\n   */\n  declare loginError: undefined | AggregateError | ConnectionError;\n  /**\n   * @private\n   */\n  declare debug: Debug;\n  /**\n   * @private\n   */\n  declare ntlmpacket: undefined | any;\n  /**\n   * @private\n   */\n  declare ntlmpacketBuffer: undefined | Buffer;\n\n  /**\n   * @private\n   */\n  declare STATE: {\n    INITIALIZED: State;\n    CONNECTING: State;\n    SENT_PRELOGIN: State;\n    REROUTING: State;\n    TRANSIENT_FAILURE_RETRY: State;\n    SENT_TLSSSLNEGOTIATION: State;\n    SENT_LOGIN7_WITH_STANDARD_LOGIN: State;\n    SENT_LOGIN7_WITH_NTLM: State;\n    SENT_LOGIN7_WITH_FEDAUTH: State;\n    LOGGED_IN_SENDING_INITIAL_SQL: State;\n    LOGGED_IN: State;\n    SENT_CLIENT_REQUEST: State;\n    SENT_ATTENTION: State;\n    FINAL: State;\n  };\n\n  /**\n   * @private\n   */\n  declare routingData: undefined | RoutingData;\n\n  /**\n   * @private\n   */\n  declare messageIo: MessageIO;\n  /**\n   * @private\n   */\n  declare state: State;\n  /**\n   * @private\n   */\n  declare resetConnectionOnNextRequest: undefined | boolean;\n\n  /**\n   * @private\n   */\n  declare request: undefined | Request | BulkLoad;\n  /**\n   * @private\n   */\n  declare procReturnStatusValue: undefined | any;\n  /**\n   * @private\n   */\n  declare socket: undefined | net.Socket;\n  /**\n   * @private\n   */\n  declare messageBuffer: Buffer;\n\n  /**\n   * @private\n   */\n  declare connectTimer: undefined | NodeJS.Timeout;\n  /**\n   * @private\n   */\n  declare cancelTimer: undefined | NodeJS.Timeout;\n  /**\n   * @private\n   */\n  declare requestTimer: undefined | NodeJS.Timeout;\n  /**\n   * @private\n   */\n  declare retryTimer: undefined | NodeJS.Timeout;\n\n  /**\n   * @private\n   */\n  _cancelAfterRequestSent: () => void;\n\n  /**\n   * @private\n   */\n  declare databaseCollation: Collation | undefined;\n\n  /**\n   * Note: be aware of the different options field:\n   * 1. config.authentication.options\n   * 2. config.options\n   *\n   * ```js\n   * const { Connection } = require('tedious');\n   *\n   * const config = {\n   *  \"authentication\": {\n   *    ...,\n   *    \"options\": {...}\n   *  },\n   *  \"options\": {...}\n   * };\n   *\n   * const connection = new Connection(config);\n   * ```\n   *\n   * @param config\n   */\n  constructor(config: ConnectionConfiguration) {\n    super();\n\n    if (typeof config !== 'object' || config === null) {\n      throw new TypeError('The \"config\" argument is required and must be of type Object.');\n    }\n\n    if (typeof config.server !== 'string') {\n      throw new TypeError('The \"config.server\" property is required and must be of type string.');\n    }\n\n    this.fedAuthRequired = false;\n\n    let authentication: InternalConnectionConfig['authentication'];\n    if (config.authentication !== undefined) {\n      if (typeof config.authentication !== 'object' || config.authentication === null) {\n        throw new TypeError('The \"config.authentication\" property must be of type Object.');\n      }\n\n      const type = config.authentication.type;\n      const options = config.authentication.options === undefined ? {} : config.authentication.options;\n\n      if (typeof type !== 'string') {\n        throw new TypeError('The \"config.authentication.type\" property must be of type string.');\n      }\n\n      if (type !== 'default' && type !== 'ntlm' && type !== 'azure-active-directory-password' && type !== 'azure-active-directory-access-token' && type !== 'azure-active-directory-msi-vm' && type !== 'azure-active-directory-msi-app-service' && type !== 'azure-active-directory-service-principal-secret' && type !== 'azure-active-directory-default') {\n        throw new TypeError('The \"type\" property must one of \"default\", \"ntlm\", \"azure-active-directory-password\", \"azure-active-directory-access-token\", \"azure-active-directory-default\", \"azure-active-directory-msi-vm\" or \"azure-active-directory-msi-app-service\" or \"azure-active-directory-service-principal-secret\".');\n      }\n\n      if (typeof options !== 'object' || options === null) {\n        throw new TypeError('The \"config.authentication.options\" property must be of type object.');\n      }\n\n      if (type === 'ntlm') {\n        if (typeof options.domain !== 'string') {\n          throw new TypeError('The \"config.authentication.options.domain\" property must be of type string.');\n        }\n\n        if (options.userName !== undefined && typeof options.userName !== 'string') {\n          throw new TypeError('The \"config.authentication.options.userName\" property must be of type string.');\n        }\n\n        if (options.password !== undefined && typeof options.password !== 'string') {\n          throw new TypeError('The \"config.authentication.options.password\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'ntlm',\n          options: {\n            userName: options.userName,\n            password: options.password,\n            domain: options.domain && options.domain.toUpperCase()\n          }\n        };\n      } else if (type === 'azure-active-directory-password') {\n        if (typeof options.clientId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientId\" property must be of type string.');\n        }\n\n        if (options.userName !== undefined && typeof options.userName !== 'string') {\n          throw new TypeError('The \"config.authentication.options.userName\" property must be of type string.');\n        }\n\n        if (options.password !== undefined && typeof options.password !== 'string') {\n          throw new TypeError('The \"config.authentication.options.password\" property must be of type string.');\n        }\n\n        if (options.tenantId !== undefined && typeof options.tenantId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.tenantId\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'azure-active-directory-password',\n          options: {\n            userName: options.userName,\n            password: options.password,\n            tenantId: options.tenantId,\n            clientId: options.clientId\n          }\n        };\n      } else if (type === 'azure-active-directory-access-token') {\n        if (typeof options.token !== 'string') {\n          throw new TypeError('The \"config.authentication.options.token\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'azure-active-directory-access-token',\n          options: {\n            token: options.token\n          }\n        };\n      } else if (type === 'azure-active-directory-msi-vm') {\n        if (options.clientId !== undefined && typeof options.clientId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientId\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'azure-active-directory-msi-vm',\n          options: {\n            clientId: options.clientId\n          }\n        };\n      } else if (type === 'azure-active-directory-default') {\n        if (options.clientId !== undefined && typeof options.clientId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientId\" property must be of type string.');\n        }\n        authentication = {\n          type: 'azure-active-directory-default',\n          options: {\n            clientId: options.clientId\n          }\n        };\n      } else if (type === 'azure-active-directory-msi-app-service') {\n        if (options.clientId !== undefined && typeof options.clientId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientId\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'azure-active-directory-msi-app-service',\n          options: {\n            clientId: options.clientId\n          }\n        };\n      } else if (type === 'azure-active-directory-service-principal-secret') {\n        if (typeof options.clientId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientId\" property must be of type string.');\n        }\n\n        if (typeof options.clientSecret !== 'string') {\n          throw new TypeError('The \"config.authentication.options.clientSecret\" property must be of type string.');\n        }\n\n        if (typeof options.tenantId !== 'string') {\n          throw new TypeError('The \"config.authentication.options.tenantId\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'azure-active-directory-service-principal-secret',\n          options: {\n            clientId: options.clientId,\n            clientSecret: options.clientSecret,\n            tenantId: options.tenantId\n          }\n        };\n      } else {\n        if (options.userName !== undefined && typeof options.userName !== 'string') {\n          throw new TypeError('The \"config.authentication.options.userName\" property must be of type string.');\n        }\n\n        if (options.password !== undefined && typeof options.password !== 'string') {\n          throw new TypeError('The \"config.authentication.options.password\" property must be of type string.');\n        }\n\n        authentication = {\n          type: 'default',\n          options: {\n            userName: options.userName,\n            password: options.password\n          }\n        };\n      }\n    } else {\n      authentication = {\n        type: 'default',\n        options: {\n          userName: undefined,\n          password: undefined\n        }\n      };\n    }\n\n    this.config = {\n      server: config.server,\n      authentication: authentication,\n      options: {\n        abortTransactionOnError: false,\n        appName: undefined,\n        camelCaseColumns: false,\n        cancelTimeout: DEFAULT_CANCEL_TIMEOUT,\n        columnEncryptionKeyCacheTTL: 2 * 60 * 60 * 1000,  // Units: milliseconds\n        columnEncryptionSetting: false,\n        columnNameReplacer: undefined,\n        connectionRetryInterval: DEFAULT_CONNECT_RETRY_INTERVAL,\n        connectTimeout: DEFAULT_CONNECT_TIMEOUT,\n        connector: undefined,\n        connectionIsolationLevel: ISOLATION_LEVEL.READ_COMMITTED,\n        cryptoCredentialsDetails: {},\n        database: undefined,\n        datefirst: DEFAULT_DATEFIRST,\n        dateFormat: DEFAULT_DATEFORMAT,\n        debug: {\n          data: false,\n          packet: false,\n          payload: false,\n          token: false\n        },\n        enableAnsiNull: true,\n        enableAnsiNullDefault: true,\n        enableAnsiPadding: true,\n        enableAnsiWarnings: true,\n        enableArithAbort: true,\n        enableConcatNullYieldsNull: true,\n        enableCursorCloseOnCommit: null,\n        enableImplicitTransactions: false,\n        enableNumericRoundabort: false,\n        enableQuotedIdentifier: true,\n        encrypt: true,\n        fallbackToDefaultDb: false,\n        encryptionKeyStoreProviders: undefined,\n        instanceName: undefined,\n        isolationLevel: ISOLATION_LEVEL.READ_COMMITTED,\n        language: DEFAULT_LANGUAGE,\n        localAddress: undefined,\n        maxRetriesOnTransientErrors: 3,\n        multiSubnetFailover: false,\n        packetSize: DEFAULT_PACKET_SIZE,\n        port: DEFAULT_PORT,\n        readOnlyIntent: false,\n        requestTimeout: DEFAULT_CLIENT_REQUEST_TIMEOUT,\n        rowCollectionOnDone: false,\n        rowCollectionOnRequestCompletion: false,\n        serverName: undefined,\n        serverSupportsColumnEncryption: false,\n        tdsVersion: DEFAULT_TDS_VERSION,\n        textsize: DEFAULT_TEXTSIZE,\n        trustedServerNameAE: undefined,\n        trustServerCertificate: false,\n        useColumnNames: false,\n        useUTC: true,\n        workstationId: undefined,\n        lowerCaseGuids: false\n      }\n    };\n\n    if (config.options) {\n      if (config.options.port && config.options.instanceName) {\n        throw new Error('Port and instanceName are mutually exclusive, but ' + config.options.port + ' and ' + config.options.instanceName + ' provided');\n      }\n\n      if (config.options.abortTransactionOnError !== undefined) {\n        if (typeof config.options.abortTransactionOnError !== 'boolean' && config.options.abortTransactionOnError !== null) {\n          throw new TypeError('The \"config.options.abortTransactionOnError\" property must be of type string or null.');\n        }\n\n        this.config.options.abortTransactionOnError = config.options.abortTransactionOnError;\n      }\n\n      if (config.options.appName !== undefined) {\n        if (typeof config.options.appName !== 'string') {\n          throw new TypeError('The \"config.options.appName\" property must be of type string.');\n        }\n\n        this.config.options.appName = config.options.appName;\n      }\n\n      if (config.options.camelCaseColumns !== undefined) {\n        if (typeof config.options.camelCaseColumns !== 'boolean') {\n          throw new TypeError('The \"config.options.camelCaseColumns\" property must be of type boolean.');\n        }\n\n        this.config.options.camelCaseColumns = config.options.camelCaseColumns;\n      }\n\n      if (config.options.cancelTimeout !== undefined) {\n        if (typeof config.options.cancelTimeout !== 'number') {\n          throw new TypeError('The \"config.options.cancelTimeout\" property must be of type number.');\n        }\n\n        this.config.options.cancelTimeout = config.options.cancelTimeout;\n      }\n\n      if (config.options.columnNameReplacer) {\n        if (typeof config.options.columnNameReplacer !== 'function') {\n          throw new TypeError('The \"config.options.cancelTimeout\" property must be of type function.');\n        }\n\n        this.config.options.columnNameReplacer = config.options.columnNameReplacer;\n      }\n\n      if (config.options.connectionIsolationLevel !== undefined) {\n        assertValidIsolationLevel(config.options.connectionIsolationLevel, 'config.options.connectionIsolationLevel');\n\n        this.config.options.connectionIsolationLevel = config.options.connectionIsolationLevel;\n      }\n\n      if (config.options.connectTimeout !== undefined) {\n        if (typeof config.options.connectTimeout !== 'number') {\n          throw new TypeError('The \"config.options.connectTimeout\" property must be of type number.');\n        }\n\n        this.config.options.connectTimeout = config.options.connectTimeout;\n      }\n\n      if (config.options.connector !== undefined) {\n        if (typeof config.options.connector !== 'function') {\n          throw new TypeError('The \"config.options.connector\" property must be a function.');\n        }\n\n        this.config.options.connector = config.options.connector;\n      }\n\n      if (config.options.cryptoCredentialsDetails !== undefined) {\n        if (typeof config.options.cryptoCredentialsDetails !== 'object' || config.options.cryptoCredentialsDetails === null) {\n          throw new TypeError('The \"config.options.cryptoCredentialsDetails\" property must be of type Object.');\n        }\n\n        this.config.options.cryptoCredentialsDetails = config.options.cryptoCredentialsDetails;\n      }\n\n      if (config.options.database !== undefined) {\n        if (typeof config.options.database !== 'string') {\n          throw new TypeError('The \"config.options.database\" property must be of type string.');\n        }\n\n        this.config.options.database = config.options.database;\n      }\n\n      if (config.options.datefirst !== undefined) {\n        if (typeof config.options.datefirst !== 'number' && config.options.datefirst !== null) {\n          throw new TypeError('The \"config.options.datefirst\" property must be of type number.');\n        }\n\n        if (config.options.datefirst !== null && (config.options.datefirst < 1 || config.options.datefirst > 7)) {\n          throw new RangeError('The \"config.options.datefirst\" property must be >= 1 and <= 7');\n        }\n\n        this.config.options.datefirst = config.options.datefirst;\n      }\n\n      if (config.options.dateFormat !== undefined) {\n        if (typeof config.options.dateFormat !== 'string' && config.options.dateFormat !== null) {\n          throw new TypeError('The \"config.options.dateFormat\" property must be of type string or null.');\n        }\n\n        this.config.options.dateFormat = config.options.dateFormat;\n      }\n\n      if (config.options.debug) {\n        if (config.options.debug.data !== undefined) {\n          if (typeof config.options.debug.data !== 'boolean') {\n            throw new TypeError('The \"config.options.debug.data\" property must be of type boolean.');\n          }\n\n          this.config.options.debug.data = config.options.debug.data;\n        }\n\n        if (config.options.debug.packet !== undefined) {\n          if (typeof config.options.debug.packet !== 'boolean') {\n            throw new TypeError('The \"config.options.debug.packet\" property must be of type boolean.');\n          }\n\n          this.config.options.debug.packet = config.options.debug.packet;\n        }\n\n        if (config.options.debug.payload !== undefined) {\n          if (typeof config.options.debug.payload !== 'boolean') {\n            throw new TypeError('The \"config.options.debug.payload\" property must be of type boolean.');\n          }\n\n          this.config.options.debug.payload = config.options.debug.payload;\n        }\n\n        if (config.options.debug.token !== undefined) {\n          if (typeof config.options.debug.token !== 'boolean') {\n            throw new TypeError('The \"config.options.debug.token\" property must be of type boolean.');\n          }\n\n          this.config.options.debug.token = config.options.debug.token;\n        }\n      }\n\n      if (config.options.enableAnsiNull !== undefined) {\n        if (typeof config.options.enableAnsiNull !== 'boolean' && config.options.enableAnsiNull !== null) {\n          throw new TypeError('The \"config.options.enableAnsiNull\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableAnsiNull = config.options.enableAnsiNull;\n      }\n\n      if (config.options.enableAnsiNullDefault !== undefined) {\n        if (typeof config.options.enableAnsiNullDefault !== 'boolean' && config.options.enableAnsiNullDefault !== null) {\n          throw new TypeError('The \"config.options.enableAnsiNullDefault\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableAnsiNullDefault = config.options.enableAnsiNullDefault;\n      }\n\n      if (config.options.enableAnsiPadding !== undefined) {\n        if (typeof config.options.enableAnsiPadding !== 'boolean' && config.options.enableAnsiPadding !== null) {\n          throw new TypeError('The \"config.options.enableAnsiPadding\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableAnsiPadding = config.options.enableAnsiPadding;\n      }\n\n      if (config.options.enableAnsiWarnings !== undefined) {\n        if (typeof config.options.enableAnsiWarnings !== 'boolean' && config.options.enableAnsiWarnings !== null) {\n          throw new TypeError('The \"config.options.enableAnsiWarnings\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableAnsiWarnings = config.options.enableAnsiWarnings;\n      }\n\n      if (config.options.enableArithAbort !== undefined) {\n        if (typeof config.options.enableArithAbort !== 'boolean' && config.options.enableArithAbort !== null) {\n          throw new TypeError('The \"config.options.enableArithAbort\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableArithAbort = config.options.enableArithAbort;\n      }\n\n      if (config.options.enableConcatNullYieldsNull !== undefined) {\n        if (typeof config.options.enableConcatNullYieldsNull !== 'boolean' && config.options.enableConcatNullYieldsNull !== null) {\n          throw new TypeError('The \"config.options.enableConcatNullYieldsNull\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableConcatNullYieldsNull = config.options.enableConcatNullYieldsNull;\n      }\n\n      if (config.options.enableCursorCloseOnCommit !== undefined) {\n        if (typeof config.options.enableCursorCloseOnCommit !== 'boolean' && config.options.enableCursorCloseOnCommit !== null) {\n          throw new TypeError('The \"config.options.enableCursorCloseOnCommit\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableCursorCloseOnCommit = config.options.enableCursorCloseOnCommit;\n      }\n\n      if (config.options.enableImplicitTransactions !== undefined) {\n        if (typeof config.options.enableImplicitTransactions !== 'boolean' && config.options.enableImplicitTransactions !== null) {\n          throw new TypeError('The \"config.options.enableImplicitTransactions\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableImplicitTransactions = config.options.enableImplicitTransactions;\n      }\n\n      if (config.options.enableNumericRoundabort !== undefined) {\n        if (typeof config.options.enableNumericRoundabort !== 'boolean' && config.options.enableNumericRoundabort !== null) {\n          throw new TypeError('The \"config.options.enableNumericRoundabort\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableNumericRoundabort = config.options.enableNumericRoundabort;\n      }\n\n      if (config.options.enableQuotedIdentifier !== undefined) {\n        if (typeof config.options.enableQuotedIdentifier !== 'boolean' && config.options.enableQuotedIdentifier !== null) {\n          throw new TypeError('The \"config.options.enableQuotedIdentifier\" property must be of type boolean or null.');\n        }\n\n        this.config.options.enableQuotedIdentifier = config.options.enableQuotedIdentifier;\n      }\n      if (config.options.encrypt !== undefined) {\n        if (typeof config.options.encrypt !== 'boolean') {\n          if (config.options.encrypt !== 'strict') {\n            throw new TypeError('The \"encrypt\" property must be set to \"strict\", or of type boolean.');\n          }\n        }\n\n        this.config.options.encrypt = config.options.encrypt;\n      }\n\n      if (config.options.fallbackToDefaultDb !== undefined) {\n        if (typeof config.options.fallbackToDefaultDb !== 'boolean') {\n          throw new TypeError('The \"config.options.fallbackToDefaultDb\" property must be of type boolean.');\n        }\n\n        this.config.options.fallbackToDefaultDb = config.options.fallbackToDefaultDb;\n      }\n\n      if (config.options.instanceName !== undefined) {\n        if (typeof config.options.instanceName !== 'string') {\n          throw new TypeError('The \"config.options.instanceName\" property must be of type string.');\n        }\n\n        this.config.options.instanceName = config.options.instanceName;\n        this.config.options.port = undefined;\n      }\n\n      if (config.options.isolationLevel !== undefined) {\n        assertValidIsolationLevel(config.options.isolationLevel, 'config.options.isolationLevel');\n\n        this.config.options.isolationLevel = config.options.isolationLevel;\n      }\n\n      if (config.options.language !== undefined) {\n        if (typeof config.options.language !== 'string' && config.options.language !== null) {\n          throw new TypeError('The \"config.options.language\" property must be of type string or null.');\n        }\n\n        this.config.options.language = config.options.language;\n      }\n\n      if (config.options.localAddress !== undefined) {\n        if (typeof config.options.localAddress !== 'string') {\n          throw new TypeError('The \"config.options.localAddress\" property must be of type string.');\n        }\n\n        this.config.options.localAddress = config.options.localAddress;\n      }\n\n      if (config.options.multiSubnetFailover !== undefined) {\n        if (typeof config.options.multiSubnetFailover !== 'boolean') {\n          throw new TypeError('The \"config.options.multiSubnetFailover\" property must be of type boolean.');\n        }\n\n        this.config.options.multiSubnetFailover = config.options.multiSubnetFailover;\n      }\n\n      if (config.options.packetSize !== undefined) {\n        if (typeof config.options.packetSize !== 'number') {\n          throw new TypeError('The \"config.options.packetSize\" property must be of type number.');\n        }\n\n        this.config.options.packetSize = config.options.packetSize;\n      }\n\n      if (config.options.port !== undefined) {\n        if (typeof config.options.port !== 'number') {\n          throw new TypeError('The \"config.options.port\" property must be of type number.');\n        }\n\n        if (config.options.port <= 0 || config.options.port >= 65536) {\n          throw new RangeError('The \"config.options.port\" property must be > 0 and < 65536');\n        }\n\n        this.config.options.port = config.options.port;\n        this.config.options.instanceName = undefined;\n      }\n\n      if (config.options.readOnlyIntent !== undefined) {\n        if (typeof config.options.readOnlyIntent !== 'boolean') {\n          throw new TypeError('The \"config.options.readOnlyIntent\" property must be of type boolean.');\n        }\n\n        this.config.options.readOnlyIntent = config.options.readOnlyIntent;\n      }\n\n      if (config.options.requestTimeout !== undefined) {\n        if (typeof config.options.requestTimeout !== 'number') {\n          throw new TypeError('The \"config.options.requestTimeout\" property must be of type number.');\n        }\n\n        this.config.options.requestTimeout = config.options.requestTimeout;\n      }\n\n      if (config.options.maxRetriesOnTransientErrors !== undefined) {\n        if (typeof config.options.maxRetriesOnTransientErrors !== 'number') {\n          throw new TypeError('The \"config.options.maxRetriesOnTransientErrors\" property must be of type number.');\n        }\n\n        if (config.options.maxRetriesOnTransientErrors < 0) {\n          throw new TypeError('The \"config.options.maxRetriesOnTransientErrors\" property must be equal or greater than 0.');\n        }\n\n        this.config.options.maxRetriesOnTransientErrors = config.options.maxRetriesOnTransientErrors;\n      }\n\n      if (config.options.connectionRetryInterval !== undefined) {\n        if (typeof config.options.connectionRetryInterval !== 'number') {\n          throw new TypeError('The \"config.options.connectionRetryInterval\" property must be of type number.');\n        }\n\n        if (config.options.connectionRetryInterval <= 0) {\n          throw new TypeError('The \"config.options.connectionRetryInterval\" property must be greater than 0.');\n        }\n\n        this.config.options.connectionRetryInterval = config.options.connectionRetryInterval;\n      }\n\n      if (config.options.rowCollectionOnDone !== undefined) {\n        if (typeof config.options.rowCollectionOnDone !== 'boolean') {\n          throw new TypeError('The \"config.options.rowCollectionOnDone\" property must be of type boolean.');\n        }\n\n        this.config.options.rowCollectionOnDone = config.options.rowCollectionOnDone;\n      }\n\n      if (config.options.rowCollectionOnRequestCompletion !== undefined) {\n        if (typeof config.options.rowCollectionOnRequestCompletion !== 'boolean') {\n          throw new TypeError('The \"config.options.rowCollectionOnRequestCompletion\" property must be of type boolean.');\n        }\n\n        this.config.options.rowCollectionOnRequestCompletion = config.options.rowCollectionOnRequestCompletion;\n      }\n\n      if (config.options.tdsVersion !== undefined) {\n        if (typeof config.options.tdsVersion !== 'string') {\n          throw new TypeError('The \"config.options.tdsVersion\" property must be of type string.');\n        }\n\n        this.config.options.tdsVersion = config.options.tdsVersion;\n      }\n\n      if (config.options.textsize !== undefined) {\n        if (typeof config.options.textsize !== 'number' && config.options.textsize !== null) {\n          throw new TypeError('The \"config.options.textsize\" property must be of type number or null.');\n        }\n\n        if (config.options.textsize > 2147483647) {\n          throw new TypeError('The \"config.options.textsize\" can\\'t be greater than 2147483647.');\n        } else if (config.options.textsize < -1) {\n          throw new TypeError('The \"config.options.textsize\" can\\'t be smaller than -1.');\n        }\n\n        this.config.options.textsize = config.options.textsize | 0;\n      }\n\n      if (config.options.trustServerCertificate !== undefined) {\n        if (typeof config.options.trustServerCertificate !== 'boolean') {\n          throw new TypeError('The \"config.options.trustServerCertificate\" property must be of type boolean.');\n        }\n\n        this.config.options.trustServerCertificate = config.options.trustServerCertificate;\n      }\n\n      if (config.options.serverName !== undefined) {\n        if (typeof config.options.serverName !== 'string') {\n          throw new TypeError('The \"config.options.serverName\" property must be of type string.');\n        }\n        this.config.options.serverName = config.options.serverName;\n      }\n\n      if (config.options.useColumnNames !== undefined) {\n        if (typeof config.options.useColumnNames !== 'boolean') {\n          throw new TypeError('The \"config.options.useColumnNames\" property must be of type boolean.');\n        }\n\n        this.config.options.useColumnNames = config.options.useColumnNames;\n      }\n\n      if (config.options.useUTC !== undefined) {\n        if (typeof config.options.useUTC !== 'boolean') {\n          throw new TypeError('The \"config.options.useUTC\" property must be of type boolean.');\n        }\n\n        this.config.options.useUTC = config.options.useUTC;\n      }\n\n      if (config.options.workstationId !== undefined) {\n        if (typeof config.options.workstationId !== 'string') {\n          throw new TypeError('The \"config.options.workstationId\" property must be of type string.');\n        }\n\n        this.config.options.workstationId = config.options.workstationId;\n      }\n\n      if (config.options.lowerCaseGuids !== undefined) {\n        if (typeof config.options.lowerCaseGuids !== 'boolean') {\n          throw new TypeError('The \"config.options.lowerCaseGuids\" property must be of type boolean.');\n        }\n\n        this.config.options.lowerCaseGuids = config.options.lowerCaseGuids;\n      }\n    }\n\n    this.secureContextOptions = this.config.options.cryptoCredentialsDetails;\n    if (this.secureContextOptions.secureOptions === undefined) {\n      // If the caller has not specified their own `secureOptions`,\n      // we set `SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS` here.\n      // Older SQL Server instances running on older Windows versions have\n      // trouble with the BEAST workaround in OpenSSL.\n      // As BEAST is a browser specific exploit, we can just disable this option here.\n      this.secureContextOptions = Object.create(this.secureContextOptions, {\n        secureOptions: {\n          value: constants.SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS\n        }\n      });\n    }\n\n    this.debug = this.createDebug();\n    this.inTransaction = false;\n    this.transactionDescriptors = [Buffer.from([0, 0, 0, 0, 0, 0, 0, 0])];\n\n    // 'beginTransaction', 'commitTransaction' and 'rollbackTransaction'\n    // events are utilized to maintain inTransaction property state which in\n    // turn is used in managing transactions. These events are only fired for\n    // TDS version 7.2 and beyond. The properties below are used to emulate\n    // equivalent behavior for TDS versions before 7.2.\n    this.transactionDepth = 0;\n    this.isSqlBatch = false;\n    this.closed = false;\n    this.messageBuffer = Buffer.alloc(0);\n\n    this.curTransientRetryCount = 0;\n    this.transientErrorLookup = new TransientErrorLookup();\n\n    this.state = this.STATE.INITIALIZED;\n\n    this._cancelAfterRequestSent = () => {\n      this.messageIo.sendMessage(TYPE.ATTENTION);\n      this.createCancelTimer();\n    };\n  }\n\n  connect(connectListener?: (err?: Error) => void) {\n    if (this.state !== this.STATE.INITIALIZED) {\n      throw new ConnectionError('`.connect` can not be called on a Connection in `' + this.state.name + '` state.');\n    }\n\n    if (connectListener) {\n      const onConnect = (err?: Error) => {\n        this.removeListener('error', onError);\n        connectListener(err);\n      };\n\n      const onError = (err: Error) => {\n        this.removeListener('connect', onConnect);\n        connectListener(err);\n      };\n\n      this.once('connect', onConnect);\n      this.once('error', onError);\n    }\n\n    this.transitionTo(this.STATE.CONNECTING);\n  }\n\n  /**\n   * The server has reported that the charset has changed.\n   */\n  on(event: 'charsetChange', listener: (charset: string) => void): this\n\n  /**\n   * The attempt to connect and validate has completed.\n   */\n  on(\n    event: 'connect',\n    /**\n     * @param err If successfully connected, will be falsey. If there was a\n     *   problem (with either connecting or validation), will be an [[Error]] object.\n     */\n    listener: (err: Error | undefined) => void\n  ): this\n\n  /**\n   * The server has reported that the active database has changed.\n   * This may be as a result of a successful login, or a `use` statement.\n   */\n  on(event: 'databaseChange', listener: (databaseName: string) => void): this\n\n  /**\n   * A debug message is available. It may be logged or ignored.\n   */\n  on(event: 'debug', listener: (messageText: string) => void): this\n\n  /**\n   * Internal error occurs.\n   */\n  on(event: 'error', listener: (err: Error) => void): this\n\n  /**\n   * The server has issued an error message.\n   */\n  on(event: 'errorMessage', listener: (message: import('./token/token').ErrorMessageToken) => void): this\n\n  /**\n   * The connection has ended.\n   *\n   * This may be as a result of the client calling [[close]], the server\n   * closing the connection, or a network error.\n   */\n  on(event: 'end', listener: () => void): this\n\n  /**\n   * The server has issued an information message.\n   */\n  on(event: 'infoMessage', listener: (message: import('./token/token').InfoMessageToken) => void): this\n\n  /**\n   * The server has reported that the language has changed.\n   */\n  on(event: 'languageChange', listener: (languageName: string) => void): this\n\n  /**\n   * The connection was reset.\n   */\n  on(event: 'resetConnection', listener: () => void): this\n\n  /**\n   * A secure connection has been established.\n   */\n  on(event: 'secure', listener: (cleartext: import('tls').TLSSocket) => void): this\n\n  on(event: string | symbol, listener: (...args: any[]) => void) {\n    return super.on(event, listener);\n  }\n\n  /**\n   * @private\n   */\n  emit(event: 'charsetChange', charset: string): boolean\n  /**\n   * @private\n   */\n  emit(event: 'connect', error?: Error): boolean\n  /**\n   * @private\n   */\n  emit(event: 'databaseChange', databaseName: string): boolean\n  /**\n   * @private\n   */\n  emit(event: 'debug', messageText: string): boolean\n  /**\n   * @private\n   */\n  emit(event: 'error', error: Error): boolean\n  /**\n   * @private\n   */\n  emit(event: 'errorMessage', message: import('./token/token').ErrorMessageToken): boolean\n  /**\n   * @private\n   */\n  emit(event: 'end'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'infoMessage', message: import('./token/token').InfoMessageToken): boolean\n  /**\n   * @private\n   */\n  emit(event: 'languageChange', languageName: string): boolean\n  /**\n   * @private\n   */\n  emit(event: 'secure', cleartext: import('tls').TLSSocket): boolean\n  /**\n   * @private\n   */\n  emit(event: 'rerouting'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'resetConnection'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'retry'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'rollbackTransaction'): boolean\n\n  emit(event: string | symbol, ...args: any[]) {\n    return super.emit(event, ...args);\n  }\n\n  /**\n   * Closes the connection to the database.\n   *\n   * The [[Event_end]] will be emitted once the connection has been closed.\n   */\n  close() {\n    this.transitionTo(this.STATE.FINAL);\n  }\n\n  /**\n   * @private\n   */\n  initialiseConnection() {\n    const signal = this.createConnectTimer();\n\n    if (this.config.options.port) {\n      return this.connectOnPort(this.config.options.port, this.config.options.multiSubnetFailover, signal, this.config.options.connector);\n    } else {\n      return instanceLookup({\n        server: this.config.server,\n        instanceName: this.config.options.instanceName!,\n        timeout: this.config.options.connectTimeout,\n        signal: signal\n      }).then((port) => {\n        process.nextTick(() => {\n          this.connectOnPort(port, this.config.options.multiSubnetFailover, signal, this.config.options.connector);\n        });\n      }, (err) => {\n        this.clearConnectTimer();\n\n        if (signal.aborted) {\n          // Ignore the AbortError for now, this is still handled by the connectTimer firing\n          return;\n        }\n\n        process.nextTick(() => {\n          this.emit('connect', new ConnectionError(err.message, 'EINSTLOOKUP'));\n        });\n      });\n    }\n  }\n\n  /**\n   * @private\n   */\n  cleanupConnection(cleanupType: typeof CLEANUP_TYPE[keyof typeof CLEANUP_TYPE]) {\n    if (!this.closed) {\n      this.clearConnectTimer();\n      this.clearRequestTimer();\n      this.clearRetryTimer();\n      this.closeConnection();\n      if (cleanupType === CLEANUP_TYPE.REDIRECT) {\n        this.emit('rerouting');\n      } else if (cleanupType !== CLEANUP_TYPE.RETRY) {\n        process.nextTick(() => {\n          this.emit('end');\n        });\n      }\n\n      const request = this.request;\n      if (request) {\n        const err = new RequestError('Connection closed before request completed.', 'ECLOSE');\n        request.callback(err);\n        this.request = undefined;\n      }\n\n      this.closed = true;\n      this.loginError = undefined;\n    }\n  }\n\n  /**\n   * @private\n   */\n  createDebug() {\n    const debug = new Debug(this.config.options.debug);\n    debug.on('debug', (message) => {\n      this.emit('debug', message);\n    });\n    return debug;\n  }\n\n  /**\n   * @private\n   */\n  createTokenStreamParser(message: Message, handler: TokenHandler) {\n    return new TokenStreamParser(message, this.debug, handler, this.config.options);\n  }\n\n  socketHandlingForSendPreLogin(socket: net.Socket) {\n    socket.on('error', (error) => { this.socketError(error); });\n    socket.on('close', () => { this.socketClose(); });\n    socket.on('end', () => { this.socketEnd(); });\n    socket.setKeepAlive(true, KEEP_ALIVE_INITIAL_DELAY);\n\n    this.messageIo = new MessageIO(socket, this.config.options.packetSize, this.debug);\n    this.messageIo.on('secure', (cleartext) => { this.emit('secure', cleartext); });\n\n    this.socket = socket;\n\n    this.closed = false;\n    this.debug.log('connected to ' + this.config.server + ':' + this.config.options.port);\n\n    this.sendPreLogin();\n    this.transitionTo(this.STATE.SENT_PRELOGIN);\n  }\n\n  wrapWithTls(socket: net.Socket, signal: AbortSignal): Promise<tls.TLSSocket> {\n    signal.throwIfAborted();\n\n    return new Promise((resolve, reject) => {\n      const secureContext = tls.createSecureContext(this.secureContextOptions);\n      // If connect to an ip address directly,\n      // need to set the servername to an empty string\n      // if the user has not given a servername explicitly\n      const serverName = !net.isIP(this.config.server) ? this.config.server : '';\n      const encryptOptions = {\n        host: this.config.server,\n        socket: socket,\n        ALPNProtocols: ['tds/8.0'],\n        secureContext: secureContext,\n        servername: this.config.options.serverName ? this.config.options.serverName : serverName,\n      };\n\n      const encryptsocket = tls.connect(encryptOptions);\n\n      const onAbort = () => {\n        encryptsocket.removeListener('error', onError);\n        encryptsocket.removeListener('connect', onConnect);\n\n        encryptsocket.destroy();\n\n        reject(signal.reason);\n      };\n\n      const onError = (err: Error) => {\n        signal.removeEventListener('abort', onAbort);\n\n        encryptsocket.removeListener('error', onError);\n        encryptsocket.removeListener('connect', onConnect);\n\n        encryptsocket.destroy();\n\n        reject(err);\n      };\n\n      const onConnect = () => {\n        signal.removeEventListener('abort', onAbort);\n\n        encryptsocket.removeListener('error', onError);\n        encryptsocket.removeListener('connect', onConnect);\n\n        resolve(encryptsocket);\n      };\n\n      signal.addEventListener('abort', onAbort, { once: true });\n\n      encryptsocket.on('error', onError);\n      encryptsocket.on('secureConnect', onConnect);\n    });\n  }\n\n  connectOnPort(port: number, multiSubnetFailover: boolean, signal: AbortSignal, customConnector?: () => Promise<net.Socket>) {\n    const connectOpts = {\n      host: this.routingData ? this.routingData.server : this.config.server,\n      port: this.routingData ? this.routingData.port : port,\n      localAddress: this.config.options.localAddress\n    };\n\n    const connect = customConnector || (multiSubnetFailover ? connectInParallel : connectInSequence);\n\n    (async () => {\n      let socket = await connect(connectOpts, dns.lookup, signal);\n\n      if (this.config.options.encrypt === 'strict') {\n        try {\n          // Wrap the socket with TLS for TDS 8.0\n          socket = await this.wrapWithTls(socket, signal);\n        } catch (err) {\n          socket.end();\n\n          throw err;\n        }\n      }\n\n      this.socketHandlingForSendPreLogin(socket);\n    })().catch((err) => {\n      this.clearConnectTimer();\n\n      if (signal.aborted) {\n        return;\n      }\n\n      process.nextTick(() => { this.socketError(err); });\n    });\n  }\n\n  /**\n   * @private\n   */\n  closeConnection() {\n    if (this.socket) {\n      this.socket.destroy();\n    }\n  }\n\n  /**\n   * @private\n   */\n  createConnectTimer() {\n    const controller = new AbortController();\n    this.connectTimer = setTimeout(() => {\n      controller.abort();\n      this.connectTimeout();\n    }, this.config.options.connectTimeout);\n    return controller.signal;\n  }\n\n  /**\n   * @private\n   */\n  createCancelTimer() {\n    this.clearCancelTimer();\n    const timeout = this.config.options.cancelTimeout;\n    if (timeout > 0) {\n      this.cancelTimer = setTimeout(() => {\n        this.cancelTimeout();\n      }, timeout);\n    }\n  }\n\n  /**\n   * @private\n   */\n  createRequestTimer() {\n    this.clearRequestTimer(); // release old timer, just to be safe\n    const request = this.request as Request;\n    const timeout = (request.timeout !== undefined) ? request.timeout : this.config.options.requestTimeout;\n    if (timeout) {\n      this.requestTimer = setTimeout(() => {\n        this.requestTimeout();\n      }, timeout);\n    }\n  }\n\n  /**\n   * @private\n   */\n  createRetryTimer() {\n    this.clearRetryTimer();\n    this.retryTimer = setTimeout(() => {\n      this.retryTimeout();\n    }, this.config.options.connectionRetryInterval);\n  }\n\n  /**\n   * @private\n   */\n  connectTimeout() {\n    const hostPostfix = this.config.options.port ? `:${this.config.options.port}` : `\\\\${this.config.options.instanceName}`;\n    // If we have routing data stored, this connection has been redirected\n    const server = this.routingData ? this.routingData.server : this.config.server;\n    const port = this.routingData ? `:${this.routingData.port}` : hostPostfix;\n    // Grab the target host from the connection configuration, and from a redirect message\n    // otherwise, leave the message empty.\n    const routingMessage = this.routingData ? ` (redirected from ${this.config.server}${hostPostfix})` : '';\n    const message = `Failed to connect to ${server}${port}${routingMessage} in ${this.config.options.connectTimeout}ms`;\n    this.debug.log(message);\n    this.emit('connect', new ConnectionError(message, 'ETIMEOUT'));\n    this.connectTimer = undefined;\n    this.dispatchEvent('connectTimeout');\n  }\n\n  /**\n   * @private\n   */\n  cancelTimeout() {\n    const message = `Failed to cancel request in ${this.config.options.cancelTimeout}ms`;\n    this.debug.log(message);\n    this.dispatchEvent('socketError', new ConnectionError(message, 'ETIMEOUT'));\n  }\n\n  /**\n   * @private\n   */\n  requestTimeout() {\n    this.requestTimer = undefined;\n    const request = this.request!;\n    request.cancel();\n    const timeout = (request.timeout !== undefined) ? request.timeout : this.config.options.requestTimeout;\n    const message = 'Timeout: Request failed to complete in ' + timeout + 'ms';\n    request.error = new RequestError(message, 'ETIMEOUT');\n  }\n\n  /**\n   * @private\n   */\n  retryTimeout() {\n    this.retryTimer = undefined;\n    this.emit('retry');\n    this.transitionTo(this.STATE.CONNECTING);\n  }\n\n  /**\n   * @private\n   */\n  clearConnectTimer() {\n    if (this.connectTimer) {\n      clearTimeout(this.connectTimer);\n      this.connectTimer = undefined;\n    }\n  }\n\n  /**\n   * @private\n   */\n  clearCancelTimer() {\n    if (this.cancelTimer) {\n      clearTimeout(this.cancelTimer);\n      this.cancelTimer = undefined;\n    }\n  }\n\n  /**\n   * @private\n   */\n  clearRequestTimer() {\n    if (this.requestTimer) {\n      clearTimeout(this.requestTimer);\n      this.requestTimer = undefined;\n    }\n  }\n\n  /**\n   * @private\n   */\n  clearRetryTimer() {\n    if (this.retryTimer) {\n      clearTimeout(this.retryTimer);\n      this.retryTimer = undefined;\n    }\n  }\n\n  /**\n   * @private\n   */\n  transitionTo(newState: State) {\n    if (this.state === newState) {\n      this.debug.log('State is already ' + newState.name);\n      return;\n    }\n\n    if (this.state && this.state.exit) {\n      this.state.exit.call(this, newState);\n    }\n\n    this.debug.log('State change: ' + (this.state ? this.state.name : 'undefined') + ' -> ' + newState.name);\n    this.state = newState;\n\n    if (this.state.enter) {\n      this.state.enter.apply(this);\n    }\n  }\n\n  /**\n   * @private\n   */\n  getEventHandler<T extends keyof State['events']>(eventName: T): NonNullable<State['events'][T]> {\n    const handler = this.state.events[eventName];\n\n    if (!handler) {\n      throw new Error(`No event '${eventName}' in state '${this.state.name}'`);\n    }\n\n    return handler!;\n  }\n\n  /**\n   * @private\n   */\n  dispatchEvent<T extends keyof State['events']>(eventName: T, ...args: Parameters<NonNullable<State['events'][T]>>) {\n    const handler = this.state.events[eventName] as ((this: Connection, ...args: any[]) => void) | undefined;\n    if (handler) {\n      handler.apply(this, args);\n    } else {\n      this.emit('error', new Error(`No event '${eventName}' in state '${this.state.name}'`));\n      this.close();\n    }\n  }\n\n  /**\n   * @private\n   */\n  socketError(error: Error) {\n    if (this.state === this.STATE.CONNECTING || this.state === this.STATE.SENT_TLSSSLNEGOTIATION) {\n      const hostPostfix = this.config.options.port ? `:${this.config.options.port}` : `\\\\${this.config.options.instanceName}`;\n      // If we have routing data stored, this connection has been redirected\n      const server = this.routingData ? this.routingData.server : this.config.server;\n      const port = this.routingData ? `:${this.routingData.port}` : hostPostfix;\n      // Grab the target host from the connection configuration, and from a redirect message\n      // otherwise, leave the message empty.\n      const routingMessage = this.routingData ? ` (redirected from ${this.config.server}${hostPostfix})` : '';\n      const message = `Failed to connect to ${server}${port}${routingMessage} - ${error.message}`;\n      this.debug.log(message);\n      this.emit('connect', new ConnectionError(message, 'ESOCKET'));\n    } else {\n      const message = `Connection lost - ${error.message}`;\n      this.debug.log(message);\n      this.emit('error', new ConnectionError(message, 'ESOCKET'));\n    }\n    this.dispatchEvent('socketError', error);\n  }\n\n  /**\n   * @private\n   */\n  socketEnd() {\n    this.debug.log('socket ended');\n    if (this.state !== this.STATE.FINAL) {\n      const error: ErrorWithCode = new Error('socket hang up');\n      error.code = 'ECONNRESET';\n      this.socketError(error);\n    }\n  }\n\n  /**\n   * @private\n   */\n  socketClose() {\n    this.debug.log('connection to ' + this.config.server + ':' + this.config.options.port + ' closed');\n    if (this.state === this.STATE.REROUTING) {\n      this.debug.log('Rerouting to ' + this.routingData!.server + ':' + this.routingData!.port);\n\n      this.dispatchEvent('reconnect');\n    } else if (this.state === this.STATE.TRANSIENT_FAILURE_RETRY) {\n      const server = this.routingData ? this.routingData.server : this.config.server;\n      const port = this.routingData ? this.routingData.port : this.config.options.port;\n      this.debug.log('Retry after transient failure connecting to ' + server + ':' + port);\n\n      this.dispatchEvent('retry');\n    } else {\n      this.transitionTo(this.STATE.FINAL);\n    }\n  }\n\n  /**\n   * @private\n   */\n  sendPreLogin() {\n    const [, major, minor, build] = /^(\\d+)\\.(\\d+)\\.(\\d+)/.exec(version) ?? ['0.0.0', '0', '0', '0'];\n    const payload = new PreloginPayload({\n      // If encrypt setting is set to 'strict', then we should have already done the encryption before calling\n      // this function. Therefore, the encrypt will be set to false here.\n      // Otherwise, we will set encrypt here based on the encrypt Boolean value from the configuration.\n      encrypt: typeof this.config.options.encrypt === 'boolean' && this.config.options.encrypt,\n      version: { major: Number(major), minor: Number(minor), build: Number(build), subbuild: 0 }\n    });\n\n    this.messageIo.sendMessage(TYPE.PRELOGIN, payload.data);\n    this.debug.payload(function() {\n      return payload.toString('  ');\n    });\n  }\n\n  /**\n   * @private\n   */\n  sendLogin7Packet() {\n    const payload = new Login7Payload({\n      tdsVersion: versions[this.config.options.tdsVersion],\n      packetSize: this.config.options.packetSize,\n      clientProgVer: 0,\n      clientPid: process.pid,\n      connectionId: 0,\n      clientTimeZone: new Date().getTimezoneOffset(),\n      clientLcid: 0x00000409\n    });\n\n    const { authentication } = this.config;\n    switch (authentication.type) {\n      case 'azure-active-directory-password':\n        payload.fedAuth = {\n          type: 'ADAL',\n          echo: this.fedAuthRequired,\n          workflow: 'default'\n        };\n        break;\n\n      case 'azure-active-directory-access-token':\n        payload.fedAuth = {\n          type: 'SECURITYTOKEN',\n          echo: this.fedAuthRequired,\n          fedAuthToken: authentication.options.token\n        };\n        break;\n\n      case 'azure-active-directory-msi-vm':\n      case 'azure-active-directory-default':\n      case 'azure-active-directory-msi-app-service':\n      case 'azure-active-directory-service-principal-secret':\n        payload.fedAuth = {\n          type: 'ADAL',\n          echo: this.fedAuthRequired,\n          workflow: 'integrated'\n        };\n        break;\n\n      case 'ntlm':\n        payload.sspi = createNTLMRequest({ domain: authentication.options.domain });\n        break;\n\n      default:\n        payload.userName = authentication.options.userName;\n        payload.password = authentication.options.password;\n    }\n\n    payload.hostname = this.config.options.workstationId || os.hostname();\n    payload.serverName = this.routingData ? this.routingData.server : this.config.server;\n    payload.appName = this.config.options.appName || 'Tedious';\n    payload.libraryName = libraryName;\n    payload.language = this.config.options.language;\n    payload.database = this.config.options.database;\n    payload.clientId = Buffer.from([1, 2, 3, 4, 5, 6]);\n\n    payload.readOnlyIntent = this.config.options.readOnlyIntent;\n    payload.initDbFatal = !this.config.options.fallbackToDefaultDb;\n\n    this.routingData = undefined;\n    this.messageIo.sendMessage(TYPE.LOGIN7, payload.toBuffer());\n\n    this.debug.payload(function() {\n      return payload.toString('  ');\n    });\n  }\n\n  /**\n   * @private\n   */\n  sendFedAuthTokenMessage(token: string) {\n    const accessTokenLen = Buffer.byteLength(token, 'ucs2');\n    const data = Buffer.alloc(8 + accessTokenLen);\n    let offset = 0;\n    offset = data.writeUInt32LE(accessTokenLen + 4, offset);\n    offset = data.writeUInt32LE(accessTokenLen, offset);\n    data.write(token, offset, 'ucs2');\n    this.messageIo.sendMessage(TYPE.FEDAUTH_TOKEN, data);\n    // sent the fedAuth token message, the rest is similar to standard login 7\n    this.transitionTo(this.STATE.SENT_LOGIN7_WITH_STANDARD_LOGIN);\n  }\n\n  /**\n   * @private\n   */\n  sendInitialSql() {\n    const payload = new SqlBatchPayload(this.getInitialSql(), this.currentTransactionDescriptor(), this.config.options);\n\n    const message = new Message({ type: TYPE.SQL_BATCH });\n    this.messageIo.outgoingMessageStream.write(message);\n    Readable.from(payload).pipe(message);\n  }\n\n  /**\n   * @private\n   */\n  getInitialSql() {\n    const options = [];\n\n    if (this.config.options.enableAnsiNull === true) {\n      options.push('set ansi_nulls on');\n    } else if (this.config.options.enableAnsiNull === false) {\n      options.push('set ansi_nulls off');\n    }\n\n    if (this.config.options.enableAnsiNullDefault === true) {\n      options.push('set ansi_null_dflt_on on');\n    } else if (this.config.options.enableAnsiNullDefault === false) {\n      options.push('set ansi_null_dflt_on off');\n    }\n\n    if (this.config.options.enableAnsiPadding === true) {\n      options.push('set ansi_padding on');\n    } else if (this.config.options.enableAnsiPadding === false) {\n      options.push('set ansi_padding off');\n    }\n\n    if (this.config.options.enableAnsiWarnings === true) {\n      options.push('set ansi_warnings on');\n    } else if (this.config.options.enableAnsiWarnings === false) {\n      options.push('set ansi_warnings off');\n    }\n\n    if (this.config.options.enableArithAbort === true) {\n      options.push('set arithabort on');\n    } else if (this.config.options.enableArithAbort === false) {\n      options.push('set arithabort off');\n    }\n\n    if (this.config.options.enableConcatNullYieldsNull === true) {\n      options.push('set concat_null_yields_null on');\n    } else if (this.config.options.enableConcatNullYieldsNull === false) {\n      options.push('set concat_null_yields_null off');\n    }\n\n    if (this.config.options.enableCursorCloseOnCommit === true) {\n      options.push('set cursor_close_on_commit on');\n    } else if (this.config.options.enableCursorCloseOnCommit === false) {\n      options.push('set cursor_close_on_commit off');\n    }\n\n    if (this.config.options.datefirst !== null) {\n      options.push(`set datefirst ${this.config.options.datefirst}`);\n    }\n\n    if (this.config.options.dateFormat !== null) {\n      options.push(`set dateformat ${this.config.options.dateFormat}`);\n    }\n\n    if (this.config.options.enableImplicitTransactions === true) {\n      options.push('set implicit_transactions on');\n    } else if (this.config.options.enableImplicitTransactions === false) {\n      options.push('set implicit_transactions off');\n    }\n\n    if (this.config.options.language !== null) {\n      options.push(`set language ${this.config.options.language}`);\n    }\n\n    if (this.config.options.enableNumericRoundabort === true) {\n      options.push('set numeric_roundabort on');\n    } else if (this.config.options.enableNumericRoundabort === false) {\n      options.push('set numeric_roundabort off');\n    }\n\n    if (this.config.options.enableQuotedIdentifier === true) {\n      options.push('set quoted_identifier on');\n    } else if (this.config.options.enableQuotedIdentifier === false) {\n      options.push('set quoted_identifier off');\n    }\n\n    if (this.config.options.textsize !== null) {\n      options.push(`set textsize ${this.config.options.textsize}`);\n    }\n\n    if (this.config.options.connectionIsolationLevel !== null) {\n      options.push(`set transaction isolation level ${this.getIsolationLevelText(this.config.options.connectionIsolationLevel)}`);\n    }\n\n    if (this.config.options.abortTransactionOnError === true) {\n      options.push('set xact_abort on');\n    } else if (this.config.options.abortTransactionOnError === false) {\n      options.push('set xact_abort off');\n    }\n\n    return options.join('\\n');\n  }\n\n  /**\n   * @private\n   */\n  processedInitialSql() {\n    this.clearConnectTimer();\n    this.emit('connect');\n  }\n\n  /**\n   * Execute the SQL batch represented by [[Request]].\n   * There is no param support, and unlike [[Request.execSql]],\n   * it is not likely that SQL Server will reuse the execution plan it generates for the SQL.\n   *\n   * In almost all cases, [[Request.execSql]] will be a better choice.\n   *\n   * @param request A [[Request]] object representing the request.\n   */\n  execSqlBatch(request: Request) {\n    this.makeRequest(request, TYPE.SQL_BATCH, new SqlBatchPayload(request.sqlTextOrProcedure!, this.currentTransactionDescriptor(), this.config.options));\n  }\n\n  /**\n   *  Execute the SQL represented by [[Request]].\n   *\n   * As `sp_executesql` is used to execute the SQL, if the same SQL is executed multiples times\n   * using this function, the SQL Server query optimizer is likely to reuse the execution plan it generates\n   * for the first execution. This may also result in SQL server treating the request like a stored procedure\n   * which can result in the [[Event_doneInProc]] or [[Event_doneProc]] events being emitted instead of the\n   * [[Event_done]] event you might expect. Using [[execSqlBatch]] will prevent this from occurring but may have a negative performance impact.\n   *\n   * Beware of the way that scoping rules apply, and how they may [affect local temp tables](http://weblogs.sqlteam.com/mladenp/archive/2006/11/03/17197.aspx)\n   * If you're running in to scoping issues, then [[execSqlBatch]] may be a better choice.\n   * See also [issue #24](https://github.com/pekim/tedious/issues/24)\n   *\n   * @param request A [[Request]] object representing the request.\n   */\n  execSql(request: Request) {\n    try {\n      request.validateParameters(this.databaseCollation);\n    } catch (error: any) {\n      request.error = error;\n\n      process.nextTick(() => {\n        this.debug.log(error.message);\n        request.callback(error);\n      });\n\n      return;\n    }\n\n    const parameters: Parameter[] = [];\n\n    parameters.push({\n      type: TYPES.NVarChar,\n      name: 'statement',\n      value: request.sqlTextOrProcedure,\n      output: false,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    if (request.parameters.length) {\n      parameters.push({\n        type: TYPES.NVarChar,\n        name: 'params',\n        value: request.makeParamsParameter(request.parameters),\n        output: false,\n        length: undefined,\n        precision: undefined,\n        scale: undefined\n      });\n\n      parameters.push(...request.parameters);\n    }\n\n    this.makeRequest(request, TYPE.RPC_REQUEST, new RpcRequestPayload(Procedures.Sp_ExecuteSql, parameters, this.currentTransactionDescriptor(), this.config.options, this.databaseCollation));\n  }\n\n  /**\n   * Creates a new BulkLoad instance.\n   *\n   * @param table The name of the table to bulk-insert into.\n   * @param options A set of bulk load options.\n   */\n  newBulkLoad(table: string, callback: BulkLoadCallback): BulkLoad\n  newBulkLoad(table: string, options: BulkLoadOptions, callback: BulkLoadCallback): BulkLoad\n  newBulkLoad(table: string, callbackOrOptions: BulkLoadOptions | BulkLoadCallback, callback?: BulkLoadCallback) {\n    let options: BulkLoadOptions;\n\n    if (callback === undefined) {\n      callback = callbackOrOptions as BulkLoadCallback;\n      options = {};\n    } else {\n      options = callbackOrOptions as BulkLoadOptions;\n    }\n\n    if (typeof options !== 'object') {\n      throw new TypeError('\"options\" argument must be an object');\n    }\n    return new BulkLoad(table, this.databaseCollation, this.config.options, options, callback);\n  }\n\n  /**\n   * Execute a [[BulkLoad]].\n   *\n   * ```js\n   * // We want to perform a bulk load into a table with the following format:\n   * // CREATE TABLE employees (first_name nvarchar(255), last_name nvarchar(255), day_of_birth date);\n   *\n   * const bulkLoad = connection.newBulkLoad('employees', (err, rowCount) => {\n   *   // ...\n   * });\n   *\n   * // First, we need to specify the columns that we want to write to,\n   * // and their definitions. These definitions must match the actual table,\n   * // otherwise the bulk load will fail.\n   * bulkLoad.addColumn('first_name', TYPES.NVarchar, { nullable: false });\n   * bulkLoad.addColumn('last_name', TYPES.NVarchar, { nullable: false });\n   * bulkLoad.addColumn('date_of_birth', TYPES.Date, { nullable: false });\n   *\n   * // Execute a bulk load with a predefined list of rows.\n   * //\n   * // Note that these rows are held in memory until the\n   * // bulk load was performed, so if you need to write a large\n   * // number of rows (e.g. by reading from a CSV file),\n   * // passing an `AsyncIterable` is advisable to keep memory usage low.\n   * connection.execBulkLoad(bulkLoad, [\n   *   { 'first_name': 'Steve', 'last_name': 'Jobs', 'day_of_birth': new Date('02-24-1955') },\n   *   { 'first_name': 'Bill', 'last_name': 'Gates', 'day_of_birth': new Date('10-28-1955') }\n   * ]);\n   * ```\n   *\n   * @param bulkLoad A previously created [[BulkLoad]].\n   * @param rows A [[Iterable]] or [[AsyncIterable]] that contains the rows that should be bulk loaded.\n   */\n  execBulkLoad(bulkLoad: BulkLoad, rows: AsyncIterable<unknown[] | { [columnName: string]: unknown }> | Iterable<unknown[] | { [columnName: string]: unknown }>): void\n\n  execBulkLoad(bulkLoad: BulkLoad, rows?: AsyncIterable<unknown[] | { [columnName: string]: unknown }> | Iterable<unknown[] | { [columnName: string]: unknown }>) {\n    bulkLoad.executionStarted = true;\n\n    if (rows) {\n      if (bulkLoad.streamingMode) {\n        throw new Error(\"Connection.execBulkLoad can't be called with a BulkLoad that was put in streaming mode.\");\n      }\n\n      if (bulkLoad.firstRowWritten) {\n        throw new Error(\"Connection.execBulkLoad can't be called with a BulkLoad that already has rows written to it.\");\n      }\n\n      const rowStream = Readable.from(rows);\n\n      // Destroy the packet transform if an error happens in the row stream,\n      // e.g. if an error is thrown from within a generator or stream.\n      rowStream.on('error', (err) => {\n        bulkLoad.rowToPacketTransform.destroy(err);\n      });\n\n      // Destroy the row stream if an error happens in the packet transform,\n      // e.g. if the bulk load is cancelled.\n      bulkLoad.rowToPacketTransform.on('error', (err) => {\n        rowStream.destroy(err);\n      });\n\n      rowStream.pipe(bulkLoad.rowToPacketTransform);\n    } else if (!bulkLoad.streamingMode) {\n      // If the bulkload was not put into streaming mode by the user,\n      // we end the rowToPacketTransform here for them.\n      //\n      // If it was put into streaming mode, it's the user's responsibility\n      // to end the stream.\n      bulkLoad.rowToPacketTransform.end();\n    }\n\n    const onCancel = () => {\n      request.cancel();\n    };\n\n    const payload = new BulkLoadPayload(bulkLoad);\n\n    const request = new Request(bulkLoad.getBulkInsertSql(), (error: (Error & { code?: string }) | null | undefined) => {\n      bulkLoad.removeListener('cancel', onCancel);\n\n      if (error) {\n        if (error.code === 'UNKNOWN') {\n          error.message += ' This is likely because the schema of the BulkLoad does not match the schema of the table you are attempting to insert into.';\n        }\n        bulkLoad.error = error;\n        bulkLoad.callback(error);\n        return;\n      }\n\n      this.makeRequest(bulkLoad, TYPE.BULK_LOAD, payload);\n    });\n\n    bulkLoad.once('cancel', onCancel);\n\n    this.execSqlBatch(request);\n  }\n\n  /**\n   * Prepare the SQL represented by the request.\n   *\n   * The request can then be used in subsequent calls to\n   * [[execute]] and [[unprepare]]\n   *\n   * @param request A [[Request]] object representing the request.\n   *   Parameters only require a name and type. Parameter values are ignored.\n   */\n  prepare(request: Request) {\n    const parameters: Parameter[] = [];\n\n    parameters.push({\n      type: TYPES.Int,\n      name: 'handle',\n      value: undefined,\n      output: true,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    parameters.push({\n      type: TYPES.NVarChar,\n      name: 'params',\n      value: request.parameters.length ? request.makeParamsParameter(request.parameters) : null,\n      output: false,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    parameters.push({\n      type: TYPES.NVarChar,\n      name: 'stmt',\n      value: request.sqlTextOrProcedure,\n      output: false,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    request.preparing = true;\n\n    // TODO: We need to clean up this event handler, otherwise this leaks memory\n    request.on('returnValue', (name: string, value: any) => {\n      if (name === 'handle') {\n        request.handle = value;\n      } else {\n        request.error = new RequestError(`Tedious > Unexpected output parameter ${name} from sp_prepare`);\n      }\n    });\n\n    this.makeRequest(request, TYPE.RPC_REQUEST, new RpcRequestPayload(Procedures.Sp_Prepare, parameters, this.currentTransactionDescriptor(), this.config.options, this.databaseCollation));\n  }\n\n  /**\n   * Release the SQL Server resources associated with a previously prepared request.\n   *\n   * @param request A [[Request]] object representing the request.\n   *   Parameters only require a name and type.\n   *   Parameter values are ignored.\n   */\n  unprepare(request: Request) {\n    const parameters: Parameter[] = [];\n\n    parameters.push({\n      type: TYPES.Int,\n      name: 'handle',\n      // TODO: Abort if `request.handle` is not set\n      value: request.handle,\n      output: false,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    this.makeRequest(request, TYPE.RPC_REQUEST, new RpcRequestPayload(Procedures.Sp_Unprepare, parameters, this.currentTransactionDescriptor(), this.config.options, this.databaseCollation));\n  }\n\n  /**\n   * Execute previously prepared SQL, using the supplied parameters.\n   *\n   * @param request A previously prepared [[Request]].\n   * @param parameters  An object whose names correspond to the names of\n   *   parameters that were added to the [[Request]] before it was prepared.\n   *   The object's values are passed as the parameters' values when the\n   *   request is executed.\n   */\n  execute(request: Request, parameters?: { [key: string]: unknown }) {\n    const executeParameters: Parameter[] = [];\n\n    executeParameters.push({\n      type: TYPES.Int,\n      name: '',\n      // TODO: Abort if `request.handle` is not set\n      value: request.handle,\n      output: false,\n      length: undefined,\n      precision: undefined,\n      scale: undefined\n    });\n\n    try {\n      for (let i = 0, len = request.parameters.length; i < len; i++) {\n        const parameter = request.parameters[i];\n\n        executeParameters.push({\n          ...parameter,\n          value: parameter.type.validate(parameters ? parameters[parameter.name] : null, this.databaseCollation)\n        });\n      }\n    } catch (error: any) {\n      request.error = error;\n\n      process.nextTick(() => {\n        this.debug.log(error.message);\n        request.callback(error);\n      });\n\n      return;\n    }\n\n    this.makeRequest(request, TYPE.RPC_REQUEST, new RpcRequestPayload(Procedures.Sp_Execute, executeParameters, this.currentTransactionDescriptor(), this.config.options, this.databaseCollation));\n  }\n\n  /**\n   * Call a stored procedure represented by [[Request]].\n   *\n   * @param request A [[Request]] object representing the request.\n   */\n  callProcedure(request: Request) {\n    try {\n      request.validateParameters(this.databaseCollation);\n    } catch (error: any) {\n      request.error = error;\n\n      process.nextTick(() => {\n        this.debug.log(error.message);\n        request.callback(error);\n      });\n\n      return;\n    }\n\n    this.makeRequest(request, TYPE.RPC_REQUEST, new RpcRequestPayload(request.sqlTextOrProcedure!, request.parameters, this.currentTransactionDescriptor(), this.config.options, this.databaseCollation));\n  }\n\n  /**\n   * Start a transaction.\n   *\n   * @param callback\n   * @param name A string representing a name to associate with the transaction.\n   *   Optional, and defaults to an empty string. Required when `isolationLevel`\n   *   is present.\n   * @param isolationLevel The isolation level that the transaction is to be run with.\n   *\n   *   The isolation levels are available from `require('tedious').ISOLATION_LEVEL`.\n   *   * `READ_UNCOMMITTED`\n   *   * `READ_COMMITTED`\n   *   * `REPEATABLE_READ`\n   *   * `SERIALIZABLE`\n   *   * `SNAPSHOT`\n   *\n   *   Optional, and defaults to the Connection's isolation level.\n   */\n  beginTransaction(callback: BeginTransactionCallback, name = '', isolationLevel = this.config.options.isolationLevel) {\n    assertValidIsolationLevel(isolationLevel, 'isolationLevel');\n\n    const transaction = new Transaction(name, isolationLevel);\n\n    if (this.config.options.tdsVersion < '7_2') {\n      return this.execSqlBatch(new Request('SET TRANSACTION ISOLATION LEVEL ' + (transaction.isolationLevelToTSQL()) + ';BEGIN TRAN ' + transaction.name, (err) => {\n        this.transactionDepth++;\n        if (this.transactionDepth === 1) {\n          this.inTransaction = true;\n        }\n        callback(err);\n      }));\n    }\n\n    const request = new Request(undefined, (err) => {\n      return callback(err, this.currentTransactionDescriptor());\n    });\n    return this.makeRequest(request, TYPE.TRANSACTION_MANAGER, transaction.beginPayload(this.currentTransactionDescriptor()));\n  }\n\n  /**\n   * Commit a transaction.\n   *\n   * There should be an active transaction - that is, [[beginTransaction]]\n   * should have been previously called.\n   *\n   * @param callback\n   * @param name A string representing a name to associate with the transaction.\n   *   Optional, and defaults to an empty string. Required when `isolationLevel`is present.\n   */\n  commitTransaction(callback: CommitTransactionCallback, name = '') {\n    const transaction = new Transaction(name);\n    if (this.config.options.tdsVersion < '7_2') {\n      return this.execSqlBatch(new Request('COMMIT TRAN ' + transaction.name, (err) => {\n        this.transactionDepth--;\n        if (this.transactionDepth === 0) {\n          this.inTransaction = false;\n        }\n\n        callback(err);\n      }));\n    }\n    const request = new Request(undefined, callback);\n    return this.makeRequest(request, TYPE.TRANSACTION_MANAGER, transaction.commitPayload(this.currentTransactionDescriptor()));\n  }\n\n  /**\n   * Rollback a transaction.\n   *\n   * There should be an active transaction - that is, [[beginTransaction]]\n   * should have been previously called.\n   *\n   * @param callback\n   * @param name A string representing a name to associate with the transaction.\n   *   Optional, and defaults to an empty string.\n   *   Required when `isolationLevel` is present.\n   */\n  rollbackTransaction(callback: RollbackTransactionCallback, name = '') {\n    const transaction = new Transaction(name);\n    if (this.config.options.tdsVersion < '7_2') {\n      return this.execSqlBatch(new Request('ROLLBACK TRAN ' + transaction.name, (err) => {\n        this.transactionDepth--;\n        if (this.transactionDepth === 0) {\n          this.inTransaction = false;\n        }\n        callback(err);\n      }));\n    }\n    const request = new Request(undefined, callback);\n    return this.makeRequest(request, TYPE.TRANSACTION_MANAGER, transaction.rollbackPayload(this.currentTransactionDescriptor()));\n  }\n\n  /**\n   * Set a savepoint within a transaction.\n   *\n   * There should be an active transaction - that is, [[beginTransaction]]\n   * should have been previously called.\n   *\n   * @param callback\n   * @param name A string representing a name to associate with the transaction.\\\n   *   Optional, and defaults to an empty string.\n   *   Required when `isolationLevel` is present.\n   */\n  saveTransaction(callback: SaveTransactionCallback, name: string) {\n    const transaction = new Transaction(name);\n    if (this.config.options.tdsVersion < '7_2') {\n      return this.execSqlBatch(new Request('SAVE TRAN ' + transaction.name, (err) => {\n        this.transactionDepth++;\n        callback(err);\n      }));\n    }\n    const request = new Request(undefined, callback);\n    return this.makeRequest(request, TYPE.TRANSACTION_MANAGER, transaction.savePayload(this.currentTransactionDescriptor()));\n  }\n\n  /**\n   * Run the given callback after starting a transaction, and commit or\n   * rollback the transaction afterwards.\n   *\n   * This is a helper that employs [[beginTransaction]], [[commitTransaction]],\n   * [[rollbackTransaction]], and [[saveTransaction]] to greatly simplify the\n   * use of database transactions and automatically handle transaction nesting.\n   *\n   * @param cb\n   * @param isolationLevel\n   *   The isolation level that the transaction is to be run with.\n   *\n   *   The isolation levels are available from `require('tedious').ISOLATION_LEVEL`.\n   *   * `READ_UNCOMMITTED`\n   *   * `READ_COMMITTED`\n   *   * `REPEATABLE_READ`\n   *   * `SERIALIZABLE`\n   *   * `SNAPSHOT`\n   *\n   *   Optional, and defaults to the Connection's isolation level.\n   */\n  transaction(cb: (err: Error | null | undefined, txDone?: <T extends TransactionDoneCallback>(err: Error | null | undefined, done: T, ...args: CallbackParameters<T>) => void) => void, isolationLevel?: typeof ISOLATION_LEVEL[keyof typeof ISOLATION_LEVEL]) {\n    if (typeof cb !== 'function') {\n      throw new TypeError('`cb` must be a function');\n    }\n\n    const useSavepoint = this.inTransaction;\n    const name = '_tedious_' + (crypto.randomBytes(10).toString('hex'));\n    const txDone: <T extends TransactionDoneCallback>(err: Error | null | undefined, done: T, ...args: CallbackParameters<T>) => void = (err, done, ...args) => {\n      if (err) {\n        if (this.inTransaction && this.state === this.STATE.LOGGED_IN) {\n          this.rollbackTransaction((txErr) => {\n            done(txErr || err, ...args);\n          }, name);\n        } else {\n          done(err, ...args);\n        }\n      } else if (useSavepoint) {\n        if (this.config.options.tdsVersion < '7_2') {\n          this.transactionDepth--;\n        }\n        done(null, ...args);\n      } else {\n        this.commitTransaction((txErr) => {\n          done(txErr, ...args);\n        }, name);\n      }\n    };\n\n    if (useSavepoint) {\n      return this.saveTransaction((err) => {\n        if (err) {\n          return cb(err);\n        }\n\n        if (isolationLevel) {\n          return this.execSqlBatch(new Request('SET transaction isolation level ' + this.getIsolationLevelText(isolationLevel), (err) => {\n            return cb(err, txDone);\n          }));\n        } else {\n          return cb(null, txDone);\n        }\n      }, name);\n    } else {\n      return this.beginTransaction((err) => {\n        if (err) {\n          return cb(err);\n        }\n\n        return cb(null, txDone);\n      }, name, isolationLevel);\n    }\n  }\n\n  /**\n   * @private\n   */\n  makeRequest(request: Request | BulkLoad, packetType: number, payload: (Iterable<Buffer> | AsyncIterable<Buffer>) & { toString: (indent?: string) => string }) {\n    if (this.state !== this.STATE.LOGGED_IN) {\n      const message = 'Requests can only be made in the ' + this.STATE.LOGGED_IN.name + ' state, not the ' + this.state.name + ' state';\n      this.debug.log(message);\n      request.callback(new RequestError(message, 'EINVALIDSTATE'));\n    } else if (request.canceled) {\n      process.nextTick(() => {\n        request.callback(new RequestError('Canceled.', 'ECANCEL'));\n      });\n    } else {\n      if (packetType === TYPE.SQL_BATCH) {\n        this.isSqlBatch = true;\n      } else {\n        this.isSqlBatch = false;\n      }\n\n      this.request = request;\n      request.connection! = this;\n      request.rowCount! = 0;\n      request.rows! = [];\n      request.rst! = [];\n\n      const onCancel = () => {\n        payloadStream.unpipe(message);\n        payloadStream.destroy(new RequestError('Canceled.', 'ECANCEL'));\n\n        // set the ignore bit and end the message.\n        message.ignore = true;\n        message.end();\n\n        if (request instanceof Request && request.paused) {\n          // resume the request if it was paused so we can read the remaining tokens\n          request.resume();\n        }\n      };\n\n      request.once('cancel', onCancel);\n\n      this.createRequestTimer();\n\n      const message = new Message({ type: packetType, resetConnection: this.resetConnectionOnNextRequest });\n      this.messageIo.outgoingMessageStream.write(message);\n      this.transitionTo(this.STATE.SENT_CLIENT_REQUEST);\n\n      message.once('finish', () => {\n        request.removeListener('cancel', onCancel);\n        request.once('cancel', this._cancelAfterRequestSent);\n\n        this.resetConnectionOnNextRequest = false;\n        this.debug.payload(function() {\n          return payload!.toString('  ');\n        });\n      });\n\n      const payloadStream = Readable.from(payload);\n      payloadStream.once('error', (error) => {\n        payloadStream.unpipe(message);\n\n        // Only set a request error if no error was set yet.\n        request.error ??= error;\n\n        message.ignore = true;\n        message.end();\n      });\n      payloadStream.pipe(message);\n    }\n  }\n\n  /**\n   * Cancel currently executed request.\n   */\n  cancel() {\n    if (!this.request) {\n      return false;\n    }\n\n    if (this.request.canceled) {\n      return false;\n    }\n\n    this.request.cancel();\n    return true;\n  }\n\n  /**\n   * Reset the connection to its initial state.\n   * Can be useful for connection pool implementations.\n   *\n   * @param callback\n   */\n  reset(callback: ResetCallback) {\n    const request = new Request(this.getInitialSql(), (err) => {\n      if (this.config.options.tdsVersion < '7_2') {\n        this.inTransaction = false;\n      }\n      callback(err);\n    });\n    this.resetConnectionOnNextRequest = true;\n    this.execSqlBatch(request);\n  }\n\n  /**\n   * @private\n   */\n  currentTransactionDescriptor() {\n    return this.transactionDescriptors[this.transactionDescriptors.length - 1];\n  }\n\n  /**\n   * @private\n   */\n  getIsolationLevelText(isolationLevel: typeof ISOLATION_LEVEL[keyof typeof ISOLATION_LEVEL]) {\n    switch (isolationLevel) {\n      case ISOLATION_LEVEL.READ_UNCOMMITTED:\n        return 'read uncommitted';\n      case ISOLATION_LEVEL.REPEATABLE_READ:\n        return 'repeatable read';\n      case ISOLATION_LEVEL.SERIALIZABLE:\n        return 'serializable';\n      case ISOLATION_LEVEL.SNAPSHOT:\n        return 'snapshot';\n      default:\n        return 'read committed';\n    }\n  }\n}\n\nfunction isTransientError(error: AggregateError | ConnectionError): boolean {\n  if (error instanceof AggregateError) {\n    error = error.errors[0];\n  }\n  return (error instanceof ConnectionError) && !!error.isTransient;\n}\n\nexport default Connection;\nmodule.exports = Connection;\n\nConnection.prototype.STATE = {\n  INITIALIZED: {\n    name: 'Initialized',\n    events: {}\n  },\n  CONNECTING: {\n    name: 'Connecting',\n    enter: function() {\n      this.initialiseConnection();\n    },\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  SENT_PRELOGIN: {\n    name: 'SentPrelogin',\n    enter: function() {\n      (async () => {\n        let messageBuffer = Buffer.alloc(0);\n\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n\n        for await (const data of message) {\n          messageBuffer = Buffer.concat([messageBuffer, data]);\n        }\n\n        const preloginPayload = new PreloginPayload(messageBuffer);\n        this.debug.payload(function() {\n          return preloginPayload.toString('  ');\n        });\n\n        if (preloginPayload.fedAuthRequired === 1) {\n          this.fedAuthRequired = true;\n        }\n        if ('strict' !== this.config.options.encrypt && (preloginPayload.encryptionString === 'ON' || preloginPayload.encryptionString === 'REQ')) {\n          if (!this.config.options.encrypt) {\n            this.emit('connect', new ConnectionError(\"Server requires encryption, set 'encrypt' config option to true.\", 'EENCRYPT'));\n            return this.close();\n          }\n\n          try {\n            this.transitionTo(this.STATE.SENT_TLSSSLNEGOTIATION);\n            await this.messageIo.startTls(this.secureContextOptions, this.config.options.serverName ? this.config.options.serverName : this.routingData?.server ?? this.config.server, this.config.options.trustServerCertificate);\n          } catch (err: any) {\n            return this.socketError(err);\n          }\n        }\n\n        this.sendLogin7Packet();\n\n        const { authentication } = this.config;\n\n        switch (authentication.type) {\n          case 'azure-active-directory-password':\n          case 'azure-active-directory-msi-vm':\n          case 'azure-active-directory-msi-app-service':\n          case 'azure-active-directory-service-principal-secret':\n          case 'azure-active-directory-default':\n            this.transitionTo(this.STATE.SENT_LOGIN7_WITH_FEDAUTH);\n            break;\n          case 'ntlm':\n            this.transitionTo(this.STATE.SENT_LOGIN7_WITH_NTLM);\n            break;\n          default:\n            this.transitionTo(this.STATE.SENT_LOGIN7_WITH_STANDARD_LOGIN);\n            break;\n        }\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  REROUTING: {\n    name: 'ReRouting',\n    enter: function() {\n      this.cleanupConnection(CLEANUP_TYPE.REDIRECT);\n    },\n    events: {\n      message: function() {\n      },\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      reconnect: function() {\n        this.transitionTo(this.STATE.CONNECTING);\n      }\n    }\n  },\n  TRANSIENT_FAILURE_RETRY: {\n    name: 'TRANSIENT_FAILURE_RETRY',\n    enter: function() {\n      this.curTransientRetryCount++;\n      this.cleanupConnection(CLEANUP_TYPE.RETRY);\n    },\n    events: {\n      message: function() {\n      },\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      retry: function() {\n        this.createRetryTimer();\n      }\n    }\n  },\n  SENT_TLSSSLNEGOTIATION: {\n    name: 'SentTLSSSLNegotiation',\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  SENT_LOGIN7_WITH_STANDARD_LOGIN: {\n    name: 'SentLogin7WithStandardLogin',\n    enter: function() {\n      (async () => {\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n\n        const handler = new Login7TokenHandler(this);\n        const tokenStreamParser = this.createTokenStreamParser(message, handler);\n\n        await once(tokenStreamParser, 'end');\n\n        if (handler.loginAckReceived) {\n          if (handler.routingData) {\n            this.routingData = handler.routingData;\n            this.transitionTo(this.STATE.REROUTING);\n          } else {\n            this.transitionTo(this.STATE.LOGGED_IN_SENDING_INITIAL_SQL);\n          }\n        } else if (this.loginError) {\n          if (isTransientError(this.loginError)) {\n            this.debug.log('Initiating retry on transient error');\n            this.transitionTo(this.STATE.TRANSIENT_FAILURE_RETRY);\n          } else {\n            this.emit('connect', this.loginError);\n            this.transitionTo(this.STATE.FINAL);\n          }\n        } else {\n          this.emit('connect', new ConnectionError('Login failed.', 'ELOGIN'));\n          this.transitionTo(this.STATE.FINAL);\n        }\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  SENT_LOGIN7_WITH_NTLM: {\n    name: 'SentLogin7WithNTLMLogin',\n    enter: function() {\n      (async () => {\n        while (true) {\n          let message;\n          try {\n            message = await this.messageIo.readMessage();\n          } catch (err: any) {\n            return this.socketError(err);\n          }\n\n          const handler = new Login7TokenHandler(this);\n          const tokenStreamParser = this.createTokenStreamParser(message, handler);\n\n          await once(tokenStreamParser, 'end');\n\n          if (handler.loginAckReceived) {\n            if (handler.routingData) {\n              this.routingData = handler.routingData;\n              return this.transitionTo(this.STATE.REROUTING);\n            } else {\n              return this.transitionTo(this.STATE.LOGGED_IN_SENDING_INITIAL_SQL);\n            }\n          } else if (this.ntlmpacket) {\n            const authentication = this.config.authentication as NtlmAuthentication;\n\n            const payload = new NTLMResponsePayload({\n              domain: authentication.options.domain,\n              userName: authentication.options.userName,\n              password: authentication.options.password,\n              ntlmpacket: this.ntlmpacket\n            });\n\n            this.messageIo.sendMessage(TYPE.NTLMAUTH_PKT, payload.data);\n            this.debug.payload(function() {\n              return payload.toString('  ');\n            });\n\n            this.ntlmpacket = undefined;\n          } else if (this.loginError) {\n            if (isTransientError(this.loginError)) {\n              this.debug.log('Initiating retry on transient error');\n              return this.transitionTo(this.STATE.TRANSIENT_FAILURE_RETRY);\n            } else {\n              this.emit('connect', this.loginError);\n              return this.transitionTo(this.STATE.FINAL);\n            }\n          } else {\n            this.emit('connect', new ConnectionError('Login failed.', 'ELOGIN'));\n            return this.transitionTo(this.STATE.FINAL);\n          }\n        }\n\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  SENT_LOGIN7_WITH_FEDAUTH: {\n    name: 'SentLogin7Withfedauth',\n    enter: function() {\n      (async () => {\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n\n        const handler = new Login7TokenHandler(this);\n        const tokenStreamParser = this.createTokenStreamParser(message, handler);\n        await once(tokenStreamParser, 'end');\n        if (handler.loginAckReceived) {\n          if (handler.routingData) {\n            this.routingData = handler.routingData;\n            this.transitionTo(this.STATE.REROUTING);\n          } else {\n            this.transitionTo(this.STATE.LOGGED_IN_SENDING_INITIAL_SQL);\n          }\n\n          return;\n        }\n\n        const fedAuthInfoToken = handler.fedAuthInfoToken;\n\n        if (fedAuthInfoToken && fedAuthInfoToken.stsurl && fedAuthInfoToken.spn) {\n          const authentication = this.config.authentication as AzureActiveDirectoryPasswordAuthentication | AzureActiveDirectoryMsiVmAuthentication | AzureActiveDirectoryMsiAppServiceAuthentication | AzureActiveDirectoryServicePrincipalSecret | AzureActiveDirectoryDefaultAuthentication;\n          const tokenScope = new URL('/.default', fedAuthInfoToken.spn).toString();\n\n          let credentials;\n\n          switch (authentication.type) {\n            case 'azure-active-directory-password':\n              credentials = new UsernamePasswordCredential(\n                authentication.options.tenantId ?? 'common',\n                authentication.options.clientId,\n                authentication.options.userName,\n                authentication.options.password\n              );\n              break;\n            case 'azure-active-directory-msi-vm':\n            case 'azure-active-directory-msi-app-service':\n              const msiArgs = authentication.options.clientId ? [authentication.options.clientId, {}] : [{}];\n              credentials = new ManagedIdentityCredential(...msiArgs);\n              break;\n            case 'azure-active-directory-default':\n              const args = authentication.options.clientId ? { managedIdentityClientId: authentication.options.clientId } : {};\n              credentials = new DefaultAzureCredential(args);\n              break;\n            case 'azure-active-directory-service-principal-secret':\n              credentials = new ClientSecretCredential(\n                authentication.options.tenantId,\n                authentication.options.clientId,\n                authentication.options.clientSecret\n              );\n              break;\n          }\n\n          let tokenResponse;\n          try {\n            tokenResponse = await credentials.getToken(tokenScope);\n          } catch (err) {\n            this.loginError = new AggregateError(\n              [new ConnectionError('Security token could not be authenticated or authorized.', 'EFEDAUTH'), err]);\n            this.emit('connect', this.loginError);\n            this.transitionTo(this.STATE.FINAL);\n            return;\n          }\n\n\n          const token = tokenResponse.token;\n          this.sendFedAuthTokenMessage(token);\n\n        } else if (this.loginError) {\n          if (isTransientError(this.loginError)) {\n            this.debug.log('Initiating retry on transient error');\n            this.transitionTo(this.STATE.TRANSIENT_FAILURE_RETRY);\n          } else {\n            this.emit('connect', this.loginError);\n            this.transitionTo(this.STATE.FINAL);\n          }\n        } else {\n          this.emit('connect', new ConnectionError('Login failed.', 'ELOGIN'));\n          this.transitionTo(this.STATE.FINAL);\n        }\n\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  LOGGED_IN_SENDING_INITIAL_SQL: {\n    name: 'LoggedInSendingInitialSql',\n    enter: function() {\n      (async () => {\n        this.sendInitialSql();\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n        const tokenStreamParser = this.createTokenStreamParser(message, new InitialSqlTokenHandler(this));\n        await once(tokenStreamParser, 'end');\n\n        this.transitionTo(this.STATE.LOGGED_IN);\n        this.processedInitialSql();\n\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function socketError() {\n        this.transitionTo(this.STATE.FINAL);\n      },\n      connectTimeout: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  LOGGED_IN: {\n    name: 'LoggedIn',\n    events: {\n      socketError: function() {\n        this.transitionTo(this.STATE.FINAL);\n      }\n    }\n  },\n  SENT_CLIENT_REQUEST: {\n    name: 'SentClientRequest',\n    enter: function() {\n      (async () => {\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n        // request timer is stopped on first data package\n        this.clearRequestTimer();\n\n        const tokenStreamParser = this.createTokenStreamParser(message, new RequestTokenHandler(this, this.request!));\n\n        // If the request was canceled and we have a `cancelTimer`\n        // defined, we send a attention message after the\n        // request message was fully sent off.\n        //\n        // We already started consuming the current message\n        // (but all the token handlers should be no-ops), and\n        // need to ensure the next message is handled by the\n        // `SENT_ATTENTION` state.\n        if (this.request?.canceled && this.cancelTimer) {\n          return this.transitionTo(this.STATE.SENT_ATTENTION);\n        }\n\n        const onResume = () => {\n          tokenStreamParser.resume();\n        };\n        const onPause = () => {\n          tokenStreamParser.pause();\n\n          this.request?.once('resume', onResume);\n        };\n\n        this.request?.on('pause', onPause);\n\n        if (this.request instanceof Request && this.request.paused) {\n          onPause();\n        }\n\n        const onCancel = () => {\n          tokenStreamParser.removeListener('end', onEndOfMessage);\n\n          if (this.request instanceof Request && this.request.paused) {\n            // resume the request if it was paused so we can read the remaining tokens\n            this.request.resume();\n          }\n\n          this.request?.removeListener('pause', onPause);\n          this.request?.removeListener('resume', onResume);\n\n          // The `_cancelAfterRequestSent` callback will have sent a\n          // attention message, so now we need to also switch to\n          // the `SENT_ATTENTION` state to make sure the attention ack\n          // message is processed correctly.\n          this.transitionTo(this.STATE.SENT_ATTENTION);\n        };\n\n        const onEndOfMessage = () => {\n          this.request?.removeListener('cancel', this._cancelAfterRequestSent);\n          this.request?.removeListener('cancel', onCancel);\n          this.request?.removeListener('pause', onPause);\n          this.request?.removeListener('resume', onResume);\n\n          this.transitionTo(this.STATE.LOGGED_IN);\n          const sqlRequest = this.request as Request;\n          this.request = undefined;\n          if (this.config.options.tdsVersion < '7_2' && sqlRequest.error && this.isSqlBatch) {\n            this.inTransaction = false;\n          }\n          sqlRequest.callback(sqlRequest.error, sqlRequest.rowCount, sqlRequest.rows);\n        };\n\n        tokenStreamParser.once('end', onEndOfMessage);\n        this.request?.once('cancel', onCancel);\n      })();\n\n    },\n    exit: function(nextState) {\n      this.clearRequestTimer();\n    },\n    events: {\n      socketError: function(err) {\n        const sqlRequest = this.request!;\n        this.request = undefined;\n        this.transitionTo(this.STATE.FINAL);\n\n        sqlRequest.callback(err);\n      }\n    }\n  },\n  SENT_ATTENTION: {\n    name: 'SentAttention',\n    enter: function() {\n      (async () => {\n        let message;\n        try {\n          message = await this.messageIo.readMessage();\n        } catch (err: any) {\n          return this.socketError(err);\n        }\n\n        const handler = new AttentionTokenHandler(this, this.request!);\n        const tokenStreamParser = this.createTokenStreamParser(message, handler);\n\n        await once(tokenStreamParser, 'end');\n        // 3.2.5.7 Sent Attention State\n        // Discard any data contained in the response, until we receive the attention response\n        if (handler.attentionReceived) {\n          this.clearCancelTimer();\n\n          const sqlRequest = this.request!;\n          this.request = undefined;\n          this.transitionTo(this.STATE.LOGGED_IN);\n\n          if (sqlRequest.error && sqlRequest.error instanceof RequestError && sqlRequest.error.code === 'ETIMEOUT') {\n            sqlRequest.callback(sqlRequest.error);\n          } else {\n            sqlRequest.callback(new RequestError('Canceled.', 'ECANCEL'));\n          }\n        }\n\n      })().catch((err) => {\n        process.nextTick(() => {\n          throw err;\n        });\n      });\n    },\n    events: {\n      socketError: function(err) {\n        const sqlRequest = this.request!;\n        this.request = undefined;\n\n        this.transitionTo(this.STATE.FINAL);\n\n        sqlRequest.callback(err);\n      }\n    }\n  },\n  FINAL: {\n    name: 'Final',\n    enter: function() {\n      this.cleanupConnection(CLEANUP_TYPE.NORMAL);\n    },\n    events: {\n      connectTimeout: function() {\n        // Do nothing, as the timer should be cleaned up.\n      },\n      message: function() {\n        // Do nothing\n      },\n      socketError: function() {\n        // Do nothing\n      }\n    }\n  }\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,GAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,GAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,GAAA,GAAAD,uBAAA,CAAAH,OAAA;AACA,IAAAK,IAAA,GAAAN,sBAAA,CAAAC,OAAA;AAEA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AAGA,IAAAO,OAAA,GAAAP,OAAA;AAEA,IAAAQ,SAAA,GAAAR,OAAA;AAOA,IAAAS,SAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,MAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,eAAA,GAAAZ,OAAA;AACA,IAAAa,qBAAA,GAAAb,OAAA;AACA,IAAAc,OAAA,GAAAd,OAAA;AACA,IAAAe,gBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,cAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,YAAA,GAAAlB,sBAAA,CAAAC,OAAA;AACA,IAAAkB,QAAA,GAAAnB,sBAAA,CAAAC,OAAA;AACA,IAAAmB,kBAAA,GAAApB,sBAAA,CAAAC,OAAA;AACA,IAAAoB,gBAAA,GAAArB,sBAAA,CAAAC,OAAA;AACA,IAAAqB,UAAA,GAAAtB,sBAAA,CAAAC,OAAA;AACA,IAAAsB,kBAAA,GAAAtB,OAAA;AACA,IAAAuB,YAAA,GAAAvB,OAAA;AACA,IAAAwB,OAAA,GAAAxB,OAAA;AACA,IAAAyB,UAAA,GAAAzB,OAAA;AACA,IAAA0B,QAAA,GAAA1B,OAAA;AACA,IAAA2B,YAAA,GAAA3B,OAAA;AACA,IAAA4B,QAAA,GAAA7B,sBAAA,CAAAC,OAAA;AAEA,IAAA6B,KAAA,GAAA7B,OAAA;AAGA,IAAA8B,oBAAA,GAAA9B,OAAA;AACA,IAAA+B,SAAA,GAAA/B,OAAA;AACA,IAAAgC,gBAAA,GAAAhC,OAAA;AAEA,IAAAiC,uBAAA,GAAAlC,sBAAA,CAAAC,OAAA;AAEA,IAAAkC,iBAAA,GAAAnC,sBAAA,CAAAC,OAAA;AACA,IAAAmC,QAAA,GAAAnC,OAAA;AACA,IAAAoC,IAAA,GAAApC,OAAA;AACA,IAAAqC,QAAA,GAAArC,OAAA;AAAuI,SAAAsC,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAApC,wBAAAwC,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAlD,uBAAA4C,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAqEvI;;AA+BA;AACA;AACA;AACA,MAAMiB,wBAAwB,GAAG,EAAE,GAAG,IAAI;AAC1C;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,EAAE,GAAG,IAAI;AACzC;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,EAAE,GAAG,IAAI;AAChD;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC,GAAG,IAAI;AACvC;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,GAAG;AAC1C;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,CAAC,GAAG,IAAI;AACpC;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,UAAU;AACnC;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,CAAC;AAC3B;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAI;AACzB;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,KAAK;AACjC;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,YAAY;AACrC;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,KAAK;;AA2MhC;AACA;AACA;;AA0cA;AACA;AACA;AACA,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE;AACT,CAAC;AAOD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAASC,oBAAY,CAAC;EACpC;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAkBE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;EACEC,uBAAuB;;EAEvB;AACF;AACA;;EAGE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,MAA+B,EAAE;IAC3C,KAAK,CAAC,CAAC;IAEP,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjD,MAAM,IAAIC,SAAS,CAAC,+DAA+D,CAAC;IACtF;IAEA,IAAI,OAAOD,MAAM,CAACE,MAAM,KAAK,QAAQ,EAAE;MACrC,MAAM,IAAID,SAAS,CAAC,sEAAsE,CAAC;IAC7F;IAEA,IAAI,CAACE,eAAe,GAAG,KAAK;IAE5B,IAAIC,cAA0D;IAC9D,IAAIJ,MAAM,CAACI,cAAc,KAAKC,SAAS,EAAE;MACvC,IAAI,OAAOL,MAAM,CAACI,cAAc,KAAK,QAAQ,IAAIJ,MAAM,CAACI,cAAc,KAAK,IAAI,EAAE;QAC/E,MAAM,IAAIH,SAAS,CAAC,8DAA8D,CAAC;MACrF;MAEA,MAAMK,IAAI,GAAGN,MAAM,CAACI,cAAc,CAACE,IAAI;MACvC,MAAMC,OAAO,GAAGP,MAAM,CAACI,cAAc,CAACG,OAAO,KAAKF,SAAS,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACI,cAAc,CAACG,OAAO;MAEhG,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAM,IAAIL,SAAS,CAAC,mEAAmE,CAAC;MAC1F;MAEA,IAAIK,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,iCAAiC,IAAIA,IAAI,KAAK,qCAAqC,IAAIA,IAAI,KAAK,+BAA+B,IAAIA,IAAI,KAAK,wCAAwC,IAAIA,IAAI,KAAK,iDAAiD,IAAIA,IAAI,KAAK,gCAAgC,EAAE;QACrV,MAAM,IAAIL,SAAS,CAAC,kSAAkS,CAAC;MACzT;MAEA,IAAI,OAAOM,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;QACnD,MAAM,IAAIN,SAAS,CAAC,sEAAsE,CAAC;MAC7F;MAEA,IAAIK,IAAI,KAAK,MAAM,EAAE;QACnB,IAAI,OAAOC,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAE;UACtC,MAAM,IAAIP,SAAS,CAAC,6EAA6E,CAAC;QACpG;QAEA,IAAIM,OAAO,CAACE,QAAQ,KAAKJ,SAAS,IAAI,OAAOE,OAAO,CAACE,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIR,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAIM,OAAO,CAACG,QAAQ,KAAKL,SAAS,IAAI,OAAOE,OAAO,CAACG,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIT,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE;YACPE,QAAQ,EAAEF,OAAO,CAACE,QAAQ;YAC1BC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BF,MAAM,EAAED,OAAO,CAACC,MAAM,IAAID,OAAO,CAACC,MAAM,CAACG,WAAW,CAAC;UACvD;QACF,CAAC;MACH,CAAC,MAAM,IAAIL,IAAI,KAAK,iCAAiC,EAAE;QACrD,IAAI,OAAOC,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;UACxC,MAAM,IAAIX,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAIM,OAAO,CAACE,QAAQ,KAAKJ,SAAS,IAAI,OAAOE,OAAO,CAACE,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIR,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAIM,OAAO,CAACG,QAAQ,KAAKL,SAAS,IAAI,OAAOE,OAAO,CAACG,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIT,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAIM,OAAO,CAACM,QAAQ,KAAKR,SAAS,IAAI,OAAOE,OAAO,CAACM,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIZ,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,iCAAiC;UACvCC,OAAO,EAAE;YACPE,QAAQ,EAAEF,OAAO,CAACE,QAAQ;YAC1BC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BG,QAAQ,EAAEN,OAAO,CAACM,QAAQ;YAC1BD,QAAQ,EAAEL,OAAO,CAACK;UACpB;QACF,CAAC;MACH,CAAC,MAAM,IAAIN,IAAI,KAAK,qCAAqC,EAAE;QACzD,IAAI,OAAOC,OAAO,CAACO,KAAK,KAAK,QAAQ,EAAE;UACrC,MAAM,IAAIb,SAAS,CAAC,4EAA4E,CAAC;QACnG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,qCAAqC;UAC3CC,OAAO,EAAE;YACPO,KAAK,EAAEP,OAAO,CAACO;UACjB;QACF,CAAC;MACH,CAAC,MAAM,IAAIR,IAAI,KAAK,+BAA+B,EAAE;QACnD,IAAIC,OAAO,CAACK,QAAQ,KAAKP,SAAS,IAAI,OAAOE,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIX,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,+BAA+B;UACrCC,OAAO,EAAE;YACPK,QAAQ,EAAEL,OAAO,CAACK;UACpB;QACF,CAAC;MACH,CAAC,MAAM,IAAIN,IAAI,KAAK,gCAAgC,EAAE;QACpD,IAAIC,OAAO,CAACK,QAAQ,KAAKP,SAAS,IAAI,OAAOE,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIX,SAAS,CAAC,+EAA+E,CAAC;QACtG;QACAG,cAAc,GAAG;UACfE,IAAI,EAAE,gCAAgC;UACtCC,OAAO,EAAE;YACPK,QAAQ,EAAEL,OAAO,CAACK;UACpB;QACF,CAAC;MACH,CAAC,MAAM,IAAIN,IAAI,KAAK,wCAAwC,EAAE;QAC5D,IAAIC,OAAO,CAACK,QAAQ,KAAKP,SAAS,IAAI,OAAOE,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIX,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,wCAAwC;UAC9CC,OAAO,EAAE;YACPK,QAAQ,EAAEL,OAAO,CAACK;UACpB;QACF,CAAC;MACH,CAAC,MAAM,IAAIN,IAAI,KAAK,iDAAiD,EAAE;QACrE,IAAI,OAAOC,OAAO,CAACK,QAAQ,KAAK,QAAQ,EAAE;UACxC,MAAM,IAAIX,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAI,OAAOM,OAAO,CAACQ,YAAY,KAAK,QAAQ,EAAE;UAC5C,MAAM,IAAId,SAAS,CAAC,mFAAmF,CAAC;QAC1G;QAEA,IAAI,OAAOM,OAAO,CAACM,QAAQ,KAAK,QAAQ,EAAE;UACxC,MAAM,IAAIZ,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,iDAAiD;UACvDC,OAAO,EAAE;YACPK,QAAQ,EAAEL,OAAO,CAACK,QAAQ;YAC1BG,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCF,QAAQ,EAAEN,OAAO,CAACM;UACpB;QACF,CAAC;MACH,CAAC,MAAM;QACL,IAAIN,OAAO,CAACE,QAAQ,KAAKJ,SAAS,IAAI,OAAOE,OAAO,CAACE,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIR,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAIM,OAAO,CAACG,QAAQ,KAAKL,SAAS,IAAI,OAAOE,OAAO,CAACG,QAAQ,KAAK,QAAQ,EAAE;UAC1E,MAAM,IAAIT,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEAG,cAAc,GAAG;UACfE,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;YACPE,QAAQ,EAAEF,OAAO,CAACE,QAAQ;YAC1BC,QAAQ,EAAEH,OAAO,CAACG;UACpB;QACF,CAAC;MACH;IACF,CAAC,MAAM;MACLN,cAAc,GAAG;QACfE,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE;UACPE,QAAQ,EAAEJ,SAAS;UACnBK,QAAQ,EAAEL;QACZ;MACF,CAAC;IACH;IAEA,IAAI,CAACL,MAAM,GAAG;MACZE,MAAM,EAAEF,MAAM,CAACE,MAAM;MACrBE,cAAc,EAAEA,cAAc;MAC9BG,OAAO,EAAE;QACPS,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAEZ,SAAS;QAClBa,gBAAgB,EAAE,KAAK;QACvBC,aAAa,EAAEpC,sBAAsB;QACrCqC,2BAA2B,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAAG;QAClDC,uBAAuB,EAAE,KAAK;QAC9BC,kBAAkB,EAAEjB,SAAS;QAC7BkB,uBAAuB,EAAEvC,8BAA8B;QACvDwC,cAAc,EAAE3C,uBAAuB;QACvC4C,SAAS,EAAEpB,SAAS;QACpBqB,wBAAwB,EAAEC,4BAAe,CAACC,cAAc;QACxDC,wBAAwB,EAAE,CAAC,CAAC;QAC5BC,QAAQ,EAAEzB,SAAS;QACnB0B,SAAS,EAAE5C,iBAAiB;QAC5B6C,UAAU,EAAEzC,kBAAkB;QAC9B0C,KAAK,EAAE;UACLC,IAAI,EAAE,KAAK;UACXC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE,KAAK;UACdtB,KAAK,EAAE;QACT,CAAC;QACDuB,cAAc,EAAE,IAAI;QACpBC,qBAAqB,EAAE,IAAI;QAC3BC,iBAAiB,EAAE,IAAI;QACvBC,kBAAkB,EAAE,IAAI;QACxBC,gBAAgB,EAAE,IAAI;QACtBC,0BAA0B,EAAE,IAAI;QAChCC,yBAAyB,EAAE,IAAI;QAC/BC,0BAA0B,EAAE,KAAK;QACjCC,uBAAuB,EAAE,KAAK;QAC9BC,sBAAsB,EAAE,IAAI;QAC5BC,OAAO,EAAE,IAAI;QACbC,mBAAmB,EAAE,KAAK;QAC1BC,2BAA2B,EAAE5C,SAAS;QACtC6C,YAAY,EAAE7C,SAAS;QACvB8C,cAAc,EAAExB,4BAAe,CAACC,cAAc;QAC9CwB,QAAQ,EAAE9D,gBAAgB;QAC1B+D,YAAY,EAAEhD,SAAS;QACvBiD,2BAA2B,EAAE,CAAC;QAC9BC,mBAAmB,EAAE,KAAK;QAC1BC,UAAU,EAAEvE,mBAAmB;QAC/BwE,IAAI,EAAErE,YAAY;QAClBsE,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE7E,8BAA8B;QAC9C8E,mBAAmB,EAAE,KAAK;QAC1BC,gCAAgC,EAAE,KAAK;QACvCC,UAAU,EAAEzD,SAAS;QACrB0D,8BAA8B,EAAE,KAAK;QACrCC,UAAU,EAAE3E,mBAAmB;QAC/B4E,QAAQ,EAAE/E,gBAAgB;QAC1BgF,mBAAmB,EAAE7D,SAAS;QAC9B8D,sBAAsB,EAAE,KAAK;QAC7BC,cAAc,EAAE,KAAK;QACrBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAEjE,SAAS;QACxBkE,cAAc,EAAE;MAClB;IACF,CAAC;IAED,IAAIvE,MAAM,CAACO,OAAO,EAAE;MAClB,IAAIP,MAAM,CAACO,OAAO,CAACkD,IAAI,IAAIzD,MAAM,CAACO,OAAO,CAAC2C,YAAY,EAAE;QACtD,MAAM,IAAIsB,KAAK,CAAC,oDAAoD,GAAGxE,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAG,OAAO,GAAGzD,MAAM,CAACO,OAAO,CAAC2C,YAAY,GAAG,WAAW,CAAC;MACnJ;MAEA,IAAIlD,MAAM,CAACO,OAAO,CAACS,uBAAuB,KAAKX,SAAS,EAAE;QACxD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACS,uBAAuB,KAAK,SAAS,IAAIhB,MAAM,CAACO,OAAO,CAACS,uBAAuB,KAAK,IAAI,EAAE;UAClH,MAAM,IAAIf,SAAS,CAAC,uFAAuF,CAAC;QAC9G;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACS,uBAAuB,GAAGhB,MAAM,CAACO,OAAO,CAACS,uBAAuB;MACtF;MAEA,IAAIhB,MAAM,CAACO,OAAO,CAACU,OAAO,KAAKZ,SAAS,EAAE;QACxC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACU,OAAO,KAAK,QAAQ,EAAE;UAC9C,MAAM,IAAIhB,SAAS,CAAC,+DAA+D,CAAC;QACtF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACU,OAAO,GAAGjB,MAAM,CAACO,OAAO,CAACU,OAAO;MACtD;MAEA,IAAIjB,MAAM,CAACO,OAAO,CAACW,gBAAgB,KAAKb,SAAS,EAAE;QACjD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACW,gBAAgB,KAAK,SAAS,EAAE;UACxD,MAAM,IAAIjB,SAAS,CAAC,yEAAyE,CAAC;QAChG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACW,gBAAgB,GAAGlB,MAAM,CAACO,OAAO,CAACW,gBAAgB;MACxE;MAEA,IAAIlB,MAAM,CAACO,OAAO,CAACY,aAAa,KAAKd,SAAS,EAAE;QAC9C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACY,aAAa,KAAK,QAAQ,EAAE;UACpD,MAAM,IAAIlB,SAAS,CAAC,qEAAqE,CAAC;QAC5F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACY,aAAa,GAAGnB,MAAM,CAACO,OAAO,CAACY,aAAa;MAClE;MAEA,IAAInB,MAAM,CAACO,OAAO,CAACe,kBAAkB,EAAE;QACrC,IAAI,OAAOtB,MAAM,CAACO,OAAO,CAACe,kBAAkB,KAAK,UAAU,EAAE;UAC3D,MAAM,IAAIrB,SAAS,CAAC,uEAAuE,CAAC;QAC9F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACe,kBAAkB,GAAGtB,MAAM,CAACO,OAAO,CAACe,kBAAkB;MAC5E;MAEA,IAAItB,MAAM,CAACO,OAAO,CAACmB,wBAAwB,KAAKrB,SAAS,EAAE;QACzD,IAAAoE,sCAAyB,EAACzE,MAAM,CAACO,OAAO,CAACmB,wBAAwB,EAAE,yCAAyC,CAAC;QAE7G,IAAI,CAAC1B,MAAM,CAACO,OAAO,CAACmB,wBAAwB,GAAG1B,MAAM,CAACO,OAAO,CAACmB,wBAAwB;MACxF;MAEA,IAAI1B,MAAM,CAACO,OAAO,CAACiB,cAAc,KAAKnB,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACiB,cAAc,KAAK,QAAQ,EAAE;UACrD,MAAM,IAAIvB,SAAS,CAAC,sEAAsE,CAAC;QAC7F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACiB,cAAc,GAAGxB,MAAM,CAACO,OAAO,CAACiB,cAAc;MACpE;MAEA,IAAIxB,MAAM,CAACO,OAAO,CAACkB,SAAS,KAAKpB,SAAS,EAAE;QAC1C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACkB,SAAS,KAAK,UAAU,EAAE;UAClD,MAAM,IAAIxB,SAAS,CAAC,6DAA6D,CAAC;QACpF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACkB,SAAS,GAAGzB,MAAM,CAACO,OAAO,CAACkB,SAAS;MAC1D;MAEA,IAAIzB,MAAM,CAACO,OAAO,CAACsB,wBAAwB,KAAKxB,SAAS,EAAE;QACzD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACsB,wBAAwB,KAAK,QAAQ,IAAI7B,MAAM,CAACO,OAAO,CAACsB,wBAAwB,KAAK,IAAI,EAAE;UACnH,MAAM,IAAI5B,SAAS,CAAC,gFAAgF,CAAC;QACvG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACsB,wBAAwB,GAAG7B,MAAM,CAACO,OAAO,CAACsB,wBAAwB;MACxF;MAEA,IAAI7B,MAAM,CAACO,OAAO,CAACuB,QAAQ,KAAKzB,SAAS,EAAE;QACzC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACuB,QAAQ,KAAK,QAAQ,EAAE;UAC/C,MAAM,IAAI7B,SAAS,CAAC,gEAAgE,CAAC;QACvF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACuB,QAAQ,GAAG9B,MAAM,CAACO,OAAO,CAACuB,QAAQ;MACxD;MAEA,IAAI9B,MAAM,CAACO,OAAO,CAACwB,SAAS,KAAK1B,SAAS,EAAE;QAC1C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACwB,SAAS,KAAK,QAAQ,IAAI/B,MAAM,CAACO,OAAO,CAACwB,SAAS,KAAK,IAAI,EAAE;UACrF,MAAM,IAAI9B,SAAS,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAID,MAAM,CAACO,OAAO,CAACwB,SAAS,KAAK,IAAI,KAAK/B,MAAM,CAACO,OAAO,CAACwB,SAAS,GAAG,CAAC,IAAI/B,MAAM,CAACO,OAAO,CAACwB,SAAS,GAAG,CAAC,CAAC,EAAE;UACvG,MAAM,IAAI2C,UAAU,CAAC,+DAA+D,CAAC;QACvF;QAEA,IAAI,CAAC1E,MAAM,CAACO,OAAO,CAACwB,SAAS,GAAG/B,MAAM,CAACO,OAAO,CAACwB,SAAS;MAC1D;MAEA,IAAI/B,MAAM,CAACO,OAAO,CAACyB,UAAU,KAAK3B,SAAS,EAAE;QAC3C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACyB,UAAU,KAAK,QAAQ,IAAIhC,MAAM,CAACO,OAAO,CAACyB,UAAU,KAAK,IAAI,EAAE;UACvF,MAAM,IAAI/B,SAAS,CAAC,0EAA0E,CAAC;QACjG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACyB,UAAU,GAAGhC,MAAM,CAACO,OAAO,CAACyB,UAAU;MAC5D;MAEA,IAAIhC,MAAM,CAACO,OAAO,CAAC0B,KAAK,EAAE;QACxB,IAAIjC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACC,IAAI,KAAK7B,SAAS,EAAE;UAC3C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACC,IAAI,KAAK,SAAS,EAAE;YAClD,MAAM,IAAIjC,SAAS,CAAC,mEAAmE,CAAC;UAC1F;UAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACC,IAAI,GAAGlC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACC,IAAI;QAC5D;QAEA,IAAIlC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACE,MAAM,KAAK9B,SAAS,EAAE;UAC7C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACE,MAAM,KAAK,SAAS,EAAE;YACpD,MAAM,IAAIlC,SAAS,CAAC,qEAAqE,CAAC;UAC5F;UAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACE,MAAM,GAAGnC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACE,MAAM;QAChE;QAEA,IAAInC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACG,OAAO,KAAK/B,SAAS,EAAE;UAC9C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACG,OAAO,KAAK,SAAS,EAAE;YACrD,MAAM,IAAInC,SAAS,CAAC,sEAAsE,CAAC;UAC7F;UAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACG,OAAO,GAAGpC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACG,OAAO;QAClE;QAEA,IAAIpC,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACnB,KAAK,KAAKT,SAAS,EAAE;UAC5C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACnB,KAAK,KAAK,SAAS,EAAE;YACnD,MAAM,IAAIb,SAAS,CAAC,oEAAoE,CAAC;UAC3F;UAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACnB,KAAK,GAAGd,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAACnB,KAAK;QAC9D;MACF;MAEA,IAAId,MAAM,CAACO,OAAO,CAAC8B,cAAc,KAAKhC,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC8B,cAAc,KAAK,SAAS,IAAIrC,MAAM,CAACO,OAAO,CAAC8B,cAAc,KAAK,IAAI,EAAE;UAChG,MAAM,IAAIpC,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC8B,cAAc,GAAGrC,MAAM,CAACO,OAAO,CAAC8B,cAAc;MACpE;MAEA,IAAIrC,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,KAAKjC,SAAS,EAAE;QACtD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,KAAK,SAAS,IAAItC,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,KAAK,IAAI,EAAE;UAC9G,MAAM,IAAIrC,SAAS,CAAC,sFAAsF,CAAC;QAC7G;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,GAAGtC,MAAM,CAACO,OAAO,CAAC+B,qBAAqB;MAClF;MAEA,IAAItC,MAAM,CAACO,OAAO,CAACgC,iBAAiB,KAAKlC,SAAS,EAAE;QAClD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACgC,iBAAiB,KAAK,SAAS,IAAIvC,MAAM,CAACO,OAAO,CAACgC,iBAAiB,KAAK,IAAI,EAAE;UACtG,MAAM,IAAItC,SAAS,CAAC,kFAAkF,CAAC;QACzG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACgC,iBAAiB,GAAGvC,MAAM,CAACO,OAAO,CAACgC,iBAAiB;MAC1E;MAEA,IAAIvC,MAAM,CAACO,OAAO,CAACiC,kBAAkB,KAAKnC,SAAS,EAAE;QACnD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACiC,kBAAkB,KAAK,SAAS,IAAIxC,MAAM,CAACO,OAAO,CAACiC,kBAAkB,KAAK,IAAI,EAAE;UACxG,MAAM,IAAIvC,SAAS,CAAC,mFAAmF,CAAC;QAC1G;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACiC,kBAAkB,GAAGxC,MAAM,CAACO,OAAO,CAACiC,kBAAkB;MAC5E;MAEA,IAAIxC,MAAM,CAACO,OAAO,CAACkC,gBAAgB,KAAKpC,SAAS,EAAE;QACjD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACkC,gBAAgB,KAAK,SAAS,IAAIzC,MAAM,CAACO,OAAO,CAACkC,gBAAgB,KAAK,IAAI,EAAE;UACpG,MAAM,IAAIxC,SAAS,CAAC,iFAAiF,CAAC;QACxG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACkC,gBAAgB,GAAGzC,MAAM,CAACO,OAAO,CAACkC,gBAAgB;MACxE;MAEA,IAAIzC,MAAM,CAACO,OAAO,CAACmC,0BAA0B,KAAKrC,SAAS,EAAE;QAC3D,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACmC,0BAA0B,KAAK,SAAS,IAAI1C,MAAM,CAACO,OAAO,CAACmC,0BAA0B,KAAK,IAAI,EAAE;UACxH,MAAM,IAAIzC,SAAS,CAAC,2FAA2F,CAAC;QAClH;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACmC,0BAA0B,GAAG1C,MAAM,CAACO,OAAO,CAACmC,0BAA0B;MAC5F;MAEA,IAAI1C,MAAM,CAACO,OAAO,CAACoC,yBAAyB,KAAKtC,SAAS,EAAE;QAC1D,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACoC,yBAAyB,KAAK,SAAS,IAAI3C,MAAM,CAACO,OAAO,CAACoC,yBAAyB,KAAK,IAAI,EAAE;UACtH,MAAM,IAAI1C,SAAS,CAAC,0FAA0F,CAAC;QACjH;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACoC,yBAAyB,GAAG3C,MAAM,CAACO,OAAO,CAACoC,yBAAyB;MAC1F;MAEA,IAAI3C,MAAM,CAACO,OAAO,CAACqC,0BAA0B,KAAKvC,SAAS,EAAE;QAC3D,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACqC,0BAA0B,KAAK,SAAS,IAAI5C,MAAM,CAACO,OAAO,CAACqC,0BAA0B,KAAK,IAAI,EAAE;UACxH,MAAM,IAAI3C,SAAS,CAAC,2FAA2F,CAAC;QAClH;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACqC,0BAA0B,GAAG5C,MAAM,CAACO,OAAO,CAACqC,0BAA0B;MAC5F;MAEA,IAAI5C,MAAM,CAACO,OAAO,CAACsC,uBAAuB,KAAKxC,SAAS,EAAE;QACxD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACsC,uBAAuB,KAAK,SAAS,IAAI7C,MAAM,CAACO,OAAO,CAACsC,uBAAuB,KAAK,IAAI,EAAE;UAClH,MAAM,IAAI5C,SAAS,CAAC,wFAAwF,CAAC;QAC/G;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACsC,uBAAuB,GAAG7C,MAAM,CAACO,OAAO,CAACsC,uBAAuB;MACtF;MAEA,IAAI7C,MAAM,CAACO,OAAO,CAACuC,sBAAsB,KAAKzC,SAAS,EAAE;QACvD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACuC,sBAAsB,KAAK,SAAS,IAAI9C,MAAM,CAACO,OAAO,CAACuC,sBAAsB,KAAK,IAAI,EAAE;UAChH,MAAM,IAAI7C,SAAS,CAAC,uFAAuF,CAAC;QAC9G;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACuC,sBAAsB,GAAG9C,MAAM,CAACO,OAAO,CAACuC,sBAAsB;MACpF;MACA,IAAI9C,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK1C,SAAS,EAAE;QACxC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK,SAAS,EAAE;UAC/C,IAAI/C,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAI9C,SAAS,CAAC,qEAAqE,CAAC;UAC5F;QACF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACwC,OAAO,GAAG/C,MAAM,CAACO,OAAO,CAACwC,OAAO;MACtD;MAEA,IAAI/C,MAAM,CAACO,OAAO,CAACyC,mBAAmB,KAAK3C,SAAS,EAAE;QACpD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACyC,mBAAmB,KAAK,SAAS,EAAE;UAC3D,MAAM,IAAI/C,SAAS,CAAC,4EAA4E,CAAC;QACnG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACyC,mBAAmB,GAAGhD,MAAM,CAACO,OAAO,CAACyC,mBAAmB;MAC9E;MAEA,IAAIhD,MAAM,CAACO,OAAO,CAAC2C,YAAY,KAAK7C,SAAS,EAAE;QAC7C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC2C,YAAY,KAAK,QAAQ,EAAE;UACnD,MAAM,IAAIjD,SAAS,CAAC,oEAAoE,CAAC;QAC3F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC2C,YAAY,GAAGlD,MAAM,CAACO,OAAO,CAAC2C,YAAY;QAC9D,IAAI,CAAClD,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAGpD,SAAS;MACtC;MAEA,IAAIL,MAAM,CAACO,OAAO,CAAC4C,cAAc,KAAK9C,SAAS,EAAE;QAC/C,IAAAoE,sCAAyB,EAACzE,MAAM,CAACO,OAAO,CAAC4C,cAAc,EAAE,+BAA+B,CAAC;QAEzF,IAAI,CAACnD,MAAM,CAACO,OAAO,CAAC4C,cAAc,GAAGnD,MAAM,CAACO,OAAO,CAAC4C,cAAc;MACpE;MAEA,IAAInD,MAAM,CAACO,OAAO,CAAC6C,QAAQ,KAAK/C,SAAS,EAAE;QACzC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC6C,QAAQ,KAAK,QAAQ,IAAIpD,MAAM,CAACO,OAAO,CAAC6C,QAAQ,KAAK,IAAI,EAAE;UACnF,MAAM,IAAInD,SAAS,CAAC,wEAAwE,CAAC;QAC/F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC6C,QAAQ,GAAGpD,MAAM,CAACO,OAAO,CAAC6C,QAAQ;MACxD;MAEA,IAAIpD,MAAM,CAACO,OAAO,CAAC8C,YAAY,KAAKhD,SAAS,EAAE;QAC7C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC8C,YAAY,KAAK,QAAQ,EAAE;UACnD,MAAM,IAAIpD,SAAS,CAAC,oEAAoE,CAAC;QAC3F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC8C,YAAY,GAAGrD,MAAM,CAACO,OAAO,CAAC8C,YAAY;MAChE;MAEA,IAAIrD,MAAM,CAACO,OAAO,CAACgD,mBAAmB,KAAKlD,SAAS,EAAE;QACpD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACgD,mBAAmB,KAAK,SAAS,EAAE;UAC3D,MAAM,IAAItD,SAAS,CAAC,4EAA4E,CAAC;QACnG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACgD,mBAAmB,GAAGvD,MAAM,CAACO,OAAO,CAACgD,mBAAmB;MAC9E;MAEA,IAAIvD,MAAM,CAACO,OAAO,CAACiD,UAAU,KAAKnD,SAAS,EAAE;QAC3C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACiD,UAAU,KAAK,QAAQ,EAAE;UACjD,MAAM,IAAIvD,SAAS,CAAC,kEAAkE,CAAC;QACzF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACiD,UAAU,GAAGxD,MAAM,CAACO,OAAO,CAACiD,UAAU;MAC5D;MAEA,IAAIxD,MAAM,CAACO,OAAO,CAACkD,IAAI,KAAKpD,SAAS,EAAE;QACrC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACkD,IAAI,KAAK,QAAQ,EAAE;UAC3C,MAAM,IAAIxD,SAAS,CAAC,4DAA4D,CAAC;QACnF;QAEA,IAAID,MAAM,CAACO,OAAO,CAACkD,IAAI,IAAI,CAAC,IAAIzD,MAAM,CAACO,OAAO,CAACkD,IAAI,IAAI,KAAK,EAAE;UAC5D,MAAM,IAAIiB,UAAU,CAAC,4DAA4D,CAAC;QACpF;QAEA,IAAI,CAAC1E,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAGzD,MAAM,CAACO,OAAO,CAACkD,IAAI;QAC9C,IAAI,CAACzD,MAAM,CAACO,OAAO,CAAC2C,YAAY,GAAG7C,SAAS;MAC9C;MAEA,IAAIL,MAAM,CAACO,OAAO,CAACmD,cAAc,KAAKrD,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACmD,cAAc,KAAK,SAAS,EAAE;UACtD,MAAM,IAAIzD,SAAS,CAAC,uEAAuE,CAAC;QAC9F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACmD,cAAc,GAAG1D,MAAM,CAACO,OAAO,CAACmD,cAAc;MACpE;MAEA,IAAI1D,MAAM,CAACO,OAAO,CAACoD,cAAc,KAAKtD,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACoD,cAAc,KAAK,QAAQ,EAAE;UACrD,MAAM,IAAI1D,SAAS,CAAC,sEAAsE,CAAC;QAC7F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACoD,cAAc,GAAG3D,MAAM,CAACO,OAAO,CAACoD,cAAc;MACpE;MAEA,IAAI3D,MAAM,CAACO,OAAO,CAAC+C,2BAA2B,KAAKjD,SAAS,EAAE;QAC5D,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC+C,2BAA2B,KAAK,QAAQ,EAAE;UAClE,MAAM,IAAIrD,SAAS,CAAC,mFAAmF,CAAC;QAC1G;QAEA,IAAID,MAAM,CAACO,OAAO,CAAC+C,2BAA2B,GAAG,CAAC,EAAE;UAClD,MAAM,IAAIrD,SAAS,CAAC,4FAA4F,CAAC;QACnH;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC+C,2BAA2B,GAAGtD,MAAM,CAACO,OAAO,CAAC+C,2BAA2B;MAC9F;MAEA,IAAItD,MAAM,CAACO,OAAO,CAACgB,uBAAuB,KAAKlB,SAAS,EAAE;QACxD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACgB,uBAAuB,KAAK,QAAQ,EAAE;UAC9D,MAAM,IAAItB,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAID,MAAM,CAACO,OAAO,CAACgB,uBAAuB,IAAI,CAAC,EAAE;UAC/C,MAAM,IAAItB,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACgB,uBAAuB,GAAGvB,MAAM,CAACO,OAAO,CAACgB,uBAAuB;MACtF;MAEA,IAAIvB,MAAM,CAACO,OAAO,CAACqD,mBAAmB,KAAKvD,SAAS,EAAE;QACpD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACqD,mBAAmB,KAAK,SAAS,EAAE;UAC3D,MAAM,IAAI3D,SAAS,CAAC,4EAA4E,CAAC;QACnG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACqD,mBAAmB,GAAG5D,MAAM,CAACO,OAAO,CAACqD,mBAAmB;MAC9E;MAEA,IAAI5D,MAAM,CAACO,OAAO,CAACsD,gCAAgC,KAAKxD,SAAS,EAAE;QACjE,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACsD,gCAAgC,KAAK,SAAS,EAAE;UACxE,MAAM,IAAI5D,SAAS,CAAC,yFAAyF,CAAC;QAChH;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACsD,gCAAgC,GAAG7D,MAAM,CAACO,OAAO,CAACsD,gCAAgC;MACxG;MAEA,IAAI7D,MAAM,CAACO,OAAO,CAACyD,UAAU,KAAK3D,SAAS,EAAE;QAC3C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACyD,UAAU,KAAK,QAAQ,EAAE;UACjD,MAAM,IAAI/D,SAAS,CAAC,kEAAkE,CAAC;QACzF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAGhE,MAAM,CAACO,OAAO,CAACyD,UAAU;MAC5D;MAEA,IAAIhE,MAAM,CAACO,OAAO,CAAC0D,QAAQ,KAAK5D,SAAS,EAAE;QACzC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC0D,QAAQ,KAAK,QAAQ,IAAIjE,MAAM,CAACO,OAAO,CAAC0D,QAAQ,KAAK,IAAI,EAAE;UACnF,MAAM,IAAIhE,SAAS,CAAC,wEAAwE,CAAC;QAC/F;QAEA,IAAID,MAAM,CAACO,OAAO,CAAC0D,QAAQ,GAAG,UAAU,EAAE;UACxC,MAAM,IAAIhE,SAAS,CAAC,kEAAkE,CAAC;QACzF,CAAC,MAAM,IAAID,MAAM,CAACO,OAAO,CAAC0D,QAAQ,GAAG,CAAC,CAAC,EAAE;UACvC,MAAM,IAAIhE,SAAS,CAAC,0DAA0D,CAAC;QACjF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC0D,QAAQ,GAAGjE,MAAM,CAACO,OAAO,CAAC0D,QAAQ,GAAG,CAAC;MAC5D;MAEA,IAAIjE,MAAM,CAACO,OAAO,CAAC4D,sBAAsB,KAAK9D,SAAS,EAAE;QACvD,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC4D,sBAAsB,KAAK,SAAS,EAAE;UAC9D,MAAM,IAAIlE,SAAS,CAAC,+EAA+E,CAAC;QACtG;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC4D,sBAAsB,GAAGnE,MAAM,CAACO,OAAO,CAAC4D,sBAAsB;MACpF;MAEA,IAAInE,MAAM,CAACO,OAAO,CAACuD,UAAU,KAAKzD,SAAS,EAAE;QAC3C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACuD,UAAU,KAAK,QAAQ,EAAE;UACjD,MAAM,IAAI7D,SAAS,CAAC,kEAAkE,CAAC;QACzF;QACA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACuD,UAAU,GAAG9D,MAAM,CAACO,OAAO,CAACuD,UAAU;MAC5D;MAEA,IAAI9D,MAAM,CAACO,OAAO,CAAC6D,cAAc,KAAK/D,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC6D,cAAc,KAAK,SAAS,EAAE;UACtD,MAAM,IAAInE,SAAS,CAAC,uEAAuE,CAAC;QAC9F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC6D,cAAc,GAAGpE,MAAM,CAACO,OAAO,CAAC6D,cAAc;MACpE;MAEA,IAAIpE,MAAM,CAACO,OAAO,CAAC8D,MAAM,KAAKhE,SAAS,EAAE;QACvC,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC8D,MAAM,KAAK,SAAS,EAAE;UAC9C,MAAM,IAAIpE,SAAS,CAAC,+DAA+D,CAAC;QACtF;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC8D,MAAM,GAAGrE,MAAM,CAACO,OAAO,CAAC8D,MAAM;MACpD;MAEA,IAAIrE,MAAM,CAACO,OAAO,CAAC+D,aAAa,KAAKjE,SAAS,EAAE;QAC9C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAAC+D,aAAa,KAAK,QAAQ,EAAE;UACpD,MAAM,IAAIrE,SAAS,CAAC,qEAAqE,CAAC;QAC5F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAAC+D,aAAa,GAAGtE,MAAM,CAACO,OAAO,CAAC+D,aAAa;MAClE;MAEA,IAAItE,MAAM,CAACO,OAAO,CAACgE,cAAc,KAAKlE,SAAS,EAAE;QAC/C,IAAI,OAAOL,MAAM,CAACO,OAAO,CAACgE,cAAc,KAAK,SAAS,EAAE;UACtD,MAAM,IAAItE,SAAS,CAAC,uEAAuE,CAAC;QAC9F;QAEA,IAAI,CAACD,MAAM,CAACO,OAAO,CAACgE,cAAc,GAAGvE,MAAM,CAACO,OAAO,CAACgE,cAAc;MACpE;IACF;IAEA,IAAI,CAACI,oBAAoB,GAAG,IAAI,CAAC3E,MAAM,CAACO,OAAO,CAACsB,wBAAwB;IACxE,IAAI,IAAI,CAAC8C,oBAAoB,CAACC,aAAa,KAAKvE,SAAS,EAAE;MACzD;MACA;MACA;MACA;MACA;MACA,IAAI,CAACsE,oBAAoB,GAAGxG,MAAM,CAAC0G,MAAM,CAAC,IAAI,CAACF,oBAAoB,EAAE;QACnEC,aAAa,EAAE;UACbE,KAAK,EAAEC,kBAAS,CAACC;QACnB;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC/C,KAAK,GAAG,IAAI,CAACgD,WAAW,CAAC,CAAC;IAC/B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,sBAAsB,GAAG,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAErE;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC;IAEpC,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,oBAAoB,GAAG,IAAIC,0CAAoB,CAAC,CAAC;IAEtD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,WAAW;IAEnC,IAAI,CAAClG,uBAAuB,GAAG,MAAM;MACnC,IAAI,CAACmG,SAAS,CAACC,WAAW,CAACC,YAAI,CAACC,SAAS,CAAC;MAC1C,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC1B,CAAC;EACH;EAEAC,OAAOA,CAACC,eAAuC,EAAE;IAC/C,IAAI,IAAI,CAACT,KAAK,KAAK,IAAI,CAACC,KAAK,CAACC,WAAW,EAAE;MACzC,MAAM,IAAIQ,uBAAe,CAAC,mDAAmD,GAAG,IAAI,CAACV,KAAK,CAACW,IAAI,GAAG,UAAU,CAAC;IAC/G;IAEA,IAAIF,eAAe,EAAE;MACnB,MAAMG,SAAS,GAAIC,GAAW,IAAK;QACjC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;QACrCN,eAAe,CAACI,GAAG,CAAC;MACtB,CAAC;MAED,MAAME,OAAO,GAAIF,GAAU,IAAK;QAC9B,IAAI,CAACC,cAAc,CAAC,SAAS,EAAEF,SAAS,CAAC;QACzCH,eAAe,CAACI,GAAG,CAAC;MACtB,CAAC;MAED,IAAI,CAACG,IAAI,CAAC,SAAS,EAAEJ,SAAS,CAAC;MAC/B,IAAI,CAACI,IAAI,CAAC,OAAO,EAAED,OAAO,CAAC;IAC7B;IAEA,IAAI,CAACE,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACiB,UAAU,CAAC;EAC1C;;EAEA;AACF;AACA;;EAGE;AACF;AACA;;EAUE;AACF;AACA;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;AACA;AACA;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAGEC,EAAEA,CAACC,KAAsB,EAAEC,QAAkC,EAAE;IAC7D,OAAO,KAAK,CAACF,EAAE,CAACC,KAAK,EAAEC,QAAQ,CAAC;EAClC;;EAEA;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGEC,IAAIA,CAACF,KAAsB,EAAE,GAAGG,IAAW,EAAE;IAC3C,OAAO,KAAK,CAACD,IAAI,CAACF,KAAK,EAAE,GAAGG,IAAI,CAAC;EACnC;;EAEA;AACF;AACA;AACA;AACA;EACEC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;EACrC;;EAEA;AACF;AACA;EACEC,oBAAoBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAExC,IAAI,IAAI,CAAC1H,MAAM,CAACO,OAAO,CAACkD,IAAI,EAAE;MAC5B,OAAO,IAAI,CAACkE,aAAa,CAAC,IAAI,CAAC3H,MAAM,CAACO,OAAO,CAACkD,IAAI,EAAE,IAAI,CAACzD,MAAM,CAACO,OAAO,CAACgD,mBAAmB,EAAEkE,MAAM,EAAE,IAAI,CAACzH,MAAM,CAACO,OAAO,CAACkB,SAAS,CAAC;IACrI,CAAC,MAAM;MACL,OAAO,IAAAmG,8BAAc,EAAC;QACpB1H,MAAM,EAAE,IAAI,CAACF,MAAM,CAACE,MAAM;QAC1BgD,YAAY,EAAE,IAAI,CAAClD,MAAM,CAACO,OAAO,CAAC2C,YAAa;QAC/C2E,OAAO,EAAE,IAAI,CAAC7H,MAAM,CAACO,OAAO,CAACiB,cAAc;QAC3CiG,MAAM,EAAEA;MACV,CAAC,CAAC,CAACK,IAAI,CAAErE,IAAI,IAAK;QAChBsE,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,IAAI,CAACL,aAAa,CAAClE,IAAI,EAAE,IAAI,CAACzD,MAAM,CAACO,OAAO,CAACgD,mBAAmB,EAAEkE,MAAM,EAAE,IAAI,CAACzH,MAAM,CAACO,OAAO,CAACkB,SAAS,CAAC;QAC1G,CAAC,CAAC;MACJ,CAAC,EAAGkF,GAAG,IAAK;QACV,IAAI,CAACsB,iBAAiB,CAAC,CAAC;QAExB,IAAIR,MAAM,CAACS,OAAO,EAAE;UAClB;UACA;QACF;QAEAH,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,IAAI,CAACZ,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAACG,GAAG,CAACwB,OAAO,EAAE,aAAa,CAAC,CAAC;QACvE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACEC,iBAAiBA,CAACC,WAA2D,EAAE;IAC7E,IAAI,CAAC,IAAI,CAAC7C,MAAM,EAAE;MAChB,IAAI,CAACyC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACK,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAIH,WAAW,KAAK7I,YAAY,CAACE,QAAQ,EAAE;QACzC,IAAI,CAAC0H,IAAI,CAAC,WAAW,CAAC;MACxB,CAAC,MAAM,IAAIiB,WAAW,KAAK7I,YAAY,CAACG,KAAK,EAAE;QAC7CoI,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,IAAI,CAACZ,IAAI,CAAC,KAAK,CAAC;QAClB,CAAC,CAAC;MACJ;MAEA,MAAMqB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACX,MAAM9B,GAAG,GAAG,IAAI+B,oBAAY,CAAC,6CAA6C,EAAE,QAAQ,CAAC;QACrFD,OAAO,CAACE,QAAQ,CAAChC,GAAG,CAAC;QACrB,IAAI,CAAC8B,OAAO,GAAGpI,SAAS;MAC1B;MAEA,IAAI,CAACmF,MAAM,GAAG,IAAI;MAClB,IAAI,CAACoD,UAAU,GAAGvI,SAAS;IAC7B;EACF;;EAEA;AACF;AACA;EACE4E,WAAWA,CAAA,EAAG;IACZ,MAAMhD,KAAK,GAAG,IAAI4G,cAAK,CAAC,IAAI,CAAC7I,MAAM,CAACO,OAAO,CAAC0B,KAAK,CAAC;IAClDA,KAAK,CAACgF,EAAE,CAAC,OAAO,EAAGkB,OAAO,IAAK;MAC7B,IAAI,CAACf,IAAI,CAAC,OAAO,EAAEe,OAAO,CAAC;IAC7B,CAAC,CAAC;IACF,OAAOlG,KAAK;EACd;;EAEA;AACF;AACA;EACE6G,uBAAuBA,CAACX,OAAgB,EAAEY,OAAqB,EAAE;IAC/D,OAAO,IAAIC,yBAAiB,CAACb,OAAO,EAAE,IAAI,CAAClG,KAAK,EAAE8G,OAAO,EAAE,IAAI,CAAC/I,MAAM,CAACO,OAAO,CAAC;EACjF;EAEA0I,6BAA6BA,CAACC,MAAkB,EAAE;IAChDA,MAAM,CAACjC,EAAE,CAAC,OAAO,EAAGkC,KAAK,IAAK;MAAE,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;IAC3DD,MAAM,CAACjC,EAAE,CAAC,OAAO,EAAE,MAAM;MAAE,IAAI,CAACoC,WAAW,CAAC,CAAC;IAAE,CAAC,CAAC;IACjDH,MAAM,CAACjC,EAAE,CAAC,KAAK,EAAE,MAAM;MAAE,IAAI,CAACqC,SAAS,CAAC,CAAC;IAAE,CAAC,CAAC;IAC7CJ,MAAM,CAACK,YAAY,CAAC,IAAI,EAAE3K,wBAAwB,CAAC;IAEnD,IAAI,CAACqH,SAAS,GAAG,IAAIuD,kBAAS,CAACN,MAAM,EAAE,IAAI,CAAClJ,MAAM,CAACO,OAAO,CAACiD,UAAU,EAAE,IAAI,CAACvB,KAAK,CAAC;IAClF,IAAI,CAACgE,SAAS,CAACgB,EAAE,CAAC,QAAQ,EAAGwC,SAAS,IAAK;MAAE,IAAI,CAACrC,IAAI,CAAC,QAAQ,EAAEqC,SAAS,CAAC;IAAE,CAAC,CAAC;IAE/E,IAAI,CAACP,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAAC1D,MAAM,GAAG,KAAK;IACnB,IAAI,CAACvD,KAAK,CAACyH,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC1J,MAAM,CAACE,MAAM,GAAG,GAAG,GAAG,IAAI,CAACF,MAAM,CAACO,OAAO,CAACkD,IAAI,CAAC;IAErF,IAAI,CAACkG,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC5C,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC6D,aAAa,CAAC;EAC7C;EAEAC,WAAWA,CAACX,MAAkB,EAAEzB,MAAmB,EAA0B;IAC3EA,MAAM,CAACqC,cAAc,CAAC,CAAC;IAEvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,aAAa,GAAGhP,GAAG,CAACiP,mBAAmB,CAAC,IAAI,CAACxF,oBAAoB,CAAC;MACxE;MACA;MACA;MACA,MAAMb,UAAU,GAAG,CAAC1I,GAAG,CAACgP,IAAI,CAAC,IAAI,CAACpK,MAAM,CAACE,MAAM,CAAC,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM,GAAG,EAAE;MAC1E,MAAMmK,cAAc,GAAG;QACrBC,IAAI,EAAE,IAAI,CAACtK,MAAM,CAACE,MAAM;QACxBgJ,MAAM,EAAEA,MAAM;QACdqB,aAAa,EAAE,CAAC,SAAS,CAAC;QAC1BL,aAAa,EAAEA,aAAa;QAC5BM,UAAU,EAAE,IAAI,CAACxK,MAAM,CAACO,OAAO,CAACuD,UAAU,GAAG,IAAI,CAAC9D,MAAM,CAACO,OAAO,CAACuD,UAAU,GAAGA;MAChF,CAAC;MAED,MAAM2G,aAAa,GAAGvP,GAAG,CAACoL,OAAO,CAAC+D,cAAc,CAAC;MAEjD,MAAMK,OAAO,GAAGA,CAAA,KAAM;QACpBD,aAAa,CAAC7D,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;QAC9C4D,aAAa,CAAC7D,cAAc,CAAC,SAAS,EAAEF,SAAS,CAAC;QAElD+D,aAAa,CAACE,OAAO,CAAC,CAAC;QAEvBV,MAAM,CAACxC,MAAM,CAACmD,MAAM,CAAC;MACvB,CAAC;MAED,MAAM/D,OAAO,GAAIF,GAAU,IAAK;QAC9Bc,MAAM,CAACoD,mBAAmB,CAAC,OAAO,EAAEH,OAAO,CAAC;QAE5CD,aAAa,CAAC7D,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;QAC9C4D,aAAa,CAAC7D,cAAc,CAAC,SAAS,EAAEF,SAAS,CAAC;QAElD+D,aAAa,CAACE,OAAO,CAAC,CAAC;QAEvBV,MAAM,CAACtD,GAAG,CAAC;MACb,CAAC;MAED,MAAMD,SAAS,GAAGA,CAAA,KAAM;QACtBe,MAAM,CAACoD,mBAAmB,CAAC,OAAO,EAAEH,OAAO,CAAC;QAE5CD,aAAa,CAAC7D,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;QAC9C4D,aAAa,CAAC7D,cAAc,CAAC,SAAS,EAAEF,SAAS,CAAC;QAElDsD,OAAO,CAACS,aAAa,CAAC;MACxB,CAAC;MAEDhD,MAAM,CAACqD,gBAAgB,CAAC,OAAO,EAAEJ,OAAO,EAAE;QAAE5D,IAAI,EAAE;MAAK,CAAC,CAAC;MAEzD2D,aAAa,CAACxD,EAAE,CAAC,OAAO,EAAEJ,OAAO,CAAC;MAClC4D,aAAa,CAACxD,EAAE,CAAC,eAAe,EAAEP,SAAS,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEAiB,aAAaA,CAAClE,IAAY,EAAEF,mBAA4B,EAAEkE,MAAmB,EAAEsD,eAA2C,EAAE;IAC1H,MAAMC,WAAW,GAAG;MAClBV,IAAI,EAAE,IAAI,CAACW,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/K,MAAM,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM;MACrEuD,IAAI,EAAE,IAAI,CAACwH,WAAW,GAAG,IAAI,CAACA,WAAW,CAACxH,IAAI,GAAGA,IAAI;MACrDJ,YAAY,EAAE,IAAI,CAACrD,MAAM,CAACO,OAAO,CAAC8C;IACpC,CAAC;IAED,MAAMiD,OAAO,GAAGyE,eAAe,KAAKxH,mBAAmB,GAAG2H,4BAAiB,GAAGC,4BAAiB,CAAC;IAEhG,CAAC,YAAY;MACX,IAAIjC,MAAM,GAAG,MAAM5C,OAAO,CAAC0E,WAAW,EAAEI,YAAG,CAACC,MAAM,EAAE5D,MAAM,CAAC;MAE3D,IAAI,IAAI,CAACzH,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK,QAAQ,EAAE;QAC5C,IAAI;UACF;UACAmG,MAAM,GAAG,MAAM,IAAI,CAACW,WAAW,CAACX,MAAM,EAAEzB,MAAM,CAAC;QACjD,CAAC,CAAC,OAAOd,GAAG,EAAE;UACZuC,MAAM,CAACoC,GAAG,CAAC,CAAC;UAEZ,MAAM3E,GAAG;QACX;MACF;MAEA,IAAI,CAACsC,6BAA6B,CAACC,MAAM,CAAC;IAC5C,CAAC,EAAE,CAAC,CAACqC,KAAK,CAAE5E,GAAG,IAAK;MAClB,IAAI,CAACsB,iBAAiB,CAAC,CAAC;MAExB,IAAIR,MAAM,CAACS,OAAO,EAAE;QAClB;MACF;MAEAH,OAAO,CAACC,QAAQ,CAAC,MAAM;QAAE,IAAI,CAACoB,WAAW,CAACzC,GAAG,CAAC;MAAE,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE6B,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACU,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACyB,OAAO,CAAC,CAAC;IACvB;EACF;;EAEA;AACF;AACA;EACEjD,kBAAkBA,CAAA,EAAG;IACnB,MAAM8D,UAAU,GAAG,IAAIC,oCAAe,CAAC,CAAC;IACxC,IAAI,CAACC,YAAY,GAAGC,UAAU,CAAC,MAAM;MACnCH,UAAU,CAACI,KAAK,CAAC,CAAC;MAClB,IAAI,CAACpK,cAAc,CAAC,CAAC;IACvB,CAAC,EAAE,IAAI,CAACxB,MAAM,CAACO,OAAO,CAACiB,cAAc,CAAC;IACtC,OAAOgK,UAAU,CAAC/D,MAAM;EAC1B;;EAEA;AACF;AACA;EACEpB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACwF,gBAAgB,CAAC,CAAC;IACvB,MAAMhE,OAAO,GAAG,IAAI,CAAC7H,MAAM,CAACO,OAAO,CAACY,aAAa;IACjD,IAAI0G,OAAO,GAAG,CAAC,EAAE;MACf,IAAI,CAACiE,WAAW,GAAGH,UAAU,CAAC,MAAM;QAClC,IAAI,CAACxK,aAAa,CAAC,CAAC;MACtB,CAAC,EAAE0G,OAAO,CAAC;IACb;EACF;;EAEA;AACF;AACA;EACEkE,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACzD,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAkB;IACvC,MAAMZ,OAAO,GAAIY,OAAO,CAACZ,OAAO,KAAKxH,SAAS,GAAIoI,OAAO,CAACZ,OAAO,GAAG,IAAI,CAAC7H,MAAM,CAACO,OAAO,CAACoD,cAAc;IACtG,IAAIkE,OAAO,EAAE;MACX,IAAI,CAACmE,YAAY,GAAGL,UAAU,CAAC,MAAM;QACnC,IAAI,CAAChI,cAAc,CAAC,CAAC;MACvB,CAAC,EAAEkE,OAAO,CAAC;IACb;EACF;;EAEA;AACF;AACA;EACEoE,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAC1D,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC2D,UAAU,GAAGP,UAAU,CAAC,MAAM;MACjC,IAAI,CAACQ,YAAY,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAACnM,MAAM,CAACO,OAAO,CAACgB,uBAAuB,CAAC;EACjD;;EAEA;AACF;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,MAAM4K,WAAW,GAAG,IAAI,CAACpM,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAI,IAAG,IAAI,CAACzD,MAAM,CAACO,OAAO,CAACkD,IAAK,EAAC,GAAI,KAAI,IAAI,CAACzD,MAAM,CAACO,OAAO,CAAC2C,YAAa,EAAC;IACvH;IACA,MAAMhD,MAAM,GAAG,IAAI,CAAC+K,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/K,MAAM,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM;IAC9E,MAAMuD,IAAI,GAAG,IAAI,CAACwH,WAAW,GAAI,IAAG,IAAI,CAACA,WAAW,CAACxH,IAAK,EAAC,GAAG2I,WAAW;IACzE;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACpB,WAAW,GAAI,qBAAoB,IAAI,CAACjL,MAAM,CAACE,MAAO,GAAEkM,WAAY,GAAE,GAAG,EAAE;IACvG,MAAMjE,OAAO,GAAI,wBAAuBjI,MAAO,GAAEuD,IAAK,GAAE4I,cAAe,OAAM,IAAI,CAACrM,MAAM,CAACO,OAAO,CAACiB,cAAe,IAAG;IACnH,IAAI,CAACS,KAAK,CAACyH,GAAG,CAACvB,OAAO,CAAC;IACvB,IAAI,CAACf,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC2B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC9D,IAAI,CAACuD,YAAY,GAAGrL,SAAS;IAC7B,IAAI,CAACiM,aAAa,CAAC,gBAAgB,CAAC;EACtC;;EAEA;AACF;AACA;EACEnL,aAAaA,CAAA,EAAG;IACd,MAAMgH,OAAO,GAAI,+BAA8B,IAAI,CAACnI,MAAM,CAACO,OAAO,CAACY,aAAc,IAAG;IACpF,IAAI,CAACc,KAAK,CAACyH,GAAG,CAACvB,OAAO,CAAC;IACvB,IAAI,CAACmE,aAAa,CAAC,aAAa,EAAE,IAAI9F,uBAAe,CAAC2B,OAAO,EAAE,UAAU,CAAC,CAAC;EAC7E;;EAEA;AACF;AACA;EACExE,cAAcA,CAAA,EAAG;IACf,IAAI,CAACqI,YAAY,GAAG3L,SAAS;IAC7B,MAAMoI,OAAO,GAAG,IAAI,CAACA,OAAQ;IAC7BA,OAAO,CAAC8D,MAAM,CAAC,CAAC;IAChB,MAAM1E,OAAO,GAAIY,OAAO,CAACZ,OAAO,KAAKxH,SAAS,GAAIoI,OAAO,CAACZ,OAAO,GAAG,IAAI,CAAC7H,MAAM,CAACO,OAAO,CAACoD,cAAc;IACtG,MAAMwE,OAAO,GAAG,yCAAyC,GAAGN,OAAO,GAAG,IAAI;IAC1EY,OAAO,CAACU,KAAK,GAAG,IAAIT,oBAAY,CAACP,OAAO,EAAE,UAAU,CAAC;EACvD;;EAEA;AACF;AACA;EACEgE,YAAYA,CAAA,EAAG;IACb,IAAI,CAACD,UAAU,GAAG7L,SAAS;IAC3B,IAAI,CAAC+G,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACL,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACiB,UAAU,CAAC;EAC1C;;EAEA;AACF;AACA;EACEiB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACyD,YAAY,EAAE;MACrBc,YAAY,CAAC,IAAI,CAACd,YAAY,CAAC;MAC/B,IAAI,CAACA,YAAY,GAAGrL,SAAS;IAC/B;EACF;;EAEA;AACF;AACA;EACEwL,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,WAAW,EAAE;MACpBU,YAAY,CAAC,IAAI,CAACV,WAAW,CAAC;MAC9B,IAAI,CAACA,WAAW,GAAGzL,SAAS;IAC9B;EACF;;EAEA;AACF;AACA;EACEiI,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC0D,YAAY,EAAE;MACrBQ,YAAY,CAAC,IAAI,CAACR,YAAY,CAAC;MAC/B,IAAI,CAACA,YAAY,GAAG3L,SAAS;IAC/B;EACF;;EAEA;AACF;AACA;EACEkI,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC2D,UAAU,EAAE;MACnBM,YAAY,CAAC,IAAI,CAACN,UAAU,CAAC;MAC7B,IAAI,CAACA,UAAU,GAAG7L,SAAS;IAC7B;EACF;;EAEA;AACF;AACA;EACE0G,YAAYA,CAAC0F,QAAe,EAAE;IAC5B,IAAI,IAAI,CAAC3G,KAAK,KAAK2G,QAAQ,EAAE;MAC3B,IAAI,CAACxK,KAAK,CAACyH,GAAG,CAAC,mBAAmB,GAAG+C,QAAQ,CAAChG,IAAI,CAAC;MACnD;IACF;IAEA,IAAI,IAAI,CAACX,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC4G,IAAI,EAAE;MACjC,IAAI,CAAC5G,KAAK,CAAC4G,IAAI,CAACjO,IAAI,CAAC,IAAI,EAAEgO,QAAQ,CAAC;IACtC;IAEA,IAAI,CAACxK,KAAK,CAACyH,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC5D,KAAK,GAAG,IAAI,CAACA,KAAK,CAACW,IAAI,GAAG,WAAW,CAAC,GAAG,MAAM,GAAGgG,QAAQ,CAAChG,IAAI,CAAC;IACxG,IAAI,CAACX,KAAK,GAAG2G,QAAQ;IAErB,IAAI,IAAI,CAAC3G,KAAK,CAAC6G,KAAK,EAAE;MACpB,IAAI,CAAC7G,KAAK,CAAC6G,KAAK,CAACC,KAAK,CAAC,IAAI,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;EACEC,eAAeA,CAAkCC,SAAY,EAAmC;IAC9F,MAAM/D,OAAO,GAAG,IAAI,CAACjD,KAAK,CAACiH,MAAM,CAACD,SAAS,CAAC;IAE5C,IAAI,CAAC/D,OAAO,EAAE;MACZ,MAAM,IAAIvE,KAAK,CAAE,aAAYsI,SAAU,eAAc,IAAI,CAAChH,KAAK,CAACW,IAAK,GAAE,CAAC;IAC1E;IAEA,OAAOsC,OAAO;EAChB;;EAEA;AACF;AACA;EACEuD,aAAaA,CAAkCQ,SAAY,EAAE,GAAGzF,IAAiD,EAAE;IACjH,MAAM0B,OAAO,GAAG,IAAI,CAACjD,KAAK,CAACiH,MAAM,CAACD,SAAS,CAA6D;IACxG,IAAI/D,OAAO,EAAE;MACXA,OAAO,CAAC6D,KAAK,CAAC,IAAI,EAAEvF,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI5C,KAAK,CAAE,aAAYsI,SAAU,eAAc,IAAI,CAAChH,KAAK,CAACW,IAAK,GAAE,CAAC,CAAC;MACtF,IAAI,CAACa,KAAK,CAAC,CAAC;IACd;EACF;;EAEA;AACF;AACA;EACE8B,WAAWA,CAACD,KAAY,EAAE;IACxB,IAAI,IAAI,CAACrD,KAAK,KAAK,IAAI,CAACC,KAAK,CAACiB,UAAU,IAAI,IAAI,CAAClB,KAAK,KAAK,IAAI,CAACC,KAAK,CAACiH,sBAAsB,EAAE;MAC5F,MAAMZ,WAAW,GAAG,IAAI,CAACpM,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAI,IAAG,IAAI,CAACzD,MAAM,CAACO,OAAO,CAACkD,IAAK,EAAC,GAAI,KAAI,IAAI,CAACzD,MAAM,CAACO,OAAO,CAAC2C,YAAa,EAAC;MACvH;MACA,MAAMhD,MAAM,GAAG,IAAI,CAAC+K,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/K,MAAM,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM;MAC9E,MAAMuD,IAAI,GAAG,IAAI,CAACwH,WAAW,GAAI,IAAG,IAAI,CAACA,WAAW,CAACxH,IAAK,EAAC,GAAG2I,WAAW;MACzE;MACA;MACA,MAAMC,cAAc,GAAG,IAAI,CAACpB,WAAW,GAAI,qBAAoB,IAAI,CAACjL,MAAM,CAACE,MAAO,GAAEkM,WAAY,GAAE,GAAG,EAAE;MACvG,MAAMjE,OAAO,GAAI,wBAAuBjI,MAAO,GAAEuD,IAAK,GAAE4I,cAAe,MAAKlD,KAAK,CAAChB,OAAQ,EAAC;MAC3F,IAAI,CAAClG,KAAK,CAACyH,GAAG,CAACvB,OAAO,CAAC;MACvB,IAAI,CAACf,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC2B,OAAO,EAAE,SAAS,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,MAAMA,OAAO,GAAI,qBAAoBgB,KAAK,CAAChB,OAAQ,EAAC;MACpD,IAAI,CAAClG,KAAK,CAACyH,GAAG,CAACvB,OAAO,CAAC;MACvB,IAAI,CAACf,IAAI,CAAC,OAAO,EAAE,IAAIZ,uBAAe,CAAC2B,OAAO,EAAE,SAAS,CAAC,CAAC;IAC7D;IACA,IAAI,CAACmE,aAAa,CAAC,aAAa,EAAEnD,KAAK,CAAC;EAC1C;;EAEA;AACF;AACA;EACEG,SAASA,CAAA,EAAG;IACV,IAAI,CAACrH,KAAK,CAACyH,GAAG,CAAC,cAAc,CAAC;IAC9B,IAAI,IAAI,CAAC5D,KAAK,KAAK,IAAI,CAACC,KAAK,CAACwB,KAAK,EAAE;MACnC,MAAM4B,KAAoB,GAAG,IAAI3E,KAAK,CAAC,gBAAgB,CAAC;MACxD2E,KAAK,CAAC8D,IAAI,GAAG,YAAY;MACzB,IAAI,CAAC7D,WAAW,CAACD,KAAK,CAAC;IACzB;EACF;;EAEA;AACF;AACA;EACEE,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACpH,KAAK,CAACyH,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC1J,MAAM,CAACE,MAAM,GAAG,GAAG,GAAG,IAAI,CAACF,MAAM,CAACO,OAAO,CAACkD,IAAI,GAAG,SAAS,CAAC;IAClG,IAAI,IAAI,CAACqC,KAAK,KAAK,IAAI,CAACC,KAAK,CAACmH,SAAS,EAAE;MACvC,IAAI,CAACjL,KAAK,CAACyH,GAAG,CAAC,eAAe,GAAG,IAAI,CAACuB,WAAW,CAAE/K,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC+K,WAAW,CAAExH,IAAI,CAAC;MAEzF,IAAI,CAAC6I,aAAa,CAAC,WAAW,CAAC;IACjC,CAAC,MAAM,IAAI,IAAI,CAACxG,KAAK,KAAK,IAAI,CAACC,KAAK,CAACoH,uBAAuB,EAAE;MAC5D,MAAMjN,MAAM,GAAG,IAAI,CAAC+K,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/K,MAAM,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM;MAC9E,MAAMuD,IAAI,GAAG,IAAI,CAACwH,WAAW,GAAG,IAAI,CAACA,WAAW,CAACxH,IAAI,GAAG,IAAI,CAACzD,MAAM,CAACO,OAAO,CAACkD,IAAI;MAChF,IAAI,CAACxB,KAAK,CAACyH,GAAG,CAAC,8CAA8C,GAAGxJ,MAAM,GAAG,GAAG,GAAGuD,IAAI,CAAC;MAEpF,IAAI,CAAC6I,aAAa,CAAC,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACvF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;IACrC;EACF;;EAEA;AACF;AACA;EACEoC,YAAYA,CAAA,EAAG;IACb,MAAM,GAAGyD,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,GAAG,sBAAsB,CAACC,IAAI,CAACC,gBAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAChG,MAAMpL,OAAO,GAAG,IAAIqL,wBAAe,CAAC;MAClC;MACA;MACA;MACA1K,OAAO,EAAE,OAAO,IAAI,CAAC/C,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC/C,MAAM,CAACO,OAAO,CAACwC,OAAO;MACxFyK,OAAO,EAAE;QAAEJ,KAAK,EAAEM,MAAM,CAACN,KAAK,CAAC;QAAEC,KAAK,EAAEK,MAAM,CAACL,KAAK,CAAC;QAAEC,KAAK,EAAEI,MAAM,CAACJ,KAAK,CAAC;QAAEK,QAAQ,EAAE;MAAE;IAC3F,CAAC,CAAC;IAEF,IAAI,CAAC1H,SAAS,CAACC,WAAW,CAACC,YAAI,CAACyH,QAAQ,EAAExL,OAAO,CAACF,IAAI,CAAC;IACvD,IAAI,CAACD,KAAK,CAACG,OAAO,CAAC,YAAW;MAC5B,OAAOA,OAAO,CAACyL,QAAQ,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEC,gBAAgBA,CAAA,EAAG;IACjB,MAAM1L,OAAO,GAAG,IAAI2L,sBAAa,CAAC;MAChC/J,UAAU,EAAEgK,qBAAQ,CAAC,IAAI,CAAChO,MAAM,CAACO,OAAO,CAACyD,UAAU,CAAC;MACpDR,UAAU,EAAE,IAAI,CAACxD,MAAM,CAACO,OAAO,CAACiD,UAAU;MAC1CyK,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAEnG,OAAO,CAACoG,GAAG;MACtBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAC9CC,UAAU,EAAE;IACd,CAAC,CAAC;IAEF,MAAM;MAAEpO;IAAe,CAAC,GAAG,IAAI,CAACJ,MAAM;IACtC,QAAQI,cAAc,CAACE,IAAI;MACzB,KAAK,iCAAiC;QACpC8B,OAAO,CAACqM,OAAO,GAAG;UAChBnO,IAAI,EAAE,MAAM;UACZoO,IAAI,EAAE,IAAI,CAACvO,eAAe;UAC1BwO,QAAQ,EAAE;QACZ,CAAC;QACD;MAEF,KAAK,qCAAqC;QACxCvM,OAAO,CAACqM,OAAO,GAAG;UAChBnO,IAAI,EAAE,eAAe;UACrBoO,IAAI,EAAE,IAAI,CAACvO,eAAe;UAC1ByO,YAAY,EAAExO,cAAc,CAACG,OAAO,CAACO;QACvC,CAAC;QACD;MAEF,KAAK,+BAA+B;MACpC,KAAK,gCAAgC;MACrC,KAAK,wCAAwC;MAC7C,KAAK,iDAAiD;QACpDsB,OAAO,CAACqM,OAAO,GAAG;UAChBnO,IAAI,EAAE,MAAM;UACZoO,IAAI,EAAE,IAAI,CAACvO,eAAe;UAC1BwO,QAAQ,EAAE;QACZ,CAAC;QACD;MAEF,KAAK,MAAM;QACTvM,OAAO,CAACyM,IAAI,GAAG,IAAAC,uBAAiB,EAAC;UAAEtO,MAAM,EAAEJ,cAAc,CAACG,OAAO,CAACC;QAAO,CAAC,CAAC;QAC3E;MAEF;QACE4B,OAAO,CAAC3B,QAAQ,GAAGL,cAAc,CAACG,OAAO,CAACE,QAAQ;QAClD2B,OAAO,CAAC1B,QAAQ,GAAGN,cAAc,CAACG,OAAO,CAACG,QAAQ;IACtD;IAEA0B,OAAO,CAAC2M,QAAQ,GAAG,IAAI,CAAC/O,MAAM,CAACO,OAAO,CAAC+D,aAAa,IAAI0K,WAAE,CAACD,QAAQ,CAAC,CAAC;IACrE3M,OAAO,CAAC0B,UAAU,GAAG,IAAI,CAACmH,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/K,MAAM,GAAG,IAAI,CAACF,MAAM,CAACE,MAAM;IACpFkC,OAAO,CAACnB,OAAO,GAAG,IAAI,CAACjB,MAAM,CAACO,OAAO,CAACU,OAAO,IAAI,SAAS;IAC1DmB,OAAO,CAAC6M,WAAW,GAAGA,aAAW;IACjC7M,OAAO,CAACgB,QAAQ,GAAG,IAAI,CAACpD,MAAM,CAACO,OAAO,CAAC6C,QAAQ;IAC/ChB,OAAO,CAACN,QAAQ,GAAG,IAAI,CAAC9B,MAAM,CAACO,OAAO,CAACuB,QAAQ;IAC/CM,OAAO,CAACxB,QAAQ,GAAGwE,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAElDjD,OAAO,CAACsB,cAAc,GAAG,IAAI,CAAC1D,MAAM,CAACO,OAAO,CAACmD,cAAc;IAC3DtB,OAAO,CAAC8M,WAAW,GAAG,CAAC,IAAI,CAAClP,MAAM,CAACO,OAAO,CAACyC,mBAAmB;IAE9D,IAAI,CAACiI,WAAW,GAAG5K,SAAS;IAC5B,IAAI,CAAC4F,SAAS,CAACC,WAAW,CAACC,YAAI,CAACgJ,MAAM,EAAE/M,OAAO,CAACgN,QAAQ,CAAC,CAAC,CAAC;IAE3D,IAAI,CAACnN,KAAK,CAACG,OAAO,CAAC,YAAW;MAC5B,OAAOA,OAAO,CAACyL,QAAQ,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEwB,uBAAuBA,CAACvO,KAAa,EAAE;IACrC,MAAMwO,cAAc,GAAGlK,MAAM,CAACmK,UAAU,CAACzO,KAAK,EAAE,MAAM,CAAC;IACvD,MAAMoB,IAAI,GAAGkD,MAAM,CAACM,KAAK,CAAC,CAAC,GAAG4J,cAAc,CAAC;IAC7C,IAAIE,MAAM,GAAG,CAAC;IACdA,MAAM,GAAGtN,IAAI,CAACuN,aAAa,CAACH,cAAc,GAAG,CAAC,EAAEE,MAAM,CAAC;IACvDA,MAAM,GAAGtN,IAAI,CAACuN,aAAa,CAACH,cAAc,EAAEE,MAAM,CAAC;IACnDtN,IAAI,CAACwN,KAAK,CAAC5O,KAAK,EAAE0O,MAAM,EAAE,MAAM,CAAC;IACjC,IAAI,CAACvJ,SAAS,CAACC,WAAW,CAACC,YAAI,CAACwJ,aAAa,EAAEzN,IAAI,CAAC;IACpD;IACA,IAAI,CAAC6E,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC6J,+BAA+B,CAAC;EAC/D;;EAEA;AACF;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,MAAMzN,OAAO,GAAG,IAAI0N,wBAAe,CAAC,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,CAAC;IAEnH,MAAM4H,OAAO,GAAG,IAAI8H,gBAAO,CAAC;MAAE3P,IAAI,EAAE6F,YAAI,CAAC+J;IAAU,CAAC,CAAC;IACrD,IAAI,CAACjK,SAAS,CAACkK,qBAAqB,CAACT,KAAK,CAACvH,OAAO,CAAC;IACnDiI,gBAAQ,CAAC/K,IAAI,CAACjD,OAAO,CAAC,CAACiO,IAAI,CAAClI,OAAO,CAAC;EACtC;;EAEA;AACF;AACA;EACE4H,aAAaA,CAAA,EAAG;IACd,MAAMxP,OAAO,GAAG,EAAE;IAElB,IAAI,IAAI,CAACP,MAAM,CAACO,OAAO,CAAC8B,cAAc,KAAK,IAAI,EAAE;MAC/C9B,OAAO,CAAC+P,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC8B,cAAc,KAAK,KAAK,EAAE;MACvD9B,OAAO,CAAC+P,IAAI,CAAC,oBAAoB,CAAC;IACpC;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,KAAK,IAAI,EAAE;MACtD/B,OAAO,CAAC+P,IAAI,CAAC,0BAA0B,CAAC;IAC1C,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC+B,qBAAqB,KAAK,KAAK,EAAE;MAC9D/B,OAAO,CAAC+P,IAAI,CAAC,2BAA2B,CAAC;IAC3C;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACgC,iBAAiB,KAAK,IAAI,EAAE;MAClDhC,OAAO,CAAC+P,IAAI,CAAC,qBAAqB,CAAC;IACrC,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACgC,iBAAiB,KAAK,KAAK,EAAE;MAC1DhC,OAAO,CAAC+P,IAAI,CAAC,sBAAsB,CAAC;IACtC;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACiC,kBAAkB,KAAK,IAAI,EAAE;MACnDjC,OAAO,CAAC+P,IAAI,CAAC,sBAAsB,CAAC;IACtC,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACiC,kBAAkB,KAAK,KAAK,EAAE;MAC3DjC,OAAO,CAAC+P,IAAI,CAAC,uBAAuB,CAAC;IACvC;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACkC,gBAAgB,KAAK,IAAI,EAAE;MACjDlC,OAAO,CAAC+P,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACkC,gBAAgB,KAAK,KAAK,EAAE;MACzDlC,OAAO,CAAC+P,IAAI,CAAC,oBAAoB,CAAC;IACpC;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACmC,0BAA0B,KAAK,IAAI,EAAE;MAC3DnC,OAAO,CAAC+P,IAAI,CAAC,gCAAgC,CAAC;IAChD,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACmC,0BAA0B,KAAK,KAAK,EAAE;MACnEnC,OAAO,CAAC+P,IAAI,CAAC,iCAAiC,CAAC;IACjD;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACoC,yBAAyB,KAAK,IAAI,EAAE;MAC1DpC,OAAO,CAAC+P,IAAI,CAAC,+BAA+B,CAAC;IAC/C,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACoC,yBAAyB,KAAK,KAAK,EAAE;MAClEpC,OAAO,CAAC+P,IAAI,CAAC,gCAAgC,CAAC;IAChD;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACwB,SAAS,KAAK,IAAI,EAAE;MAC1CxB,OAAO,CAAC+P,IAAI,CAAE,iBAAgB,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACwB,SAAU,EAAC,CAAC;IAChE;IAEA,IAAI,IAAI,CAAC/B,MAAM,CAACO,OAAO,CAACyB,UAAU,KAAK,IAAI,EAAE;MAC3CzB,OAAO,CAAC+P,IAAI,CAAE,kBAAiB,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACyB,UAAW,EAAC,CAAC;IAClE;IAEA,IAAI,IAAI,CAAChC,MAAM,CAACO,OAAO,CAACqC,0BAA0B,KAAK,IAAI,EAAE;MAC3DrC,OAAO,CAAC+P,IAAI,CAAC,8BAA8B,CAAC;IAC9C,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACqC,0BAA0B,KAAK,KAAK,EAAE;MACnErC,OAAO,CAAC+P,IAAI,CAAC,+BAA+B,CAAC;IAC/C;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC6C,QAAQ,KAAK,IAAI,EAAE;MACzC7C,OAAO,CAAC+P,IAAI,CAAE,gBAAe,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC6C,QAAS,EAAC,CAAC;IAC9D;IAEA,IAAI,IAAI,CAACpD,MAAM,CAACO,OAAO,CAACsC,uBAAuB,KAAK,IAAI,EAAE;MACxDtC,OAAO,CAAC+P,IAAI,CAAC,2BAA2B,CAAC;IAC3C,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACsC,uBAAuB,KAAK,KAAK,EAAE;MAChEtC,OAAO,CAAC+P,IAAI,CAAC,4BAA4B,CAAC;IAC5C;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACuC,sBAAsB,KAAK,IAAI,EAAE;MACvDvC,OAAO,CAAC+P,IAAI,CAAC,0BAA0B,CAAC;IAC1C,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACuC,sBAAsB,KAAK,KAAK,EAAE;MAC/DvC,OAAO,CAAC+P,IAAI,CAAC,2BAA2B,CAAC;IAC3C;IAEA,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC0D,QAAQ,KAAK,IAAI,EAAE;MACzC1D,OAAO,CAAC+P,IAAI,CAAE,gBAAe,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAAC0D,QAAS,EAAC,CAAC;IAC9D;IAEA,IAAI,IAAI,CAACjE,MAAM,CAACO,OAAO,CAACmB,wBAAwB,KAAK,IAAI,EAAE;MACzDnB,OAAO,CAAC+P,IAAI,CAAE,mCAAkC,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACvQ,MAAM,CAACO,OAAO,CAACmB,wBAAwB,CAAE,EAAC,CAAC;IAC7H;IAEA,IAAI,IAAI,CAAC1B,MAAM,CAACO,OAAO,CAACS,uBAAuB,KAAK,IAAI,EAAE;MACxDT,OAAO,CAAC+P,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC,MAAM,IAAI,IAAI,CAACtQ,MAAM,CAACO,OAAO,CAACS,uBAAuB,KAAK,KAAK,EAAE;MAChET,OAAO,CAAC+P,IAAI,CAAC,oBAAoB,CAAC;IACpC;IAEA,OAAO/P,OAAO,CAACiQ,IAAI,CAAC,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;EACEC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACxI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACb,IAAI,CAAC,SAAS,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsJ,YAAYA,CAACjI,OAAgB,EAAE;IAC7B,IAAI,CAACkI,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAAC+J,SAAS,EAAE,IAAIJ,wBAAe,CAACrH,OAAO,CAACmI,kBAAkB,EAAG,IAAI,CAACZ,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,CAAC,CAAC;EACvJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsQ,OAAOA,CAACpI,OAAgB,EAAE;IACxB,IAAI;MACFA,OAAO,CAACqI,kBAAkB,CAAC,IAAI,CAACC,iBAAiB,CAAC;IACpD,CAAC,CAAC,OAAO5H,KAAU,EAAE;MACnBV,OAAO,CAACU,KAAK,GAAGA,KAAK;MAErBpB,OAAO,CAACC,QAAQ,CAAC,MAAM;QACrB,IAAI,CAAC/F,KAAK,CAACyH,GAAG,CAACP,KAAK,CAAChB,OAAO,CAAC;QAC7BM,OAAO,CAACE,QAAQ,CAACQ,KAAK,CAAC;MACzB,CAAC,CAAC;MAEF;IACF;IAEA,MAAM6H,UAAuB,GAAG,EAAE;IAElCA,UAAU,CAACV,IAAI,CAAC;MACdhQ,IAAI,EAAE2Q,eAAK,CAACC,QAAQ;MACpBzK,IAAI,EAAE,WAAW;MACjB3B,KAAK,EAAE2D,OAAO,CAACmI,kBAAkB;MACjCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEF,IAAIoI,OAAO,CAACuI,UAAU,CAACI,MAAM,EAAE;MAC7BJ,UAAU,CAACV,IAAI,CAAC;QACdhQ,IAAI,EAAE2Q,eAAK,CAACC,QAAQ;QACpBzK,IAAI,EAAE,QAAQ;QACd3B,KAAK,EAAE2D,OAAO,CAAC8I,mBAAmB,CAAC9I,OAAO,CAACuI,UAAU,CAAC;QACtDG,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE/Q,SAAS;QACjBgR,SAAS,EAAEhR,SAAS;QACpBiR,KAAK,EAAEjR;MACT,CAAC,CAAC;MAEF2Q,UAAU,CAACV,IAAI,CAAC,GAAG7H,OAAO,CAACuI,UAAU,CAAC;IACxC;IAEA,IAAI,CAACL,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAACqL,WAAW,EAAE,IAAIC,0BAAiB,CAACC,+BAAU,CAACC,aAAa,EAAEX,UAAU,EAAE,IAAI,CAAChB,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,EAAE,IAAI,CAACwQ,iBAAiB,CAAC,CAAC;EAC5L;;EAEA;AACF;AACA;AACA;AACA;AACA;;EAGEa,WAAWA,CAACC,KAAa,EAAEC,iBAAqD,EAAEnJ,QAA2B,EAAE;IAC7G,IAAIpI,OAAwB;IAE5B,IAAIoI,QAAQ,KAAKtI,SAAS,EAAE;MAC1BsI,QAAQ,GAAGmJ,iBAAqC;MAChDvR,OAAO,GAAG,CAAC,CAAC;IACd,CAAC,MAAM;MACLA,OAAO,GAAGuR,iBAAoC;IAChD;IAEA,IAAI,OAAOvR,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAM,IAAIN,SAAS,CAAC,sCAAsC,CAAC;IAC7D;IACA,OAAO,IAAI8R,iBAAQ,CAACF,KAAK,EAAE,IAAI,CAACd,iBAAiB,EAAE,IAAI,CAAC/Q,MAAM,CAACO,OAAO,EAAEA,OAAO,EAAEoI,QAAQ,CAAC;EAC5F;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGEqJ,YAAYA,CAACC,QAAkB,EAAEC,IAA6H,EAAE;IAC9JD,QAAQ,CAACE,gBAAgB,GAAG,IAAI;IAEhC,IAAID,IAAI,EAAE;MACR,IAAID,QAAQ,CAACG,aAAa,EAAE;QAC1B,MAAM,IAAI5N,KAAK,CAAC,yFAAyF,CAAC;MAC5G;MAEA,IAAIyN,QAAQ,CAACI,eAAe,EAAE;QAC5B,MAAM,IAAI7N,KAAK,CAAC,8FAA8F,CAAC;MACjH;MAEA,MAAM8N,SAAS,GAAGlC,gBAAQ,CAAC/K,IAAI,CAAC6M,IAAI,CAAC;;MAErC;MACA;MACAI,SAAS,CAACrL,EAAE,CAAC,OAAO,EAAGN,GAAG,IAAK;QAC7BsL,QAAQ,CAACM,oBAAoB,CAAC5H,OAAO,CAAChE,GAAG,CAAC;MAC5C,CAAC,CAAC;;MAEF;MACA;MACAsL,QAAQ,CAACM,oBAAoB,CAACtL,EAAE,CAAC,OAAO,EAAGN,GAAG,IAAK;QACjD2L,SAAS,CAAC3H,OAAO,CAAChE,GAAG,CAAC;MACxB,CAAC,CAAC;MAEF2L,SAAS,CAACjC,IAAI,CAAC4B,QAAQ,CAACM,oBAAoB,CAAC;IAC/C,CAAC,MAAM,IAAI,CAACN,QAAQ,CAACG,aAAa,EAAE;MAClC;MACA;MACA;MACA;MACA;MACAH,QAAQ,CAACM,oBAAoB,CAACjH,GAAG,CAAC,CAAC;IACrC;IAEA,MAAMkH,QAAQ,GAAGA,CAAA,KAAM;MACrB/J,OAAO,CAAC8D,MAAM,CAAC,CAAC;IAClB,CAAC;IAED,MAAMnK,OAAO,GAAG,IAAIqQ,gCAAe,CAACR,QAAQ,CAAC;IAE7C,MAAMxJ,OAAO,GAAG,IAAIiK,gBAAO,CAACT,QAAQ,CAACU,gBAAgB,CAAC,CAAC,EAAGxJ,KAAqD,IAAK;MAClH8I,QAAQ,CAACrL,cAAc,CAAC,QAAQ,EAAE4L,QAAQ,CAAC;MAE3C,IAAIrJ,KAAK,EAAE;QACT,IAAIA,KAAK,CAAC8D,IAAI,KAAK,SAAS,EAAE;UAC5B9D,KAAK,CAAChB,OAAO,IAAI,8HAA8H;QACjJ;QACA8J,QAAQ,CAAC9I,KAAK,GAAGA,KAAK;QACtB8I,QAAQ,CAACtJ,QAAQ,CAACQ,KAAK,CAAC;QACxB;MACF;MAEA,IAAI,CAACwH,WAAW,CAACsB,QAAQ,EAAE9L,YAAI,CAACyM,SAAS,EAAExQ,OAAO,CAAC;IACrD,CAAC,CAAC;IAEF6P,QAAQ,CAACnL,IAAI,CAAC,QAAQ,EAAE0L,QAAQ,CAAC;IAEjC,IAAI,CAAC9B,YAAY,CAACjI,OAAO,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoK,OAAOA,CAACpK,OAAgB,EAAE;IACxB,MAAMuI,UAAuB,GAAG,EAAE;IAElCA,UAAU,CAACV,IAAI,CAAC;MACdhQ,IAAI,EAAE2Q,eAAK,CAAC6B,GAAG;MACfrM,IAAI,EAAE,QAAQ;MACd3B,KAAK,EAAEzE,SAAS;MAChB8Q,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEF2Q,UAAU,CAACV,IAAI,CAAC;MACdhQ,IAAI,EAAE2Q,eAAK,CAACC,QAAQ;MACpBzK,IAAI,EAAE,QAAQ;MACd3B,KAAK,EAAE2D,OAAO,CAACuI,UAAU,CAACI,MAAM,GAAG3I,OAAO,CAAC8I,mBAAmB,CAAC9I,OAAO,CAACuI,UAAU,CAAC,GAAG,IAAI;MACzFG,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEF2Q,UAAU,CAACV,IAAI,CAAC;MACdhQ,IAAI,EAAE2Q,eAAK,CAACC,QAAQ;MACpBzK,IAAI,EAAE,MAAM;MACZ3B,KAAK,EAAE2D,OAAO,CAACmI,kBAAkB;MACjCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEFoI,OAAO,CAACsK,SAAS,GAAG,IAAI;;IAExB;IACAtK,OAAO,CAACxB,EAAE,CAAC,aAAa,EAAE,CAACR,IAAY,EAAE3B,KAAU,KAAK;MACtD,IAAI2B,IAAI,KAAK,QAAQ,EAAE;QACrBgC,OAAO,CAACuK,MAAM,GAAGlO,KAAK;MACxB,CAAC,MAAM;QACL2D,OAAO,CAACU,KAAK,GAAG,IAAIT,oBAAY,CAAE,yCAAwCjC,IAAK,kBAAiB,CAAC;MACnG;IACF,CAAC,CAAC;IAEF,IAAI,CAACkK,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAACqL,WAAW,EAAE,IAAIC,0BAAiB,CAACC,+BAAU,CAACuB,UAAU,EAAEjC,UAAU,EAAE,IAAI,CAAChB,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,EAAE,IAAI,CAACwQ,iBAAiB,CAAC,CAAC;EACzL;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmC,SAASA,CAACzK,OAAgB,EAAE;IAC1B,MAAMuI,UAAuB,GAAG,EAAE;IAElCA,UAAU,CAACV,IAAI,CAAC;MACdhQ,IAAI,EAAE2Q,eAAK,CAAC6B,GAAG;MACfrM,IAAI,EAAE,QAAQ;MACd;MACA3B,KAAK,EAAE2D,OAAO,CAACuK,MAAM;MACrB7B,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEF,IAAI,CAACsQ,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAACqL,WAAW,EAAE,IAAIC,0BAAiB,CAACC,+BAAU,CAACyB,YAAY,EAAEnC,UAAU,EAAE,IAAI,CAAChB,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,EAAE,IAAI,CAACwQ,iBAAiB,CAAC,CAAC;EAC3L;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEqC,OAAOA,CAAC3K,OAAgB,EAAEuI,UAAuC,EAAE;IACjE,MAAMqC,iBAA8B,GAAG,EAAE;IAEzCA,iBAAiB,CAAC/C,IAAI,CAAC;MACrBhQ,IAAI,EAAE2Q,eAAK,CAAC6B,GAAG;MACfrM,IAAI,EAAE,EAAE;MACR;MACA3B,KAAK,EAAE2D,OAAO,CAACuK,MAAM;MACrB7B,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE/Q,SAAS;MACjBgR,SAAS,EAAEhR,SAAS;MACpBiR,KAAK,EAAEjR;IACT,CAAC,CAAC;IAEF,IAAI;MACF,KAAK,IAAIiT,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG9K,OAAO,CAACuI,UAAU,CAACI,MAAM,EAAEkC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC7D,MAAME,SAAS,GAAG/K,OAAO,CAACuI,UAAU,CAACsC,CAAC,CAAC;QAEvCD,iBAAiB,CAAC/C,IAAI,CAAC;UACrB,GAAGkD,SAAS;UACZ1O,KAAK,EAAE0O,SAAS,CAAClT,IAAI,CAACmT,QAAQ,CAACzC,UAAU,GAAGA,UAAU,CAACwC,SAAS,CAAC/M,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,CAACsK,iBAAiB;QACvG,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO5H,KAAU,EAAE;MACnBV,OAAO,CAACU,KAAK,GAAGA,KAAK;MAErBpB,OAAO,CAACC,QAAQ,CAAC,MAAM;QACrB,IAAI,CAAC/F,KAAK,CAACyH,GAAG,CAACP,KAAK,CAAChB,OAAO,CAAC;QAC7BM,OAAO,CAACE,QAAQ,CAACQ,KAAK,CAAC;MACzB,CAAC,CAAC;MAEF;IACF;IAEA,IAAI,CAACwH,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAACqL,WAAW,EAAE,IAAIC,0BAAiB,CAACC,+BAAU,CAACgC,UAAU,EAAEL,iBAAiB,EAAE,IAAI,CAACrD,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,EAAE,IAAI,CAACwQ,iBAAiB,CAAC,CAAC;EAChM;;EAEA;AACF;AACA;AACA;AACA;EACE4C,aAAaA,CAAClL,OAAgB,EAAE;IAC9B,IAAI;MACFA,OAAO,CAACqI,kBAAkB,CAAC,IAAI,CAACC,iBAAiB,CAAC;IACpD,CAAC,CAAC,OAAO5H,KAAU,EAAE;MACnBV,OAAO,CAACU,KAAK,GAAGA,KAAK;MAErBpB,OAAO,CAACC,QAAQ,CAAC,MAAM;QACrB,IAAI,CAAC/F,KAAK,CAACyH,GAAG,CAACP,KAAK,CAAChB,OAAO,CAAC;QAC7BM,OAAO,CAACE,QAAQ,CAACQ,KAAK,CAAC;MACzB,CAAC,CAAC;MAEF;IACF;IAEA,IAAI,CAACwH,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAACqL,WAAW,EAAE,IAAIC,0BAAiB,CAAChJ,OAAO,CAACmI,kBAAkB,EAAGnI,OAAO,CAACuI,UAAU,EAAE,IAAI,CAAChB,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAChQ,MAAM,CAACO,OAAO,EAAE,IAAI,CAACwQ,iBAAiB,CAAC,CAAC;EACvM;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6C,gBAAgBA,CAACjL,QAAkC,EAAElC,IAAI,GAAG,EAAE,EAAEtD,cAAc,GAAG,IAAI,CAACnD,MAAM,CAACO,OAAO,CAAC4C,cAAc,EAAE;IACnH,IAAAsB,sCAAyB,EAACtB,cAAc,EAAE,gBAAgB,CAAC;IAE3D,MAAM0Q,WAAW,GAAG,IAAIC,wBAAW,CAACrN,IAAI,EAAEtD,cAAc,CAAC;IAEzD,IAAI,IAAI,CAACnD,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;MAC1C,OAAO,IAAI,CAAC0M,YAAY,CAAC,IAAIgC,gBAAO,CAAC,kCAAkC,GAAImB,WAAW,CAACE,oBAAoB,CAAC,CAAE,GAAG,cAAc,GAAGF,WAAW,CAACpN,IAAI,EAAGE,GAAG,IAAK;QAC3J,IAAI,CAACrB,gBAAgB,EAAE;QACvB,IAAI,IAAI,CAACA,gBAAgB,KAAK,CAAC,EAAE;UAC/B,IAAI,CAACJ,aAAa,GAAG,IAAI;QAC3B;QACAyD,QAAQ,CAAChC,GAAG,CAAC;MACf,CAAC,CAAC,CAAC;IACL;IAEA,MAAM8B,OAAO,GAAG,IAAIiK,gBAAO,CAACrS,SAAS,EAAGsG,GAAG,IAAK;MAC9C,OAAOgC,QAAQ,CAAChC,GAAG,EAAE,IAAI,CAACqJ,4BAA4B,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;IACF,OAAO,IAAI,CAACW,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAAC6N,mBAAmB,EAAEH,WAAW,CAACI,YAAY,CAAC,IAAI,CAACjE,4BAA4B,CAAC,CAAC,CAAC,CAAC;EAC3H;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkE,iBAAiBA,CAACvL,QAAmC,EAAElC,IAAI,GAAG,EAAE,EAAE;IAChE,MAAMoN,WAAW,GAAG,IAAIC,wBAAW,CAACrN,IAAI,CAAC;IACzC,IAAI,IAAI,CAACzG,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;MAC1C,OAAO,IAAI,CAAC0M,YAAY,CAAC,IAAIgC,gBAAO,CAAC,cAAc,GAAGmB,WAAW,CAACpN,IAAI,EAAGE,GAAG,IAAK;QAC/E,IAAI,CAACrB,gBAAgB,EAAE;QACvB,IAAI,IAAI,CAACA,gBAAgB,KAAK,CAAC,EAAE;UAC/B,IAAI,CAACJ,aAAa,GAAG,KAAK;QAC5B;QAEAyD,QAAQ,CAAChC,GAAG,CAAC;MACf,CAAC,CAAC,CAAC;IACL;IACA,MAAM8B,OAAO,GAAG,IAAIiK,gBAAO,CAACrS,SAAS,EAAEsI,QAAQ,CAAC;IAChD,OAAO,IAAI,CAACgI,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAAC6N,mBAAmB,EAAEH,WAAW,CAACM,aAAa,CAAC,IAAI,CAACnE,4BAA4B,CAAC,CAAC,CAAC,CAAC;EAC5H;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoE,mBAAmBA,CAACzL,QAAqC,EAAElC,IAAI,GAAG,EAAE,EAAE;IACpE,MAAMoN,WAAW,GAAG,IAAIC,wBAAW,CAACrN,IAAI,CAAC;IACzC,IAAI,IAAI,CAACzG,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;MAC1C,OAAO,IAAI,CAAC0M,YAAY,CAAC,IAAIgC,gBAAO,CAAC,gBAAgB,GAAGmB,WAAW,CAACpN,IAAI,EAAGE,GAAG,IAAK;QACjF,IAAI,CAACrB,gBAAgB,EAAE;QACvB,IAAI,IAAI,CAACA,gBAAgB,KAAK,CAAC,EAAE;UAC/B,IAAI,CAACJ,aAAa,GAAG,KAAK;QAC5B;QACAyD,QAAQ,CAAChC,GAAG,CAAC;MACf,CAAC,CAAC,CAAC;IACL;IACA,MAAM8B,OAAO,GAAG,IAAIiK,gBAAO,CAACrS,SAAS,EAAEsI,QAAQ,CAAC;IAChD,OAAO,IAAI,CAACgI,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAAC6N,mBAAmB,EAAEH,WAAW,CAACQ,eAAe,CAAC,IAAI,CAACrE,4BAA4B,CAAC,CAAC,CAAC,CAAC;EAC9H;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsE,eAAeA,CAAC3L,QAAiC,EAAElC,IAAY,EAAE;IAC/D,MAAMoN,WAAW,GAAG,IAAIC,wBAAW,CAACrN,IAAI,CAAC;IACzC,IAAI,IAAI,CAACzG,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;MAC1C,OAAO,IAAI,CAAC0M,YAAY,CAAC,IAAIgC,gBAAO,CAAC,YAAY,GAAGmB,WAAW,CAACpN,IAAI,EAAGE,GAAG,IAAK;QAC7E,IAAI,CAACrB,gBAAgB,EAAE;QACvBqD,QAAQ,CAAChC,GAAG,CAAC;MACf,CAAC,CAAC,CAAC;IACL;IACA,MAAM8B,OAAO,GAAG,IAAIiK,gBAAO,CAACrS,SAAS,EAAEsI,QAAQ,CAAC;IAChD,OAAO,IAAI,CAACgI,WAAW,CAAClI,OAAO,EAAEtC,YAAI,CAAC6N,mBAAmB,EAAEH,WAAW,CAACU,WAAW,CAAC,IAAI,CAACvE,4BAA4B,CAAC,CAAC,CAAC,CAAC;EAC1H;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6D,WAAWA,CAACW,EAAyK,EAAErR,cAAqE,EAAE;IAC5P,IAAI,OAAOqR,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIvU,SAAS,CAAC,yBAAyB,CAAC;IAChD;IAEA,MAAMwU,YAAY,GAAG,IAAI,CAACvP,aAAa;IACvC,MAAMuB,IAAI,GAAG,WAAW,GAAIiO,eAAM,CAACC,WAAW,CAAC,EAAE,CAAC,CAAC9G,QAAQ,CAAC,KAAK,CAAE;IACnE,MAAM+G,MAA2H,GAAGA,CAACjO,GAAG,EAAEkO,IAAI,EAAE,GAAGxN,IAAI,KAAK;MAC1J,IAAIV,GAAG,EAAE;QACP,IAAI,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACY,KAAK,KAAK,IAAI,CAACC,KAAK,CAAC+O,SAAS,EAAE;UAC7D,IAAI,CAACV,mBAAmB,CAAEW,KAAK,IAAK;YAClCF,IAAI,CAACE,KAAK,IAAIpO,GAAG,EAAE,GAAGU,IAAI,CAAC;UAC7B,CAAC,EAAEZ,IAAI,CAAC;QACV,CAAC,MAAM;UACLoO,IAAI,CAAClO,GAAG,EAAE,GAAGU,IAAI,CAAC;QACpB;MACF,CAAC,MAAM,IAAIoN,YAAY,EAAE;QACvB,IAAI,IAAI,CAACzU,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;UAC1C,IAAI,CAACsB,gBAAgB,EAAE;QACzB;QACAuP,IAAI,CAAC,IAAI,EAAE,GAAGxN,IAAI,CAAC;MACrB,CAAC,MAAM;QACL,IAAI,CAAC6M,iBAAiB,CAAEa,KAAK,IAAK;UAChCF,IAAI,CAACE,KAAK,EAAE,GAAG1N,IAAI,CAAC;QACtB,CAAC,EAAEZ,IAAI,CAAC;MACV;IACF,CAAC;IAED,IAAIgO,YAAY,EAAE;MAChB,OAAO,IAAI,CAACH,eAAe,CAAE3N,GAAG,IAAK;QACnC,IAAIA,GAAG,EAAE;UACP,OAAO6N,EAAE,CAAC7N,GAAG,CAAC;QAChB;QAEA,IAAIxD,cAAc,EAAE;UAClB,OAAO,IAAI,CAACuN,YAAY,CAAC,IAAIgC,gBAAO,CAAC,kCAAkC,GAAG,IAAI,CAACnC,qBAAqB,CAACpN,cAAc,CAAC,EAAGwD,GAAG,IAAK;YAC7H,OAAO6N,EAAE,CAAC7N,GAAG,EAAEiO,MAAM,CAAC;UACxB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,OAAOJ,EAAE,CAAC,IAAI,EAAEI,MAAM,CAAC;QACzB;MACF,CAAC,EAAEnO,IAAI,CAAC;IACV,CAAC,MAAM;MACL,OAAO,IAAI,CAACmN,gBAAgB,CAAEjN,GAAG,IAAK;QACpC,IAAIA,GAAG,EAAE;UACP,OAAO6N,EAAE,CAAC7N,GAAG,CAAC;QAChB;QAEA,OAAO6N,EAAE,CAAC,IAAI,EAAEI,MAAM,CAAC;MACzB,CAAC,EAAEnO,IAAI,EAAEtD,cAAc,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACEwN,WAAWA,CAAClI,OAA2B,EAAEuM,UAAkB,EAAE5S,OAA+F,EAAE;IAC5J,IAAI,IAAI,CAAC0D,KAAK,KAAK,IAAI,CAACC,KAAK,CAAC+O,SAAS,EAAE;MACvC,MAAM3M,OAAO,GAAG,mCAAmC,GAAG,IAAI,CAACpC,KAAK,CAAC+O,SAAS,CAACrO,IAAI,GAAG,kBAAkB,GAAG,IAAI,CAACX,KAAK,CAACW,IAAI,GAAG,QAAQ;MACjI,IAAI,CAACxE,KAAK,CAACyH,GAAG,CAACvB,OAAO,CAAC;MACvBM,OAAO,CAACE,QAAQ,CAAC,IAAID,oBAAY,CAACP,OAAO,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC,MAAM,IAAIM,OAAO,CAACwM,QAAQ,EAAE;MAC3BlN,OAAO,CAACC,QAAQ,CAAC,MAAM;QACrBS,OAAO,CAACE,QAAQ,CAAC,IAAID,oBAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIsM,UAAU,KAAK7O,YAAI,CAAC+J,SAAS,EAAE;QACjC,IAAI,CAAC3K,UAAU,GAAG,IAAI;MACxB,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,KAAK;MACzB;MAEA,IAAI,CAACkD,OAAO,GAAGA,OAAO;MACtBA,OAAO,CAACyM,UAAU,GAAI,IAAI;MAC1BzM,OAAO,CAAC0M,QAAQ,GAAI,CAAC;MACrB1M,OAAO,CAACyJ,IAAI,GAAI,EAAE;MAClBzJ,OAAO,CAAC2M,GAAG,GAAI,EAAE;MAEjB,MAAM5C,QAAQ,GAAGA,CAAA,KAAM;QACrB6C,aAAa,CAACC,MAAM,CAACnN,OAAO,CAAC;QAC7BkN,aAAa,CAAC1K,OAAO,CAAC,IAAIjC,oBAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;;QAE/D;QACAP,OAAO,CAACoN,MAAM,GAAG,IAAI;QACrBpN,OAAO,CAACmD,GAAG,CAAC,CAAC;QAEb,IAAI7C,OAAO,YAAYiK,gBAAO,IAAIjK,OAAO,CAAC+M,MAAM,EAAE;UAChD;UACA/M,OAAO,CAACgN,MAAM,CAAC,CAAC;QAClB;MACF,CAAC;MAEDhN,OAAO,CAAC3B,IAAI,CAAC,QAAQ,EAAE0L,QAAQ,CAAC;MAEhC,IAAI,CAACzG,kBAAkB,CAAC,CAAC;MAEzB,MAAM5D,OAAO,GAAG,IAAI8H,gBAAO,CAAC;QAAE3P,IAAI,EAAE0U,UAAU;QAAEU,eAAe,EAAE,IAAI,CAACC;MAA6B,CAAC,CAAC;MACrG,IAAI,CAAC1P,SAAS,CAACkK,qBAAqB,CAACT,KAAK,CAACvH,OAAO,CAAC;MACnD,IAAI,CAACpB,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC6P,mBAAmB,CAAC;MAEjDzN,OAAO,CAACrB,IAAI,CAAC,QAAQ,EAAE,MAAM;QAC3B2B,OAAO,CAAC7B,cAAc,CAAC,QAAQ,EAAE4L,QAAQ,CAAC;QAC1C/J,OAAO,CAAC3B,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAChH,uBAAuB,CAAC;QAEpD,IAAI,CAAC6V,4BAA4B,GAAG,KAAK;QACzC,IAAI,CAAC1T,KAAK,CAACG,OAAO,CAAC,YAAW;UAC5B,OAAOA,OAAO,CAAEyL,QAAQ,CAAC,IAAI,CAAC;QAChC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMwH,aAAa,GAAGjF,gBAAQ,CAAC/K,IAAI,CAACjD,OAAO,CAAC;MAC5CiT,aAAa,CAACvO,IAAI,CAAC,OAAO,EAAGqC,KAAK,IAAK;QACrCkM,aAAa,CAACC,MAAM,CAACnN,OAAO,CAAC;;QAE7B;QACAM,OAAO,CAACU,KAAK,KAAKA,KAAK;QAEvBhB,OAAO,CAACoN,MAAM,GAAG,IAAI;QACrBpN,OAAO,CAACmD,GAAG,CAAC,CAAC;MACf,CAAC,CAAC;MACF+J,aAAa,CAAChF,IAAI,CAAClI,OAAO,CAAC;IAC7B;EACF;;EAEA;AACF;AACA;EACEoE,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC9D,OAAO,EAAE;MACjB,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACA,OAAO,CAACwM,QAAQ,EAAE;MACzB,OAAO,KAAK;IACd;IAEA,IAAI,CAACxM,OAAO,CAAC8D,MAAM,CAAC,CAAC;IACrB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEsJ,KAAKA,CAAClN,QAAuB,EAAE;IAC7B,MAAMF,OAAO,GAAG,IAAIiK,gBAAO,CAAC,IAAI,CAAC3C,aAAa,CAAC,CAAC,EAAGpJ,GAAG,IAAK;MACzD,IAAI,IAAI,CAAC3G,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,EAAE;QAC1C,IAAI,CAACkB,aAAa,GAAG,KAAK;MAC5B;MACAyD,QAAQ,CAAChC,GAAG,CAAC;IACf,CAAC,CAAC;IACF,IAAI,CAACgP,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACjF,YAAY,CAACjI,OAAO,CAAC;EAC5B;;EAEA;AACF;AACA;EACEuH,4BAA4BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAAC7K,sBAAsB,CAAC,IAAI,CAACA,sBAAsB,CAACiM,MAAM,GAAG,CAAC,CAAC;EAC5E;;EAEA;AACF;AACA;EACEb,qBAAqBA,CAACpN,cAAoE,EAAE;IAC1F,QAAQA,cAAc;MACpB,KAAKxB,4BAAe,CAACmU,gBAAgB;QACnC,OAAO,kBAAkB;MAC3B,KAAKnU,4BAAe,CAACoU,eAAe;QAClC,OAAO,iBAAiB;MAC1B,KAAKpU,4BAAe,CAACqU,YAAY;QAC/B,OAAO,cAAc;MACvB,KAAKrU,4BAAe,CAACsU,QAAQ;QAC3B,OAAO,UAAU;MACnB;QACE,OAAO,gBAAgB;IAC3B;EACF;AACF;AAEA,SAASC,gBAAgBA,CAAC/M,KAAuC,EAAW;EAC1E,IAAIA,KAAK,YAAYgN,yBAAc,EAAE;IACnChN,KAAK,GAAGA,KAAK,CAACiN,MAAM,CAAC,CAAC,CAAC;EACzB;EACA,OAAQjN,KAAK,YAAY3C,uBAAe,IAAK,CAAC,CAAC2C,KAAK,CAACkN,WAAW;AAClE;AAAC,IAAAC,QAAA,GAEc1W,UAAU;AAAA2W,OAAA,CAAA1Y,OAAA,GAAAyY,QAAA;AACzBE,MAAM,CAACD,OAAO,GAAG3W,UAAU;AAE3BA,UAAU,CAACrB,SAAS,CAACwH,KAAK,GAAG;EAC3BC,WAAW,EAAE;IACXS,IAAI,EAAE,aAAa;IACnBsG,MAAM,EAAE,CAAC;EACX,CAAC;EACD/F,UAAU,EAAE;IACVP,IAAI,EAAE,YAAY;IAClBkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAACnF,oBAAoB,CAAC,CAAC;IAC7B,CAAC;IACDuF,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDqC,aAAa,EAAE;IACbnD,IAAI,EAAE,cAAc;IACpBkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,IAAIlH,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC;QAEnC,IAAIyC,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QAEA,WAAW,MAAMzE,IAAI,IAAIiG,OAAO,EAAE;UAChC1C,aAAa,GAAGL,MAAM,CAACsR,MAAM,CAAC,CAACjR,aAAa,EAAEvD,IAAI,CAAC,CAAC;QACtD;QAEA,MAAMyU,eAAe,GAAG,IAAIlJ,wBAAe,CAAChI,aAAa,CAAC;QAC1D,IAAI,CAACxD,KAAK,CAACG,OAAO,CAAC,YAAW;UAC5B,OAAOuU,eAAe,CAAC9I,QAAQ,CAAC,IAAI,CAAC;QACvC,CAAC,CAAC;QAEF,IAAI8I,eAAe,CAACxW,eAAe,KAAK,CAAC,EAAE;UACzC,IAAI,CAACA,eAAe,GAAG,IAAI;QAC7B;QACA,IAAI,QAAQ,KAAK,IAAI,CAACH,MAAM,CAACO,OAAO,CAACwC,OAAO,KAAK4T,eAAe,CAACC,gBAAgB,KAAK,IAAI,IAAID,eAAe,CAACC,gBAAgB,KAAK,KAAK,CAAC,EAAE;UACzI,IAAI,CAAC,IAAI,CAAC5W,MAAM,CAACO,OAAO,CAACwC,OAAO,EAAE;YAChC,IAAI,CAACqE,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC,kEAAkE,EAAE,UAAU,CAAC,CAAC;YACzH,OAAO,IAAI,CAACc,KAAK,CAAC,CAAC;UACrB;UAEA,IAAI;YAAA,IAAAuP,iBAAA;YACF,IAAI,CAAC9P,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACiH,sBAAsB,CAAC;YACpD,MAAM,IAAI,CAAC/G,SAAS,CAAC6Q,QAAQ,CAAC,IAAI,CAACnS,oBAAoB,EAAE,IAAI,CAAC3E,MAAM,CAACO,OAAO,CAACuD,UAAU,GAAG,IAAI,CAAC9D,MAAM,CAACO,OAAO,CAACuD,UAAU,GAAG,EAAA+S,iBAAA,OAAI,CAAC5L,WAAW,cAAA4L,iBAAA,uBAAhBA,iBAAA,CAAkB3W,MAAM,KAAI,IAAI,CAACF,MAAM,CAACE,MAAM,EAAE,IAAI,CAACF,MAAM,CAACO,OAAO,CAAC4D,sBAAsB,CAAC;UACxN,CAAC,CAAC,OAAOwC,GAAQ,EAAE;YACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;UAC9B;QACF;QAEA,IAAI,CAACmH,gBAAgB,CAAC,CAAC;QAEvB,MAAM;UAAE1N;QAAe,CAAC,GAAG,IAAI,CAACJ,MAAM;QAEtC,QAAQI,cAAc,CAACE,IAAI;UACzB,KAAK,iCAAiC;UACtC,KAAK,+BAA+B;UACpC,KAAK,wCAAwC;UAC7C,KAAK,iDAAiD;UACtD,KAAK,gCAAgC;YACnC,IAAI,CAACyG,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACgR,wBAAwB,CAAC;YACtD;UACF,KAAK,MAAM;YACT,IAAI,CAAChQ,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACiR,qBAAqB,CAAC;YACnD;UACF;YACE,IAAI,CAACjQ,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC6J,+BAA+B,CAAC;YAC7D;QACJ;MACF,CAAC,EAAE,CAAC,CAACrE,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACD2F,SAAS,EAAE;IACTzG,IAAI,EAAE,WAAW;IACjBkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAACvE,iBAAiB,CAAC5I,YAAY,CAACE,QAAQ,CAAC;IAC/C,CAAC;IACDqN,MAAM,EAAE;MACN5E,OAAO,EAAE,SAAAA,CAAA,EAAW,CACpB,CAAC;MACDiB,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD0P,SAAS,EAAE,SAAAA,CAAA,EAAW;QACpB,IAAI,CAAClQ,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACiB,UAAU,CAAC;MAC1C;IACF;EACF,CAAC;EACDmG,uBAAuB,EAAE;IACvB1G,IAAI,EAAE,yBAAyB;IAC/BkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAAChH,sBAAsB,EAAE;MAC7B,IAAI,CAACyC,iBAAiB,CAAC5I,YAAY,CAACG,KAAK,CAAC;IAC5C,CAAC;IACDoN,MAAM,EAAE;MACN5E,OAAO,EAAE,SAAAA,CAAA,EAAW,CACpB,CAAC;MACDiB,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD2P,KAAK,EAAE,SAAAA,CAAA,EAAW;QAChB,IAAI,CAACjL,gBAAgB,CAAC,CAAC;MACzB;IACF;EACF,CAAC;EACDe,sBAAsB,EAAE;IACtBvG,IAAI,EAAE,uBAAuB;IAC7BsG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDqI,+BAA+B,EAAE;IAC/BnJ,IAAI,EAAE,6BAA6B;IACnCkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,IAAIxE,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QAEA,MAAMoC,OAAO,GAAG,IAAIoO,2BAAkB,CAAC,IAAI,CAAC;QAC5C,MAAMC,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAEY,OAAO,CAAC;QAExE,MAAM,IAAAjC,YAAI,EAACsQ,iBAAiB,EAAE,KAAK,CAAC;QAEpC,IAAIrO,OAAO,CAACsO,gBAAgB,EAAE;UAC5B,IAAItO,OAAO,CAACkC,WAAW,EAAE;YACvB,IAAI,CAACA,WAAW,GAAGlC,OAAO,CAACkC,WAAW;YACtC,IAAI,CAAClE,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACmH,SAAS,CAAC;UACzC,CAAC,MAAM;YACL,IAAI,CAACnG,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACuR,6BAA6B,CAAC;UAC7D;QACF,CAAC,MAAM,IAAI,IAAI,CAAC1O,UAAU,EAAE;UAC1B,IAAIsN,gBAAgB,CAAC,IAAI,CAACtN,UAAU,CAAC,EAAE;YACrC,IAAI,CAAC3G,KAAK,CAACyH,GAAG,CAAC,qCAAqC,CAAC;YACrD,IAAI,CAAC3C,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACoH,uBAAuB,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAAC/F,IAAI,CAAC,SAAS,EAAE,IAAI,CAACwB,UAAU,CAAC;YACrC,IAAI,CAAC7B,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;UACrC;QACF,CAAC,MAAM;UACL,IAAI,CAACH,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;UACpE,IAAI,CAACO,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;QACrC;MACF,CAAC,EAAE,CAAC,CAACgE,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDyP,qBAAqB,EAAE;IACrBvQ,IAAI,EAAE,yBAAyB;IAC/BkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,OAAO,IAAI,EAAE;UACX,IAAIxE,OAAO;UACX,IAAI;YACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;UAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;YACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;UAC9B;UAEA,MAAMoC,OAAO,GAAG,IAAIoO,2BAAkB,CAAC,IAAI,CAAC;UAC5C,MAAMC,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAEY,OAAO,CAAC;UAExE,MAAM,IAAAjC,YAAI,EAACsQ,iBAAiB,EAAE,KAAK,CAAC;UAEpC,IAAIrO,OAAO,CAACsO,gBAAgB,EAAE;YAC5B,IAAItO,OAAO,CAACkC,WAAW,EAAE;cACvB,IAAI,CAACA,WAAW,GAAGlC,OAAO,CAACkC,WAAW;cACtC,OAAO,IAAI,CAAClE,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACmH,SAAS,CAAC;YAChD,CAAC,MAAM;cACL,OAAO,IAAI,CAACnG,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACuR,6BAA6B,CAAC;YACpE;UACF,CAAC,MAAM,IAAI,IAAI,CAACC,UAAU,EAAE;YAC1B,MAAMnX,cAAc,GAAG,IAAI,CAACJ,MAAM,CAACI,cAAoC;YAEvE,MAAMgC,OAAO,GAAG,IAAIoV,oBAAmB,CAAC;cACtChX,MAAM,EAAEJ,cAAc,CAACG,OAAO,CAACC,MAAM;cACrCC,QAAQ,EAAEL,cAAc,CAACG,OAAO,CAACE,QAAQ;cACzCC,QAAQ,EAAEN,cAAc,CAACG,OAAO,CAACG,QAAQ;cACzC6W,UAAU,EAAE,IAAI,CAACA;YACnB,CAAC,CAAC;YAEF,IAAI,CAACtR,SAAS,CAACC,WAAW,CAACC,YAAI,CAACsR,YAAY,EAAErV,OAAO,CAACF,IAAI,CAAC;YAC3D,IAAI,CAACD,KAAK,CAACG,OAAO,CAAC,YAAW;cAC5B,OAAOA,OAAO,CAACyL,QAAQ,CAAC,IAAI,CAAC;YAC/B,CAAC,CAAC;YAEF,IAAI,CAAC0J,UAAU,GAAGlX,SAAS;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACuI,UAAU,EAAE;YAC1B,IAAIsN,gBAAgB,CAAC,IAAI,CAACtN,UAAU,CAAC,EAAE;cACrC,IAAI,CAAC3G,KAAK,CAACyH,GAAG,CAAC,qCAAqC,CAAC;cACrD,OAAO,IAAI,CAAC3C,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACoH,uBAAuB,CAAC;YAC9D,CAAC,MAAM;cACL,IAAI,CAAC/F,IAAI,CAAC,SAAS,EAAE,IAAI,CAACwB,UAAU,CAAC;cACrC,OAAO,IAAI,CAAC7B,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;YAC5C;UACF,CAAC,MAAM;YACL,IAAI,CAACH,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YACpE,OAAO,IAAI,CAACO,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;UAC5C;QACF;MAEF,CAAC,EAAE,CAAC,CAACgE,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDwP,wBAAwB,EAAE;IACxBtQ,IAAI,EAAE,uBAAuB;IAC7BkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,IAAIxE,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QAEA,MAAMoC,OAAO,GAAG,IAAIoO,2BAAkB,CAAC,IAAI,CAAC;QAC5C,MAAMC,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAEY,OAAO,CAAC;QACxE,MAAM,IAAAjC,YAAI,EAACsQ,iBAAiB,EAAE,KAAK,CAAC;QACpC,IAAIrO,OAAO,CAACsO,gBAAgB,EAAE;UAC5B,IAAItO,OAAO,CAACkC,WAAW,EAAE;YACvB,IAAI,CAACA,WAAW,GAAGlC,OAAO,CAACkC,WAAW;YACtC,IAAI,CAAClE,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACmH,SAAS,CAAC;UACzC,CAAC,MAAM;YACL,IAAI,CAACnG,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACuR,6BAA6B,CAAC;UAC7D;UAEA;QACF;QAEA,MAAMI,gBAAgB,GAAG3O,OAAO,CAAC2O,gBAAgB;QAEjD,IAAIA,gBAAgB,IAAIA,gBAAgB,CAACC,MAAM,IAAID,gBAAgB,CAACE,GAAG,EAAE;UACvE,MAAMxX,cAAc,GAAG,IAAI,CAACJ,MAAM,CAACI,cAAiP;UACpR,MAAMyX,UAAU,GAAG,IAAIC,QAAG,CAAC,WAAW,EAAEJ,gBAAgB,CAACE,GAAG,CAAC,CAAC/J,QAAQ,CAAC,CAAC;UAExE,IAAIkK,WAAW;UAEf,QAAQ3X,cAAc,CAACE,IAAI;YACzB,KAAK,iCAAiC;cACpCyX,WAAW,GAAG,IAAIC,oCAA0B,CAC1C5X,cAAc,CAACG,OAAO,CAACM,QAAQ,IAAI,QAAQ,EAC3CT,cAAc,CAACG,OAAO,CAACK,QAAQ,EAC/BR,cAAc,CAACG,OAAO,CAACE,QAAQ,EAC/BL,cAAc,CAACG,OAAO,CAACG,QACzB,CAAC;cACD;YACF,KAAK,+BAA+B;YACpC,KAAK,wCAAwC;cAC3C,MAAMuX,OAAO,GAAG7X,cAAc,CAACG,OAAO,CAACK,QAAQ,GAAG,CAACR,cAAc,CAACG,OAAO,CAACK,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cAC9FmX,WAAW,GAAG,IAAIG,mCAAyB,CAAC,GAAGD,OAAO,CAAC;cACvD;YACF,KAAK,gCAAgC;cACnC,MAAM5Q,IAAI,GAAGjH,cAAc,CAACG,OAAO,CAACK,QAAQ,GAAG;gBAAEuX,uBAAuB,EAAE/X,cAAc,CAACG,OAAO,CAACK;cAAS,CAAC,GAAG,CAAC,CAAC;cAChHmX,WAAW,GAAG,IAAIK,gCAAsB,CAAC/Q,IAAI,CAAC;cAC9C;YACF,KAAK,iDAAiD;cACpD0Q,WAAW,GAAG,IAAIM,gCAAsB,CACtCjY,cAAc,CAACG,OAAO,CAACM,QAAQ,EAC/BT,cAAc,CAACG,OAAO,CAACK,QAAQ,EAC/BR,cAAc,CAACG,OAAO,CAACQ,YACzB,CAAC;cACD;UACJ;UAEA,IAAIuX,aAAa;UACjB,IAAI;YACFA,aAAa,GAAG,MAAMP,WAAW,CAACQ,QAAQ,CAACV,UAAU,CAAC;UACxD,CAAC,CAAC,OAAOlR,GAAG,EAAE;YACZ,IAAI,CAACiC,UAAU,GAAG,IAAIuN,yBAAc,CAClC,CAAC,IAAI3P,uBAAe,CAAC,0DAA0D,EAAE,UAAU,CAAC,EAAEG,GAAG,CAAC,CAAC;YACrG,IAAI,CAACS,IAAI,CAAC,SAAS,EAAE,IAAI,CAACwB,UAAU,CAAC;YACrC,IAAI,CAAC7B,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;YACnC;UACF;UAGA,MAAMzG,KAAK,GAAGwX,aAAa,CAACxX,KAAK;UACjC,IAAI,CAACuO,uBAAuB,CAACvO,KAAK,CAAC;QAErC,CAAC,MAAM,IAAI,IAAI,CAAC8H,UAAU,EAAE;UAC1B,IAAIsN,gBAAgB,CAAC,IAAI,CAACtN,UAAU,CAAC,EAAE;YACrC,IAAI,CAAC3G,KAAK,CAACyH,GAAG,CAAC,qCAAqC,CAAC;YACrD,IAAI,CAAC3C,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACoH,uBAAuB,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAAC/F,IAAI,CAAC,SAAS,EAAE,IAAI,CAACwB,UAAU,CAAC;YACrC,IAAI,CAAC7B,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;UACrC;QACF,CAAC,MAAM;UACL,IAAI,CAACH,IAAI,CAAC,SAAS,EAAE,IAAIZ,uBAAe,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;UACpE,IAAI,CAACO,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;QACrC;MAEF,CAAC,EAAE,CAAC,CAACgE,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACD+P,6BAA6B,EAAE;IAC7B7Q,IAAI,EAAE,2BAA2B;IACjCkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,IAAI,CAACkD,cAAc,CAAC,CAAC;QACrB,IAAI1H,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QACA,MAAMyQ,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAE,IAAIqQ,+BAAsB,CAAC,IAAI,CAAC,CAAC;QACjG,MAAM,IAAA1R,YAAI,EAACsQ,iBAAiB,EAAE,KAAK,CAAC;QAEpC,IAAI,CAACrQ,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC+O,SAAS,CAAC;QACvC,IAAI,CAACrE,mBAAmB,CAAC,CAAC;MAE5B,CAAC,EAAE,CAAC,CAAClF,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC,CAAC;MACD/F,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB,IAAI,CAACuF,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDuN,SAAS,EAAE;IACTrO,IAAI,EAAE,UAAU;IAChBsG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAI,CAACrC,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACDqO,mBAAmB,EAAE;IACnBnP,IAAI,EAAE,mBAAmB;IACzBkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,OAAA8L,aAAA,EAAAC,cAAA,EAAAC,eAAA,KAAY;QACX,IAAIxQ,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QACA;QACA,IAAI,CAAC2B,iBAAiB,CAAC,CAAC;QAExB,MAAM8O,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAE,IAAIyQ,4BAAmB,CAAC,IAAI,EAAE,IAAI,CAACnQ,OAAQ,CAAC,CAAC;;QAE7G;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAAgQ,aAAA,OAAI,CAAChQ,OAAO,cAAAgQ,aAAA,eAAZA,aAAA,CAAcxD,QAAQ,IAAI,IAAI,CAACnJ,WAAW,EAAE;UAC9C,OAAO,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC8S,cAAc,CAAC;QACrD;QAEA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACrB1B,iBAAiB,CAAC3B,MAAM,CAAC,CAAC;QAC5B,CAAC;QACD,MAAMsD,OAAO,GAAGA,CAAA,KAAM;UAAA,IAAAC,cAAA;UACpB5B,iBAAiB,CAAC6B,KAAK,CAAC,CAAC;UAEzB,CAAAD,cAAA,OAAI,CAACvQ,OAAO,cAAAuQ,cAAA,uBAAZA,cAAA,CAAclS,IAAI,CAAC,QAAQ,EAAEgS,QAAQ,CAAC;QACxC,CAAC;QAED,CAAAJ,cAAA,OAAI,CAACjQ,OAAO,cAAAiQ,cAAA,uBAAZA,cAAA,CAAczR,EAAE,CAAC,OAAO,EAAE8R,OAAO,CAAC;QAElC,IAAI,IAAI,CAACtQ,OAAO,YAAYiK,gBAAO,IAAI,IAAI,CAACjK,OAAO,CAAC+M,MAAM,EAAE;UAC1DuD,OAAO,CAAC,CAAC;QACX;QAEA,MAAMvG,QAAQ,GAAGA,CAAA,KAAM;UAAA,IAAA0G,cAAA,EAAAC,cAAA;UACrB/B,iBAAiB,CAACxQ,cAAc,CAAC,KAAK,EAAEwS,cAAc,CAAC;UAEvD,IAAI,IAAI,CAAC3Q,OAAO,YAAYiK,gBAAO,IAAI,IAAI,CAACjK,OAAO,CAAC+M,MAAM,EAAE;YAC1D;YACA,IAAI,CAAC/M,OAAO,CAACgN,MAAM,CAAC,CAAC;UACvB;UAEA,CAAAyD,cAAA,OAAI,CAACzQ,OAAO,cAAAyQ,cAAA,uBAAZA,cAAA,CAActS,cAAc,CAAC,OAAO,EAAEmS,OAAO,CAAC;UAC9C,CAAAI,cAAA,OAAI,CAAC1Q,OAAO,cAAA0Q,cAAA,uBAAZA,cAAA,CAAcvS,cAAc,CAAC,QAAQ,EAAEkS,QAAQ,CAAC;;UAEhD;UACA;UACA;UACA;UACA,IAAI,CAAC/R,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC8S,cAAc,CAAC;QAC9C,CAAC;QAED,MAAMO,cAAc,GAAGA,CAAA,KAAM;UAAA,IAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;UAC3B,CAAAH,cAAA,OAAI,CAAC5Q,OAAO,cAAA4Q,cAAA,uBAAZA,cAAA,CAAczS,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC9G,uBAAuB,CAAC;UACpE,CAAAwZ,cAAA,OAAI,CAAC7Q,OAAO,cAAA6Q,cAAA,uBAAZA,cAAA,CAAc1S,cAAc,CAAC,QAAQ,EAAE4L,QAAQ,CAAC;UAChD,CAAA+G,cAAA,OAAI,CAAC9Q,OAAO,cAAA8Q,cAAA,uBAAZA,cAAA,CAAc3S,cAAc,CAAC,OAAO,EAAEmS,OAAO,CAAC;UAC9C,CAAAS,cAAA,OAAI,CAAC/Q,OAAO,cAAA+Q,cAAA,uBAAZA,cAAA,CAAc5S,cAAc,CAAC,QAAQ,EAAEkS,QAAQ,CAAC;UAEhD,IAAI,CAAC/R,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC+O,SAAS,CAAC;UACvC,MAAM2E,UAAU,GAAG,IAAI,CAAChR,OAAkB;UAC1C,IAAI,CAACA,OAAO,GAAGpI,SAAS;UACxB,IAAI,IAAI,CAACL,MAAM,CAACO,OAAO,CAACyD,UAAU,GAAG,KAAK,IAAIyV,UAAU,CAACtQ,KAAK,IAAI,IAAI,CAAC5D,UAAU,EAAE;YACjF,IAAI,CAACL,aAAa,GAAG,KAAK;UAC5B;UACAuU,UAAU,CAAC9Q,QAAQ,CAAC8Q,UAAU,CAACtQ,KAAK,EAAEsQ,UAAU,CAACtE,QAAQ,EAAEsE,UAAU,CAACvH,IAAI,CAAC;QAC7E,CAAC;QAEDkF,iBAAiB,CAACtQ,IAAI,CAAC,KAAK,EAAEsS,cAAc,CAAC;QAC7C,CAAAT,eAAA,OAAI,CAAClQ,OAAO,cAAAkQ,eAAA,uBAAZA,eAAA,CAAc7R,IAAI,CAAC,QAAQ,EAAE0L,QAAQ,CAAC;MACxC,CAAC,EAAE,CAAC;IAEN,CAAC;IACD9F,IAAI,EAAE,SAAAA,CAASgN,SAAS,EAAE;MACxB,IAAI,CAACpR,iBAAiB,CAAC,CAAC;IAC1B,CAAC;IACDyE,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAASzC,GAAG,EAAE;QACzB,MAAM8S,UAAU,GAAG,IAAI,CAAChR,OAAQ;QAChC,IAAI,CAACA,OAAO,GAAGpI,SAAS;QACxB,IAAI,CAAC0G,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;QAEnCkS,UAAU,CAAC9Q,QAAQ,CAAChC,GAAG,CAAC;MAC1B;IACF;EACF,CAAC;EACDkS,cAAc,EAAE;IACdpS,IAAI,EAAE,eAAe;IACrBkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,CAAC,YAAY;QACX,IAAIxE,OAAO;QACX,IAAI;UACFA,OAAO,GAAG,MAAM,IAAI,CAAClC,SAAS,CAACwQ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAO9P,GAAQ,EAAE;UACjB,OAAO,IAAI,CAACyC,WAAW,CAACzC,GAAG,CAAC;QAC9B;QAEA,MAAMoC,OAAO,GAAG,IAAI4Q,8BAAqB,CAAC,IAAI,EAAE,IAAI,CAAClR,OAAQ,CAAC;QAC9D,MAAM2O,iBAAiB,GAAG,IAAI,CAACtO,uBAAuB,CAACX,OAAO,EAAEY,OAAO,CAAC;QAExE,MAAM,IAAAjC,YAAI,EAACsQ,iBAAiB,EAAE,KAAK,CAAC;QACpC;QACA;QACA,IAAIrO,OAAO,CAAC6Q,iBAAiB,EAAE;UAC7B,IAAI,CAAC/N,gBAAgB,CAAC,CAAC;UAEvB,MAAM4N,UAAU,GAAG,IAAI,CAAChR,OAAQ;UAChC,IAAI,CAACA,OAAO,GAAGpI,SAAS;UACxB,IAAI,CAAC0G,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC+O,SAAS,CAAC;UAEvC,IAAI2E,UAAU,CAACtQ,KAAK,IAAIsQ,UAAU,CAACtQ,KAAK,YAAYT,oBAAY,IAAI+Q,UAAU,CAACtQ,KAAK,CAAC8D,IAAI,KAAK,UAAU,EAAE;YACxGwM,UAAU,CAAC9Q,QAAQ,CAAC8Q,UAAU,CAACtQ,KAAK,CAAC;UACvC,CAAC,MAAM;YACLsQ,UAAU,CAAC9Q,QAAQ,CAAC,IAAID,oBAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;UAC/D;QACF;MAEF,CAAC,EAAE,CAAC,CAAC6C,KAAK,CAAE5E,GAAG,IAAK;QAClBoB,OAAO,CAACC,QAAQ,CAAC,MAAM;UACrB,MAAMrB,GAAG;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDoG,MAAM,EAAE;MACN3D,WAAW,EAAE,SAAAA,CAASzC,GAAG,EAAE;QACzB,MAAM8S,UAAU,GAAG,IAAI,CAAChR,OAAQ;QAChC,IAAI,CAACA,OAAO,GAAGpI,SAAS;QAExB,IAAI,CAAC0G,YAAY,CAAC,IAAI,CAAChB,KAAK,CAACwB,KAAK,CAAC;QAEnCkS,UAAU,CAAC9Q,QAAQ,CAAChC,GAAG,CAAC;MAC1B;IACF;EACF,CAAC;EACDY,KAAK,EAAE;IACLd,IAAI,EAAE,OAAO;IACbkG,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAACvE,iBAAiB,CAAC5I,YAAY,CAACC,MAAM,CAAC;IAC7C,CAAC;IACDsN,MAAM,EAAE;MACNvL,cAAc,EAAE,SAAAA,CAAA,EAAW;QACzB;MAAA,CACD;MACD2G,OAAO,EAAE,SAAAA,CAAA,EAAW;QAClB;MAAA,CACD;MACDiB,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB;MAAA;IAEJ;EACF;AACF,CAAC"}