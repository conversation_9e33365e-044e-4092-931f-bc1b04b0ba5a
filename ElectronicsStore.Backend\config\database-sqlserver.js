const sql = require('mssql');
require('dotenv').config();

// SQL Server configuration
const config = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'your_password',
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'ElectronicsStoreDB',
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true' || false, // Use encryption
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true' || true, // Trust self-signed certificates
    enableArithAbort: true,
    requestTimeout: 30000,
    connectionTimeout: 30000,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

let pool = null;

// Initialize connection pool
async function initializeDatabase() {
  try {
    if (pool) {
      return pool;
    }
    
    console.log('🔄 Connecting to SQL Server...');
    pool = await sql.connect(config);
    console.log('✅ Connected to SQL Server database');
    
    return pool;
  } catch (error) {
    console.error('❌ Error connecting to SQL Server:', error.message);
    throw error;
  }
}

// Get connection pool
async function getPool() {
  if (!pool) {
    await initializeDatabase();
  }
  return pool;
}

// Execute query with parameters
async function executeQuery(query, params = {}) {
  try {
    const pool = await getPool();
    const request = pool.request();
    
    // Add parameters to request
    Object.keys(params).forEach(key => {
      request.input(key, params[key]);
    });
    
    const result = await request.query(query);
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// Execute stored procedure
async function executeStoredProcedure(procedureName, params = {}) {
  try {
    const pool = await getPool();
    const request = pool.request();
    
    // Add parameters to request
    Object.keys(params).forEach(key => {
      request.input(key, params[key]);
    });
    
    const result = await request.execute(procedureName);
    return result;
  } catch (error) {
    console.error('Stored procedure error:', error);
    throw error;
  }
}

// Close connection
async function closeConnection() {
  try {
    if (pool) {
      await pool.close();
      pool = null;
      console.log('📊 Database connection closed');
    }
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  await closeConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeConnection();
  process.exit(0);
});

module.exports = {
  sql,
  initializeDatabase,
  getPool,
  executeQuery,
  executeStoredProcedure,
  closeConnection,
};
