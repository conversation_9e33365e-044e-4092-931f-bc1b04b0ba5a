{"version": 3, "file": "helpers.js", "names": ["Result", "constructor", "value", "offset", "exports", "NotEnoughDataError", "Error", "byteCount", "readUInt8", "buf", "length", "readUInt16LE", "readInt16LE", "readUInt24LE", "readUIntLE", "readUInt32LE", "readUInt32BE", "readUInt40LE", "readInt32LE", "readBigUInt64LE", "readBigInt64LE", "readFloatLE", "readDoubleLE", "readBVarChar", "charCount", "byteLength", "toString", "readBVarByte", "slice", "readUsVarChar", "readUsVarByte", "readUNumeric64LE", "low", "high", "readUNumeric96LE", "dword1", "dword2", "dword3", "readUNumeric128LE", "dword4"], "sources": ["../../src/token/helpers.ts"], "sourcesContent": ["export class Result<T> {\n  declare value: T;\n  declare offset: number;\n\n  constructor(value: T, offset: number) {\n    this.value = value;\n    this.offset = offset;\n  }\n}\n\nexport class NotEnoughDataError extends Error {\n  byteCount: number;\n\n  constructor(byteCount: number) {\n    super();\n\n    this.byteCount = byteCount;\n  }\n}\n\nexport function readUInt8(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 1) {\n    throw new NotEnoughDataError(offset + 1);\n  }\n\n  return new Result(buf.readUInt8(offset), offset + 1);\n}\n\nexport function readUInt16LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 2) {\n    throw new NotEnoughDataError(offset + 2);\n  }\n\n  return new Result(buf.readUInt16LE(offset), offset + 2);\n}\n\nexport function readInt16LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 2) {\n    throw new NotEnoughDataError(offset + 2);\n  }\n\n  return new Result(buf.readInt16LE(offset), offset + 2);\n}\n\nexport function readUInt24LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 3) {\n    throw new NotEnoughDataError(offset + 3);\n  }\n\n  return new Result(buf.readUIntLE(offset, 3), offset + 3);\n}\n\nexport function readUInt32LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 4) {\n    throw new NotEnoughDataError(offset + 4);\n  }\n\n  return new Result(buf.readUInt32LE(offset), offset + 4);\n}\n\nexport function readUInt32BE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 4) {\n    throw new NotEnoughDataError(offset + 4);\n  }\n\n  return new Result(buf.readUInt32BE(offset), offset + 4);\n}\n\nexport function readUInt40LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 5) {\n    throw new NotEnoughDataError(offset + 5);\n  }\n\n  return new Result(buf.readUIntLE(offset, 5), offset + 5);\n}\nexport function readInt32LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 4) {\n    throw new NotEnoughDataError(offset + 4);\n  }\n\n  return new Result(buf.readInt32LE(offset), offset + 4);\n}\n\nexport function readBigUInt64LE(buf: Buffer, offset: number): Result<bigint> {\n  offset = +offset;\n\n  if (buf.length < offset + 8) {\n    throw new NotEnoughDataError(offset + 8);\n  }\n\n  return new Result(buf.readBigUInt64LE(offset), offset + 8);\n}\n\nexport function readBigInt64LE(buf: Buffer, offset: number): Result<bigint> {\n  offset = +offset;\n\n  if (buf.length < offset + 8) {\n    throw new NotEnoughDataError(offset + 8);\n  }\n\n  return new Result(buf.readBigInt64LE(offset), offset + 8);\n}\n\nexport function readFloatLE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 4) {\n    throw new NotEnoughDataError(offset + 4);\n  }\n\n  return new Result(buf.readFloatLE(offset), offset + 4);\n}\n\nexport function readDoubleLE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 8) {\n    throw new NotEnoughDataError(offset + 8);\n  }\n\n  return new Result(buf.readDoubleLE(offset), offset + 8);\n}\n\nexport function readBVarChar(buf: Buffer, offset: number): Result<string> {\n  offset = +offset;\n\n  let charCount;\n  ({ offset, value: charCount } = readUInt8(buf, offset));\n\n  const byteLength = charCount * 2;\n\n  if (buf.length < offset + byteLength) {\n    throw new NotEnoughDataError(offset + byteLength);\n  }\n\n  return new Result(buf.toString('ucs2', offset, offset + byteLength), offset + byteLength);\n}\n\nexport function readBVarByte(buf: Buffer, offset: number): Result<Buffer> {\n  offset = +offset;\n\n  let byteLength;\n  ({ offset, value: byteLength } = readUInt8(buf, offset));\n\n  if (buf.length < offset + byteLength) {\n    throw new NotEnoughDataError(offset + byteLength);\n  }\n\n  return new Result(buf.slice(offset, offset + byteLength), offset + byteLength);\n}\n\nexport function readUsVarChar(buf: Buffer, offset: number): Result<string> {\n  offset = +offset;\n\n  let charCount;\n  ({ offset, value: charCount } = readUInt16LE(buf, offset));\n\n  const byteLength = charCount * 2;\n\n  if (buf.length < offset + byteLength) {\n    throw new NotEnoughDataError(offset + byteLength);\n  }\n\n  return new Result(buf.toString('ucs2', offset, offset + byteLength), offset + byteLength);\n}\n\nexport function readUsVarByte(buf: Buffer, offset: number): Result<Buffer> {\n  offset = +offset;\n\n  let byteLength;\n  ({ offset, value: byteLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < offset + byteLength) {\n    throw new NotEnoughDataError(offset + byteLength);\n  }\n\n  return new Result(buf.slice(offset, offset + byteLength), offset + byteLength);\n}\n\nexport function readUNumeric64LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 8) {\n    throw new NotEnoughDataError(offset + 8);\n  }\n\n  const low = buf.readUInt32LE(offset);\n  const high = buf.readUInt32LE(offset + 4);\n\n  return new Result((0x100000000 * high) + low, offset + 8);\n}\n\nexport function readUNumeric96LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 12) {\n    throw new NotEnoughDataError(offset + 12);\n  }\n\n  const dword1 = buf.readUInt32LE(offset);\n  const dword2 = buf.readUInt32LE(offset + 4);\n  const dword3 = buf.readUInt32LE(offset + 8);\n\n  return new Result(dword1 + (0x100000000 * dword2) + (0x100000000 * 0x100000000 * dword3), offset + 12);\n}\n\nexport function readUNumeric128LE(buf: Buffer, offset: number): Result<number> {\n  offset = +offset;\n\n  if (buf.length < offset + 16) {\n    throw new NotEnoughDataError(offset + 16);\n  }\n\n  const dword1 = buf.readUInt32LE(offset);\n  const dword2 = buf.readUInt32LE(offset + 4);\n  const dword3 = buf.readUInt32LE(offset + 8);\n  const dword4 = buf.readUInt32LE(offset + 12);\n\n  return new Result(dword1 + (0x100000000 * dword2) + (0x100000000 * 0x100000000 * dword3) + (0x100000000 * 0x100000000 * 0x100000000 * dword4), offset + 16);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,MAAM,CAAI;EAIrBC,WAAWA,CAACC,KAAQ,EAAEC,MAAc,EAAE;IACpC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AAACC,OAAA,CAAAJ,MAAA,GAAAA,MAAA;AAEM,MAAMK,kBAAkB,SAASC,KAAK,CAAC;EAC5CC,SAAS;EAETN,WAAWA,CAACM,SAAiB,EAAE;IAC7B,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;AACF;AAACH,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAEM,SAASG,SAASA,CAACC,GAAW,EAAEN,MAAc,EAAkB;EACrEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACD,SAAS,CAACL,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACtD;AAEO,SAASQ,YAAYA,CAACF,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACE,YAAY,CAACR,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACzD;AAEO,SAASS,WAAWA,CAACH,GAAW,EAAEN,MAAc,EAAkB;EACvEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACG,WAAW,CAACT,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACxD;AAEO,SAASU,YAAYA,CAACJ,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACK,UAAU,CAACX,MAAM,EAAE,CAAC,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AAC1D;AAEO,SAASY,YAAYA,CAACN,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACM,YAAY,CAACZ,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACzD;AAEO,SAASa,YAAYA,CAACP,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACO,YAAY,CAACb,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACzD;AAEO,SAASc,YAAYA,CAACR,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACK,UAAU,CAACX,MAAM,EAAE,CAAC,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AAC1D;AACO,SAASe,WAAWA,CAACT,GAAW,EAAEN,MAAc,EAAkB;EACvEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACS,WAAW,CAACf,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACxD;AAEO,SAASgB,eAAeA,CAACV,GAAW,EAAEN,MAAc,EAAkB;EAC3EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACU,eAAe,CAAChB,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AAC5D;AAEO,SAASiB,cAAcA,CAACX,GAAW,EAAEN,MAAc,EAAkB;EAC1EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACW,cAAc,CAACjB,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AAC3D;AAEO,SAASkB,WAAWA,CAACZ,GAAW,EAAEN,MAAc,EAAkB;EACvEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACY,WAAW,CAAClB,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACxD;AAEO,SAASmB,YAAYA,CAACb,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,OAAO,IAAIH,MAAM,CAACS,GAAG,CAACa,YAAY,CAACnB,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;AACzD;AAEO,SAASoB,YAAYA,CAACd,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIqB,SAAS;EACb,CAAC;IAAErB,MAAM;IAAED,KAAK,EAAEsB;EAAU,CAAC,GAAGhB,SAAS,CAACC,GAAG,EAAEN,MAAM,CAAC;EAEtD,MAAMsB,UAAU,GAAGD,SAAS,GAAG,CAAC;EAEhC,IAAIf,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAGsB,UAAU,EAAE;IACpC,MAAM,IAAIpB,kBAAkB,CAACF,MAAM,GAAGsB,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIzB,MAAM,CAACS,GAAG,CAACiB,QAAQ,CAAC,MAAM,EAAEvB,MAAM,EAAEA,MAAM,GAAGsB,UAAU,CAAC,EAAEtB,MAAM,GAAGsB,UAAU,CAAC;AAC3F;AAEO,SAASE,YAAYA,CAAClB,GAAW,EAAEN,MAAc,EAAkB;EACxEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIsB,UAAU;EACd,CAAC;IAAEtB,MAAM;IAAED,KAAK,EAAEuB;EAAW,CAAC,GAAGjB,SAAS,CAACC,GAAG,EAAEN,MAAM,CAAC;EAEvD,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAGsB,UAAU,EAAE;IACpC,MAAM,IAAIpB,kBAAkB,CAACF,MAAM,GAAGsB,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIzB,MAAM,CAACS,GAAG,CAACmB,KAAK,CAACzB,MAAM,EAAEA,MAAM,GAAGsB,UAAU,CAAC,EAAEtB,MAAM,GAAGsB,UAAU,CAAC;AAChF;AAEO,SAASI,aAAaA,CAACpB,GAAW,EAAEN,MAAc,EAAkB;EACzEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIqB,SAAS;EACb,CAAC;IAAErB,MAAM;IAAED,KAAK,EAAEsB;EAAU,CAAC,GAAGb,YAAY,CAACF,GAAG,EAAEN,MAAM,CAAC;EAEzD,MAAMsB,UAAU,GAAGD,SAAS,GAAG,CAAC;EAEhC,IAAIf,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAGsB,UAAU,EAAE;IACpC,MAAM,IAAIpB,kBAAkB,CAACF,MAAM,GAAGsB,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIzB,MAAM,CAACS,GAAG,CAACiB,QAAQ,CAAC,MAAM,EAAEvB,MAAM,EAAEA,MAAM,GAAGsB,UAAU,CAAC,EAAEtB,MAAM,GAAGsB,UAAU,CAAC;AAC3F;AAEO,SAASK,aAAaA,CAACrB,GAAW,EAAEN,MAAc,EAAkB;EACzEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIsB,UAAU;EACd,CAAC;IAAEtB,MAAM;IAAED,KAAK,EAAEuB;EAAW,CAAC,GAAGd,YAAY,CAACF,GAAG,EAAEN,MAAM,CAAC;EAE1D,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAGsB,UAAU,EAAE;IACpC,MAAM,IAAIpB,kBAAkB,CAACF,MAAM,GAAGsB,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIzB,MAAM,CAACS,GAAG,CAACmB,KAAK,CAACzB,MAAM,EAAEA,MAAM,GAAGsB,UAAU,CAAC,EAAEtB,MAAM,GAAGsB,UAAU,CAAC;AAChF;AAEO,SAASM,gBAAgBA,CAACtB,GAAW,EAAEN,MAAc,EAAkB;EAC5EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,MAAM6B,GAAG,GAAGvB,GAAG,CAACM,YAAY,CAACZ,MAAM,CAAC;EACpC,MAAM8B,IAAI,GAAGxB,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,CAAC,CAAC;EAEzC,OAAO,IAAIH,MAAM,CAAE,WAAW,GAAGiC,IAAI,GAAID,GAAG,EAAE7B,MAAM,GAAG,CAAC,CAAC;AAC3D;AAEO,SAAS+B,gBAAgBA,CAACzB,GAAW,EAAEN,MAAc,EAAkB;EAC5EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,EAAE,EAAE;IAC5B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,EAAE,CAAC;EAC3C;EAEA,MAAMgC,MAAM,GAAG1B,GAAG,CAACM,YAAY,CAACZ,MAAM,CAAC;EACvC,MAAMiC,MAAM,GAAG3B,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,CAAC,CAAC;EAC3C,MAAMkC,MAAM,GAAG5B,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,CAAC,CAAC;EAE3C,OAAO,IAAIH,MAAM,CAACmC,MAAM,GAAI,WAAW,GAAGC,MAAO,GAAI,WAAW,GAAG,WAAW,GAAGC,MAAO,EAAElC,MAAM,GAAG,EAAE,CAAC;AACxG;AAEO,SAASmC,iBAAiBA,CAAC7B,GAAW,EAAEN,MAAc,EAAkB;EAC7EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIM,GAAG,CAACC,MAAM,GAAGP,MAAM,GAAG,EAAE,EAAE;IAC5B,MAAM,IAAIE,kBAAkB,CAACF,MAAM,GAAG,EAAE,CAAC;EAC3C;EAEA,MAAMgC,MAAM,GAAG1B,GAAG,CAACM,YAAY,CAACZ,MAAM,CAAC;EACvC,MAAMiC,MAAM,GAAG3B,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,CAAC,CAAC;EAC3C,MAAMkC,MAAM,GAAG5B,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,CAAC,CAAC;EAC3C,MAAMoC,MAAM,GAAG9B,GAAG,CAACM,YAAY,CAACZ,MAAM,GAAG,EAAE,CAAC;EAE5C,OAAO,IAAIH,MAAM,CAACmC,MAAM,GAAI,WAAW,GAAGC,MAAO,GAAI,WAAW,GAAG,WAAW,GAAGC,MAAO,GAAI,WAAW,GAAG,WAAW,GAAG,WAAW,GAAGE,MAAO,EAAEpC,MAAM,GAAG,EAAE,CAAC;AAC7J"}