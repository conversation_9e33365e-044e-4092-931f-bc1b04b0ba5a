"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const MAX = (1 << 16) - 1;
const UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);
const PLP_TERMINATOR = Buffer.from([0x00, 0x00, 0x00, 0x00]);
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const MAX_NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF]);
const NVarChar = {
  id: 0xE7,
  type: 'NVARCHAR',
  name: 'NVarChar',
  maximumLength: 4000,
  declaration: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.

    let length;
    if (parameter.length) {
      length = parameter.length;
    } else if (value != null) {
      length = value.toString().length || 1;
    } else if (value === null && !parameter.output) {
      length = 1;
    } else {
      length = this.maximumLength;
    }
    if (length <= this.maximumLength) {
      return 'nvarchar(' + length + ')';
    } else {
      return 'nvarchar(max)';
    }
  },
  resolveLength: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.
    if (parameter.length != null) {
      return parameter.length;
    } else if (value != null) {
      if (Buffer.isBuffer(value)) {
        return value.length / 2 || 1;
      } else {
        return value.toString().length || 1;
      }
    } else {
      return this.maximumLength;
    }
  },
  generateTypeInfo(parameter) {
    const buffer = Buffer.alloc(8);
    buffer.writeUInt8(this.id, 0);
    if (parameter.length <= this.maximumLength) {
      buffer.writeUInt16LE(parameter.length * 2, 1);
    } else {
      buffer.writeUInt16LE(MAX, 1);
    }
    if (parameter.collation) {
      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);
    }
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      if (parameter.length <= this.maximumLength) {
        return NULL_LENGTH;
      } else {
        return MAX_NULL_LENGTH;
      }
    }
    let value = parameter.value;
    if (parameter.length <= this.maximumLength) {
      let length;
      if (value instanceof Buffer) {
        length = value.length;
      } else {
        value = value.toString();
        length = Buffer.byteLength(value, 'ucs2');
      }
      const buffer = Buffer.alloc(2);
      buffer.writeUInt16LE(length, 0);
      return buffer;
    } else {
      return UNKNOWN_PLP_LEN;
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    let value = parameter.value;
    if (parameter.length <= this.maximumLength) {
      if (value instanceof Buffer) {
        yield value;
      } else {
        value = value.toString();
        yield Buffer.from(value, 'ucs2');
      }
    } else {
      if (value instanceof Buffer) {
        const length = value.length;
        if (length > 0) {
          const buffer = Buffer.alloc(4);
          buffer.writeUInt32LE(length, 0);
          yield buffer;
          yield value;
        }
      } else {
        value = value.toString();
        const length = Buffer.byteLength(value, 'ucs2');
        if (length > 0) {
          const buffer = Buffer.alloc(4);
          buffer.writeUInt32LE(length, 0);
          yield buffer;
          yield Buffer.from(value, 'ucs2');
        }
      }
      yield PLP_TERMINATOR;
    }
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'string') {
      throw new TypeError('Invalid string.');
    }
    return value;
  }
};
var _default = NVarChar;
exports.default = _default;
module.exports = NVarChar;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJNQVgiLCJVTktOT1dOX1BMUF9MRU4iLCJCdWZmZXIiLCJmcm9tIiwiUExQX1RFUk1JTkFUT1IiLCJOVUxMX0xFTkdUSCIsIk1BWF9OVUxMX0xFTkdUSCIsIk5WYXJDaGFyIiwiaWQiLCJ0eXBlIiwibmFtZSIsIm1heGltdW1MZW5ndGgiLCJkZWNsYXJhdGlvbiIsInBhcmFtZXRlciIsInZhbHVlIiwibGVuZ3RoIiwidG9TdHJpbmciLCJvdXRwdXQiLCJyZXNvbHZlTGVuZ3RoIiwiaXNCdWZmZXIiLCJnZW5lcmF0ZVR5cGVJbmZvIiwiYnVmZmVyIiwiYWxsb2MiLCJ3cml0ZVVJbnQ4Iiwid3JpdGVVSW50MTZMRSIsImNvbGxhdGlvbiIsInRvQnVmZmVyIiwiY29weSIsImdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoIiwib3B0aW9ucyIsImJ5dGVMZW5ndGgiLCJnZW5lcmF0ZVBhcmFtZXRlckRhdGEiLCJ3cml0ZVVJbnQzMkxFIiwidmFsaWRhdGUiLCJUeXBlRXJyb3IiLCJfZGVmYXVsdCIsImV4cG9ydHMiLCJkZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiLi4vLi4vc3JjL2RhdGEtdHlwZXMvbnZhcmNoYXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBEYXRhVHlwZSB9IGZyb20gJy4uL2RhdGEtdHlwZSc7XG5cbmNvbnN0IE1BWCA9ICgxIDw8IDE2KSAtIDE7XG5jb25zdCBVTktOT1dOX1BMUF9MRU4gPSBCdWZmZXIuZnJvbShbMHhmZSwgMHhmZiwgMHhmZiwgMHhmZiwgMHhmZiwgMHhmZiwgMHhmZiwgMHhmZl0pO1xuY29uc3QgUExQX1RFUk1JTkFUT1IgPSBCdWZmZXIuZnJvbShbMHgwMCwgMHgwMCwgMHgwMCwgMHgwMF0pO1xuXG5jb25zdCBOVUxMX0xFTkdUSCA9IEJ1ZmZlci5mcm9tKFsweEZGLCAweEZGXSk7XG5jb25zdCBNQVhfTlVMTF9MRU5HVEggPSBCdWZmZXIuZnJvbShbMHhGRiwgMHhGRiwgMHhGRiwgMHhGRiwgMHhGRiwgMHhGRiwgMHhGRiwgMHhGRl0pO1xuXG5jb25zdCBOVmFyQ2hhcjogeyBtYXhpbXVtTGVuZ3RoOiBudW1iZXIgfSAmIERhdGFUeXBlID0ge1xuICBpZDogMHhFNyxcbiAgdHlwZTogJ05WQVJDSEFSJyxcbiAgbmFtZTogJ05WYXJDaGFyJyxcbiAgbWF4aW11bUxlbmd0aDogNDAwMCxcblxuICBkZWNsYXJhdGlvbjogZnVuY3Rpb24ocGFyYW1ldGVyKSB7XG4gICAgY29uc3QgdmFsdWUgPSBwYXJhbWV0ZXIudmFsdWUgYXMgYW55OyAvLyBUZW1wb3Jhcnkgc29sdXRpb24uIFJlbW92ZSAnYW55JyBsYXRlci5cblxuICAgIGxldCBsZW5ndGg7XG4gICAgaWYgKHBhcmFtZXRlci5sZW5ndGgpIHtcbiAgICAgIGxlbmd0aCA9IHBhcmFtZXRlci5sZW5ndGg7XG4gICAgfSBlbHNlIGlmICh2YWx1ZSAhPSBudWxsKSB7XG4gICAgICBsZW5ndGggPSB2YWx1ZS50b1N0cmluZygpLmxlbmd0aCB8fCAxO1xuICAgIH0gZWxzZSBpZiAodmFsdWUgPT09IG51bGwgJiYgIXBhcmFtZXRlci5vdXRwdXQpIHtcbiAgICAgIGxlbmd0aCA9IDE7XG4gICAgfSBlbHNlIHtcbiAgICAgIGxlbmd0aCA9IHRoaXMubWF4aW11bUxlbmd0aDtcbiAgICB9XG5cbiAgICBpZiAobGVuZ3RoIDw9IHRoaXMubWF4aW11bUxlbmd0aCkge1xuICAgICAgcmV0dXJuICdudmFyY2hhcignICsgbGVuZ3RoICsgJyknO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gJ252YXJjaGFyKG1heCknO1xuICAgIH1cbiAgfSxcblxuICByZXNvbHZlTGVuZ3RoOiBmdW5jdGlvbihwYXJhbWV0ZXIpIHtcbiAgICBjb25zdCB2YWx1ZSA9IHBhcmFtZXRlci52YWx1ZSBhcyBhbnk7IC8vIFRlbXBvcmFyeSBzb2x1dGlvbi4gUmVtb3ZlICdhbnknIGxhdGVyLlxuICAgIGlmIChwYXJhbWV0ZXIubGVuZ3RoICE9IG51bGwpIHtcbiAgICAgIHJldHVybiBwYXJhbWV0ZXIubGVuZ3RoO1xuICAgIH0gZWxzZSBpZiAodmFsdWUgIT0gbnVsbCkge1xuICAgICAgaWYgKEJ1ZmZlci5pc0J1ZmZlcih2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuICh2YWx1ZS5sZW5ndGggLyAyKSB8fCAxO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCkubGVuZ3RoIHx8IDE7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiB0aGlzLm1heGltdW1MZW5ndGg7XG4gICAgfVxuICB9LFxuXG4gIGdlbmVyYXRlVHlwZUluZm8ocGFyYW1ldGVyKSB7XG4gICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDgpO1xuICAgIGJ1ZmZlci53cml0ZVVJbnQ4KHRoaXMuaWQsIDApO1xuXG4gICAgaWYgKHBhcmFtZXRlci5sZW5ndGghIDw9IHRoaXMubWF4aW11bUxlbmd0aCkge1xuICAgICAgYnVmZmVyLndyaXRlVUludDE2TEUocGFyYW1ldGVyLmxlbmd0aCEgKiAyLCAxKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYnVmZmVyLndyaXRlVUludDE2TEUoTUFYLCAxKTtcbiAgICB9XG5cbiAgICBpZiAocGFyYW1ldGVyLmNvbGxhdGlvbikge1xuICAgICAgcGFyYW1ldGVyLmNvbGxhdGlvbi50b0J1ZmZlcigpLmNvcHkoYnVmZmVyLCAzLCAwLCA1KTtcbiAgICB9XG5cbiAgICByZXR1cm4gYnVmZmVyO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoKHBhcmFtZXRlciwgb3B0aW9ucykge1xuICAgIGlmIChwYXJhbWV0ZXIudmFsdWUgPT0gbnVsbCkge1xuICAgICAgaWYgKHBhcmFtZXRlci5sZW5ndGghIDw9IHRoaXMubWF4aW11bUxlbmd0aCkge1xuICAgICAgICByZXR1cm4gTlVMTF9MRU5HVEg7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gTUFYX05VTExfTEVOR1RIO1xuICAgICAgfVxuICAgIH1cblxuICAgIGxldCB2YWx1ZSA9IHBhcmFtZXRlci52YWx1ZTtcbiAgICBpZiAocGFyYW1ldGVyLmxlbmd0aCEgPD0gdGhpcy5tYXhpbXVtTGVuZ3RoKSB7XG4gICAgICBsZXQgbGVuZ3RoO1xuICAgICAgaWYgKHZhbHVlIGluc3RhbmNlb2YgQnVmZmVyKSB7XG4gICAgICAgIGxlbmd0aCA9IHZhbHVlLmxlbmd0aDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHZhbHVlID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgbGVuZ3RoID0gQnVmZmVyLmJ5dGVMZW5ndGgodmFsdWUsICd1Y3MyJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGJ1ZmZlciA9IEJ1ZmZlci5hbGxvYygyKTtcbiAgICAgIGJ1ZmZlci53cml0ZVVJbnQxNkxFKGxlbmd0aCwgMCk7XG4gICAgICByZXR1cm4gYnVmZmVyO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gVU5LTk9XTl9QTFBfTEVOO1xuICAgIH1cbiAgfSxcblxuICAqIGdlbmVyYXRlUGFyYW1ldGVyRGF0YShwYXJhbWV0ZXIsIG9wdGlvbnMpIHtcbiAgICBpZiAocGFyYW1ldGVyLnZhbHVlID09IG51bGwpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBsZXQgdmFsdWUgPSBwYXJhbWV0ZXIudmFsdWU7XG5cbiAgICBpZiAocGFyYW1ldGVyLmxlbmd0aCEgPD0gdGhpcy5tYXhpbXVtTGVuZ3RoKSB7XG4gICAgICBpZiAodmFsdWUgaW5zdGFuY2VvZiBCdWZmZXIpIHtcbiAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB2YWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgIHlpZWxkIEJ1ZmZlci5mcm9tKHZhbHVlLCAndWNzMicpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBpZiAodmFsdWUgaW5zdGFuY2VvZiBCdWZmZXIpIHtcbiAgICAgICAgY29uc3QgbGVuZ3RoID0gdmFsdWUubGVuZ3RoO1xuXG4gICAgICAgIGlmIChsZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDQpO1xuICAgICAgICAgIGJ1ZmZlci53cml0ZVVJbnQzMkxFKGxlbmd0aCwgMCk7XG4gICAgICAgICAgeWllbGQgYnVmZmVyO1xuICAgICAgICAgIHlpZWxkIHZhbHVlO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB2YWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgIGNvbnN0IGxlbmd0aCA9IEJ1ZmZlci5ieXRlTGVuZ3RoKHZhbHVlLCAndWNzMicpO1xuXG4gICAgICAgIGlmIChsZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDQpO1xuICAgICAgICAgIGJ1ZmZlci53cml0ZVVJbnQzMkxFKGxlbmd0aCwgMCk7XG4gICAgICAgICAgeWllbGQgYnVmZmVyO1xuICAgICAgICAgIHlpZWxkIEJ1ZmZlci5mcm9tKHZhbHVlLCAndWNzMicpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHlpZWxkIFBMUF9URVJNSU5BVE9SO1xuICAgIH1cbiAgfSxcblxuICB2YWxpZGF0ZTogZnVuY3Rpb24odmFsdWUpOiBudWxsIHwgc3RyaW5nIHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3N0cmluZycpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ludmFsaWQgc3RyaW5nLicpO1xuICAgIH1cblxuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgTlZhckNoYXI7XG5tb2R1bGUuZXhwb3J0cyA9IE5WYXJDaGFyO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxNQUFNQSxHQUFHLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUM7QUFDekIsTUFBTUMsZUFBZSxHQUFHQyxNQUFNLENBQUNDLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztBQUNyRixNQUFNQyxjQUFjLEdBQUdGLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUM7QUFFNUQsTUFBTUUsV0FBVyxHQUFHSCxNQUFNLENBQUNDLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztBQUM3QyxNQUFNRyxlQUFlLEdBQUdKLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBRXJGLE1BQU1JLFFBQThDLEdBQUc7RUFDckRDLEVBQUUsRUFBRSxJQUFJO0VBQ1JDLElBQUksRUFBRSxVQUFVO0VBQ2hCQyxJQUFJLEVBQUUsVUFBVTtFQUNoQkMsYUFBYSxFQUFFLElBQUk7RUFFbkJDLFdBQVcsRUFBRSxTQUFBQSxDQUFTQyxTQUFTLEVBQUU7SUFDL0IsTUFBTUMsS0FBSyxHQUFHRCxTQUFTLENBQUNDLEtBQVksQ0FBQyxDQUFDOztJQUV0QyxJQUFJQyxNQUFNO0lBQ1YsSUFBSUYsU0FBUyxDQUFDRSxNQUFNLEVBQUU7TUFDcEJBLE1BQU0sR0FBR0YsU0FBUyxDQUFDRSxNQUFNO0lBQzNCLENBQUMsTUFBTSxJQUFJRCxLQUFLLElBQUksSUFBSSxFQUFFO01BQ3hCQyxNQUFNLEdBQUdELEtBQUssQ0FBQ0UsUUFBUSxDQUFDLENBQUMsQ0FBQ0QsTUFBTSxJQUFJLENBQUM7SUFDdkMsQ0FBQyxNQUFNLElBQUlELEtBQUssS0FBSyxJQUFJLElBQUksQ0FBQ0QsU0FBUyxDQUFDSSxNQUFNLEVBQUU7TUFDOUNGLE1BQU0sR0FBRyxDQUFDO0lBQ1osQ0FBQyxNQUFNO01BQ0xBLE1BQU0sR0FBRyxJQUFJLENBQUNKLGFBQWE7SUFDN0I7SUFFQSxJQUFJSSxNQUFNLElBQUksSUFBSSxDQUFDSixhQUFhLEVBQUU7TUFDaEMsT0FBTyxXQUFXLEdBQUdJLE1BQU0sR0FBRyxHQUFHO0lBQ25DLENBQUMsTUFBTTtNQUNMLE9BQU8sZUFBZTtJQUN4QjtFQUNGLENBQUM7RUFFREcsYUFBYSxFQUFFLFNBQUFBLENBQVNMLFNBQVMsRUFBRTtJQUNqQyxNQUFNQyxLQUFLLEdBQUdELFNBQVMsQ0FBQ0MsS0FBWSxDQUFDLENBQUM7SUFDdEMsSUFBSUQsU0FBUyxDQUFDRSxNQUFNLElBQUksSUFBSSxFQUFFO01BQzVCLE9BQU9GLFNBQVMsQ0FBQ0UsTUFBTTtJQUN6QixDQUFDLE1BQU0sSUFBSUQsS0FBSyxJQUFJLElBQUksRUFBRTtNQUN4QixJQUFJWixNQUFNLENBQUNpQixRQUFRLENBQUNMLEtBQUssQ0FBQyxFQUFFO1FBQzFCLE9BQVFBLEtBQUssQ0FBQ0MsTUFBTSxHQUFHLENBQUMsSUFBSyxDQUFDO01BQ2hDLENBQUMsTUFBTTtRQUNMLE9BQU9ELEtBQUssQ0FBQ0UsUUFBUSxDQUFDLENBQUMsQ0FBQ0QsTUFBTSxJQUFJLENBQUM7TUFDckM7SUFDRixDQUFDLE1BQU07TUFDTCxPQUFPLElBQUksQ0FBQ0osYUFBYTtJQUMzQjtFQUNGLENBQUM7RUFFRFMsZ0JBQWdCQSxDQUFDUCxTQUFTLEVBQUU7SUFDMUIsTUFBTVEsTUFBTSxHQUFHbkIsTUFBTSxDQUFDb0IsS0FBSyxDQUFDLENBQUMsQ0FBQztJQUM5QkQsTUFBTSxDQUFDRSxVQUFVLENBQUMsSUFBSSxDQUFDZixFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRTdCLElBQUlLLFNBQVMsQ0FBQ0UsTUFBTSxJQUFLLElBQUksQ0FBQ0osYUFBYSxFQUFFO01BQzNDVSxNQUFNLENBQUNHLGFBQWEsQ0FBQ1gsU0FBUyxDQUFDRSxNQUFNLEdBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUNoRCxDQUFDLE1BQU07TUFDTE0sTUFBTSxDQUFDRyxhQUFhLENBQUN4QixHQUFHLEVBQUUsQ0FBQyxDQUFDO0lBQzlCO0lBRUEsSUFBSWEsU0FBUyxDQUFDWSxTQUFTLEVBQUU7TUFDdkJaLFNBQVMsQ0FBQ1ksU0FBUyxDQUFDQyxRQUFRLENBQUMsQ0FBQyxDQUFDQyxJQUFJLENBQUNOLE1BQU0sRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUN0RDtJQUVBLE9BQU9BLE1BQU07RUFDZixDQUFDO0VBRURPLHVCQUF1QkEsQ0FBQ2YsU0FBUyxFQUFFZ0IsT0FBTyxFQUFFO0lBQzFDLElBQUloQixTQUFTLENBQUNDLEtBQUssSUFBSSxJQUFJLEVBQUU7TUFDM0IsSUFBSUQsU0FBUyxDQUFDRSxNQUFNLElBQUssSUFBSSxDQUFDSixhQUFhLEVBQUU7UUFDM0MsT0FBT04sV0FBVztNQUNwQixDQUFDLE1BQU07UUFDTCxPQUFPQyxlQUFlO01BQ3hCO0lBQ0Y7SUFFQSxJQUFJUSxLQUFLLEdBQUdELFNBQVMsQ0FBQ0MsS0FBSztJQUMzQixJQUFJRCxTQUFTLENBQUNFLE1BQU0sSUFBSyxJQUFJLENBQUNKLGFBQWEsRUFBRTtNQUMzQyxJQUFJSSxNQUFNO01BQ1YsSUFBSUQsS0FBSyxZQUFZWixNQUFNLEVBQUU7UUFDM0JhLE1BQU0sR0FBR0QsS0FBSyxDQUFDQyxNQUFNO01BQ3ZCLENBQUMsTUFBTTtRQUNMRCxLQUFLLEdBQUdBLEtBQUssQ0FBQ0UsUUFBUSxDQUFDLENBQUM7UUFDeEJELE1BQU0sR0FBR2IsTUFBTSxDQUFDNEIsVUFBVSxDQUFDaEIsS0FBSyxFQUFFLE1BQU0sQ0FBQztNQUMzQztNQUVBLE1BQU1PLE1BQU0sR0FBR25CLE1BQU0sQ0FBQ29CLEtBQUssQ0FBQyxDQUFDLENBQUM7TUFDOUJELE1BQU0sQ0FBQ0csYUFBYSxDQUFDVCxNQUFNLEVBQUUsQ0FBQyxDQUFDO01BQy9CLE9BQU9NLE1BQU07SUFDZixDQUFDLE1BQU07TUFDTCxPQUFPcEIsZUFBZTtJQUN4QjtFQUNGLENBQUM7RUFFRCxDQUFFOEIscUJBQXFCQSxDQUFDbEIsU0FBUyxFQUFFZ0IsT0FBTyxFQUFFO0lBQzFDLElBQUloQixTQUFTLENBQUNDLEtBQUssSUFBSSxJQUFJLEVBQUU7TUFDM0I7SUFDRjtJQUVBLElBQUlBLEtBQUssR0FBR0QsU0FBUyxDQUFDQyxLQUFLO0lBRTNCLElBQUlELFNBQVMsQ0FBQ0UsTUFBTSxJQUFLLElBQUksQ0FBQ0osYUFBYSxFQUFFO01BQzNDLElBQUlHLEtBQUssWUFBWVosTUFBTSxFQUFFO1FBQzNCLE1BQU1ZLEtBQUs7TUFDYixDQUFDLE1BQU07UUFDTEEsS0FBSyxHQUFHQSxLQUFLLENBQUNFLFFBQVEsQ0FBQyxDQUFDO1FBQ3hCLE1BQU1kLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDVyxLQUFLLEVBQUUsTUFBTSxDQUFDO01BQ2xDO0lBQ0YsQ0FBQyxNQUFNO01BQ0wsSUFBSUEsS0FBSyxZQUFZWixNQUFNLEVBQUU7UUFDM0IsTUFBTWEsTUFBTSxHQUFHRCxLQUFLLENBQUNDLE1BQU07UUFFM0IsSUFBSUEsTUFBTSxHQUFHLENBQUMsRUFBRTtVQUNkLE1BQU1NLE1BQU0sR0FBR25CLE1BQU0sQ0FBQ29CLEtBQUssQ0FBQyxDQUFDLENBQUM7VUFDOUJELE1BQU0sQ0FBQ1csYUFBYSxDQUFDakIsTUFBTSxFQUFFLENBQUMsQ0FBQztVQUMvQixNQUFNTSxNQUFNO1VBQ1osTUFBTVAsS0FBSztRQUNiO01BQ0YsQ0FBQyxNQUFNO1FBQ0xBLEtBQUssR0FBR0EsS0FBSyxDQUFDRSxRQUFRLENBQUMsQ0FBQztRQUN4QixNQUFNRCxNQUFNLEdBQUdiLE1BQU0sQ0FBQzRCLFVBQVUsQ0FBQ2hCLEtBQUssRUFBRSxNQUFNLENBQUM7UUFFL0MsSUFBSUMsTUFBTSxHQUFHLENBQUMsRUFBRTtVQUNkLE1BQU1NLE1BQU0sR0FBR25CLE1BQU0sQ0FBQ29CLEtBQUssQ0FBQyxDQUFDLENBQUM7VUFDOUJELE1BQU0sQ0FBQ1csYUFBYSxDQUFDakIsTUFBTSxFQUFFLENBQUMsQ0FBQztVQUMvQixNQUFNTSxNQUFNO1VBQ1osTUFBTW5CLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDVyxLQUFLLEVBQUUsTUFBTSxDQUFDO1FBQ2xDO01BQ0Y7TUFFQSxNQUFNVixjQUFjO0lBQ3RCO0VBQ0YsQ0FBQztFQUVENkIsUUFBUSxFQUFFLFNBQUFBLENBQVNuQixLQUFLLEVBQWlCO0lBQ3ZDLElBQUlBLEtBQUssSUFBSSxJQUFJLEVBQUU7TUFDakIsT0FBTyxJQUFJO0lBQ2I7SUFFQSxJQUFJLE9BQU9BLEtBQUssS0FBSyxRQUFRLEVBQUU7TUFDN0IsTUFBTSxJQUFJb0IsU0FBUyxDQUFDLGlCQUFpQixDQUFDO0lBQ3hDO0lBRUEsT0FBT3BCLEtBQUs7RUFDZDtBQUNGLENBQUM7QUFBQyxJQUFBcUIsUUFBQSxHQUVhNUIsUUFBUTtBQUFBNkIsT0FBQSxDQUFBQyxPQUFBLEdBQUFGLFFBQUE7QUFDdkJHLE1BQU0sQ0FBQ0YsT0FBTyxHQUFHN0IsUUFBUSJ9