import React from 'react';
import {
  Container,
  Box,
  Fade,
  useTheme,
  alpha,
} from '@mui/material';

interface PageContainerProps {
  children: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disableGutters?: boolean;
  sx?: any;
  fadeIn?: boolean;
  fadeTimeout?: number;
}

const PageContainer: React.FC<PageContainerProps> = ({
  children,
  maxWidth = 'xl',
  disableGutters = false,
  sx = {},
  fadeIn = true,
  fadeTimeout = 800,
}) => {
  const theme = useTheme();

  const containerContent = (
    <Container
      maxWidth={maxWidth}
      disableGutters={disableGutters}
      sx={{
        py: 4,
        minHeight: 'calc(100vh - 64px)', // Adjust based on your header height
        background: `linear-gradient(135deg, ${alpha(theme.palette.background.default, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
        ...sx,
      }}
    >
      <Box
        sx={{
          position: 'relative',
          zIndex: 1,
        }}
      >
        {children}
      </Box>
    </Container>
  );

  if (fadeIn) {
    return (
      <Fade in timeout={fadeTimeout}>
        {containerContent}
      </Fade>
    );
  }

  return containerContent;
};

export default PageContainer;
