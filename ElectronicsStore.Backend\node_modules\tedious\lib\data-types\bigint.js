"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _intn = _interopRequireDefault(require("./intn"));
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const DATA_LENGTH = Buffer.from([0x08]);
const NULL_LENGTH = Buffer.from([0x00]);
const BigInt = {
  id: 0x7F,
  type: 'INT8',
  name: 'BigInt',
  declaration: function () {
    return 'bigint';
  },
  generateTypeInfo() {
    return Buffer.from([_intn.default.id, 0x08]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = new _writableTrackingBuffer.default(8);
    buffer.writeInt64LE(Number(parameter.value));
    yield buffer.data;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'number') {
      value = Number(value);
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid number.');
    }
    if (value < Number.MIN_SAFE_INTEGER || value > Number.MAX_SAFE_INTEGER) {
      throw new TypeError(`Value must be between ${Number.MIN_SAFE_INTEGER} and ${Number.MAX_SAFE_INTEGER}, inclusive.  For smaller or bigger numbers, use VarChar type.`);
    }
    return value;
  }
};
var _default = BigInt;
exports.default = _default;
module.exports = BigInt;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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