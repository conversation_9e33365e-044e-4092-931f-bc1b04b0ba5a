{"version": 3, "file": "row-token-parser.js", "names": ["_token", "require", "iconv", "_interopRequireWildcard", "_valueParser", "_helpers", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "<PERSON><PERSON><PERSON><PERSON>", "parser", "columns", "metadata", "colMetadata", "isPLPStream", "chunks", "readPLPStream", "push", "value", "type", "name", "<PERSON><PERSON><PERSON>", "concat", "toString", "_metadata$collation", "decode", "collation", "codepage", "result", "readValue", "buffer", "position", "options", "err", "NotEnoughDataError", "waitForChunk", "offset", "useColumnNames", "columnsMap", "create", "for<PERSON>ach", "column", "colName", "RowToken", "_default", "exports", "module"], "sources": ["../../src/token/row-token-parser.ts"], "sourcesContent": ["// s2.2.7.17\n\nimport Parser from './stream-parser';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\n\nimport { RowToken } from './token';\nimport * as iconv from 'iconv-lite';\n\nimport { isPLPStream, readPLPStream, readValue } from '../value-parser';\nimport { NotEnoughDataError } from './helpers';\n\ninterface Column {\n  value: unknown;\n  metadata: ColumnMetadata;\n}\n\nasync function rowParser(parser: Parser): Promise<RowToken> {\n  const columns: Column[] = [];\n\n  for (const metadata of parser.colMetadata) {\n    while (true) {\n      if (isPLPStream(metadata)) {\n        const chunks = await readPLPStream(parser);\n\n        if (chunks === null) {\n          columns.push({ value: chunks, metadata });\n        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {\n          columns.push({ value: Buffer.concat(chunks).toString('ucs2'), metadata });\n        } else if (metadata.type.name === 'VarChar') {\n          columns.push({ value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'), metadata });\n        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {\n          columns.push({ value: Buffer.concat(chunks), metadata });\n        }\n      } else {\n        let result;\n        try {\n          result = readValue(parser.buffer, parser.position, metadata, parser.options);\n        } catch (err) {\n          if (err instanceof NotEnoughDataError) {\n            await parser.waitForChunk();\n            continue;\n          }\n\n          throw err;\n        }\n\n        parser.position = result.offset;\n        columns.push({ value: result.value, metadata });\n      }\n\n      break;\n    }\n  }\n\n  if (parser.options.useColumnNames) {\n    const columnsMap: { [key: string]: Column } = Object.create(null);\n\n    columns.forEach((column) => {\n      const colName = column.metadata.colName;\n      if (columnsMap[colName] == null) {\n        columnsMap[colName] = column;\n      }\n    });\n\n    return new RowToken(columnsMap);\n  } else {\n    return new RowToken(columns);\n  }\n}\n\nexport default rowParser;\nmodule.exports = rowParser;\n"], "mappings": ";;;;;;AAKA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAA+C,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAT/C;;AAgBA,eAAeW,SAASA,CAACC,MAAc,EAAqB;EAC1D,MAAMC,OAAiB,GAAG,EAAE;EAE5B,KAAK,MAAMC,QAAQ,IAAIF,MAAM,CAACG,WAAW,EAAE;IACzC,OAAO,IAAI,EAAE;MACX,IAAI,IAAAC,wBAAW,EAACF,QAAQ,CAAC,EAAE;QACzB,MAAMG,MAAM,GAAG,MAAM,IAAAC,0BAAa,EAACN,MAAM,CAAC;QAE1C,IAAIK,MAAM,KAAK,IAAI,EAAE;UACnBJ,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEH,MAAM;YAAEH;UAAS,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,UAAU,IAAIR,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC5ET,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEG,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC;YAAEX;UAAS,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;UAAA,IAAAI,mBAAA;UAC3Cb,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEnC,KAAK,CAAC0C,MAAM,CAACJ,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC,EAAE,EAAAS,mBAAA,GAAAZ,QAAQ,CAACc,SAAS,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBG,QAAQ,KAAI,MAAM,CAAC;YAAEf;UAAS,CAAC,CAAC;QAChH,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,WAAW,IAAIR,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC7ET,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEG,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC;YAAEH;UAAS,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIgB,MAAM;QACV,IAAI;UACFA,MAAM,GAAG,IAAAC,sBAAS,EAACnB,MAAM,CAACoB,MAAM,EAAEpB,MAAM,CAACqB,QAAQ,EAAEnB,QAAQ,EAAEF,MAAM,CAACsB,OAAO,CAAC;QAC9E,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;YACrC,MAAMxB,MAAM,CAACyB,YAAY,CAAC,CAAC;YAC3B;UACF;UAEA,MAAMF,GAAG;QACX;QAEAvB,MAAM,CAACqB,QAAQ,GAAGH,MAAM,CAACQ,MAAM;QAC/BzB,OAAO,CAACM,IAAI,CAAC;UAAEC,KAAK,EAAEU,MAAM,CAACV,KAAK;UAAEN;QAAS,CAAC,CAAC;MACjD;MAEA;IACF;EACF;EAEA,IAAIF,MAAM,CAACsB,OAAO,CAACK,cAAc,EAAE;IACjC,MAAMC,UAAqC,GAAGtC,MAAM,CAACuC,MAAM,CAAC,IAAI,CAAC;IAEjE5B,OAAO,CAAC6B,OAAO,CAAEC,MAAM,IAAK;MAC1B,MAAMC,OAAO,GAAGD,MAAM,CAAC7B,QAAQ,CAAC8B,OAAO;MACvC,IAAIJ,UAAU,CAACI,OAAO,CAAC,IAAI,IAAI,EAAE;QAC/BJ,UAAU,CAACI,OAAO,CAAC,GAAGD,MAAM;MAC9B;IACF,CAAC,CAAC;IAEF,OAAO,IAAIE,eAAQ,CAACL,UAAU,CAAC;EACjC,CAAC,MAAM;IACL,OAAO,IAAIK,eAAQ,CAAChC,OAAO,CAAC;EAC9B;AACF;AAAC,IAAAiC,QAAA,GAEcnC,SAAS;AAAAoC,OAAA,CAAAnD,OAAA,GAAAkD,QAAA;AACxBE,MAAM,CAACD,OAAO,GAAGpC,SAAS"}