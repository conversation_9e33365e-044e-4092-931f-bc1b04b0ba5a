{"version": 3, "file": "pollOperation.js", "sourceRoot": "", "sources": ["../../../src/legacy/pollOperation.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * PollOperationState contains an opinionated list of the smallest set of properties needed\n * to define any long running operation poller.\n *\n * While the Poller class works as the local control mechanism to start triggering, wait for,\n * and potentially cancel a long running operation, the PollOperationState documents the status\n * of the remote long running operation.\n *\n * It should be updated at least when the operation starts, when it's finished, and when it's cancelled.\n * Though, implementations can have any other number of properties that can be updated by other reasons.\n */\nexport interface PollOperationState<TResult> {\n  /**\n   * True if the operation has started.\n   */\n  isStarted?: boolean;\n  /**\n   * True if the operation has been completed.\n   */\n  isCompleted?: boolean;\n  /**\n   * True if the operation has been cancelled.\n   */\n  isCancelled?: boolean;\n  /**\n   * Will exist if the operation encountered any error.\n   */\n  error?: Error;\n  /**\n   * Will exist if the operation concluded in a result of an expected type.\n   */\n  result?: TResult;\n}\n\n/**\n * PollOperation is an interface that defines how to update the local reference of the state of the remote\n * long running operation, just as well as how to request the cancellation of the same operation.\n *\n * It also has a method to serialize the operation so that it can be stored and resumed at any time.\n */\nexport interface PollOperation<TState, TResult> {\n  /**\n   * The state of the operation.\n   * It will be used to store the basic properties of PollOperationState<TResult>,\n   * plus any custom property that the implementation may require.\n   */\n  state: TState;\n\n  /**\n   * Defines how to request the remote service for updates on the status of the long running operation.\n   *\n   * It optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   * Also optionally receives a \"fireProgress\" function, which, if called, is responsible for triggering the\n   * poller's onProgress callbacks.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  update(options?: {\n    abortSignal?: AbortSignalLike;\n    fireProgress?: (state: TState) => void;\n  }): Promise<PollOperation<TState, TResult>>;\n\n  /**\n   * Attempts to cancel the underlying operation.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * It returns a promise that should be resolved with an updated version of the poller's operation.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   *\n   * @deprecated `cancel` has been deprecated because it was not implemented.\n   */\n  cancel(options?: { abortSignal?: AbortSignalLike }): Promise<PollOperation<TState, TResult>>;\n\n  /**\n   * Serializes the operation.\n   * Useful when wanting to create a poller that monitors an existing operation.\n   */\n  toString(): string;\n}\n"]}