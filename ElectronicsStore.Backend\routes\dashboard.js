const express = require('express');
const router = express.Router();
const { runQuery, getRow, getAllRows } = require('../config/database');

// GET /api/dashboard/stats - Get dashboard statistics
router.get('/stats', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);
    
    // Today's sales
    const todayStats = await getRow(`
      SELECT 
        COUNT(*) as total_sales,
        COALESCE(SUM(total), 0) as total_revenue,
        COALESCE(AVG(total), 0) as avg_sale_amount
      FROM sales 
      WHERE DATE(sale_date) = ? AND status = 'completed'
    `, [today]);
    
    // This month's sales
    const monthStats = await getRow(`
      SELECT 
        COUNT(*) as total_sales,
        COALESCE(SUM(total), 0) as total_revenue
      FROM sales 
      WHERE strftime('%Y-%m', sale_date) = ? AND status = 'completed'
    `, [thisMonth]);
    
    // Product statistics
    const productStats = await getRow(`
      SELECT 
        COUNT(*) as total_products,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
        COUNT(CASE WHEN stock <= min_stock THEN 1 END) as low_stock_products,
        COALESCE(SUM(stock), 0) as total_stock_value
      FROM products
    `);
    
    // Customer statistics
    const customerStats = await getRow(`
      SELECT 
        COUNT(*) as total_customers,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_customers,
        COUNT(CASE WHEN status = 'vip' THEN 1 END) as vip_customers
      FROM customers
    `);
    
    // Recent sales
    const recentSales = await getAllRows(`
      SELECT 
        id, order_number, customer_name, total, payment_method, sale_date
      FROM sales 
      ORDER BY sale_date DESC 
      LIMIT 5
    `);
    
    // Low stock products
    const lowStockProducts = await getAllRows(`
      SELECT name, stock, min_stock, category
      FROM products 
      WHERE stock <= min_stock AND status = 'active'
      ORDER BY stock ASC
      LIMIT 5
    `);
    
    // Top selling products (this month)
    const topProducts = await getAllRows(`
      SELECT 
        si.product_name,
        SUM(si.quantity) as total_sold,
        SUM(si.total) as total_revenue
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      WHERE strftime('%Y-%m', s.sale_date) = ? AND s.status = 'completed'
      GROUP BY si.product_id, si.product_name
      ORDER BY total_sold DESC
      LIMIT 5
    `, [thisMonth]);
    
    // Sales trend (last 7 days)
    const salesTrend = await getAllRows(`
      SELECT 
        DATE(sale_date) as date,
        COUNT(*) as sales_count,
        COALESCE(SUM(total), 0) as revenue
      FROM sales 
      WHERE DATE(sale_date) >= DATE('now', '-7 days') AND status = 'completed'
      GROUP BY DATE(sale_date)
      ORDER BY date ASC
    `);
    
    res.json({
      success: true,
      data: {
        today: todayStats,
        month: monthStats,
        products: productStats,
        customers: customerStats,
        recentSales,
        lowStockProducts,
        topProducts,
        salesTrend
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات لوحة التحكم'
    });
  }
});

// GET /api/dashboard/revenue-chart - Get revenue chart data
router.get('/revenue-chart', async (req, res) => {
  try {
    const { period = 'week' } = req.query;
    
    let dateFilter, groupBy;
    
    switch (period) {
      case 'week':
        dateFilter = "DATE(sale_date) >= DATE('now', '-7 days')";
        groupBy = "DATE(sale_date)";
        break;
      case 'month':
        dateFilter = "DATE(sale_date) >= DATE('now', '-30 days')";
        groupBy = "DATE(sale_date)";
        break;
      case 'year':
        dateFilter = "DATE(sale_date) >= DATE('now', '-365 days')";
        groupBy = "strftime('%Y-%m', sale_date)";
        break;
      default:
        dateFilter = "DATE(sale_date) >= DATE('now', '-7 days')";
        groupBy = "DATE(sale_date)";
    }
    
    const revenueData = await getAllRows(`
      SELECT 
        ${groupBy} as period,
        COUNT(*) as sales_count,
        COALESCE(SUM(total), 0) as revenue,
        COALESCE(SUM(subtotal), 0) as subtotal,
        COALESCE(SUM(tax), 0) as tax
      FROM sales 
      WHERE ${dateFilter} AND status = 'completed'
      GROUP BY ${groupBy}
      ORDER BY period ASC
    `);
    
    res.json({
      success: true,
      data: revenueData
    });
  } catch (error) {
    console.error('Error fetching revenue chart data:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات مخطط الإيرادات'
    });
  }
});

// GET /api/dashboard/category-sales - Get sales by category
router.get('/category-sales', async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    
    let dateFilter;
    switch (period) {
      case 'week':
        dateFilter = "DATE(s.sale_date) >= DATE('now', '-7 days')";
        break;
      case 'month':
        dateFilter = "strftime('%Y-%m', s.sale_date) = strftime('%Y-%m', 'now')";
        break;
      case 'year':
        dateFilter = "strftime('%Y', s.sale_date) = strftime('%Y', 'now')";
        break;
      default:
        dateFilter = "strftime('%Y-%m', s.sale_date) = strftime('%Y-%m', 'now')";
    }
    
    const categorySales = await getAllRows(`
      SELECT 
        p.category,
        SUM(si.quantity) as total_quantity,
        SUM(si.total) as total_revenue,
        COUNT(DISTINCT s.id) as total_orders
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      JOIN products p ON si.product_id = p.id
      WHERE ${dateFilter} AND s.status = 'completed'
      GROUP BY p.category
      ORDER BY total_revenue DESC
    `);
    
    res.json({
      success: true,
      data: categorySales
    });
  } catch (error) {
    console.error('Error fetching category sales:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب مبيعات الفئات'
    });
  }
});

// GET /api/dashboard/payment-methods - Get payment methods distribution
router.get('/payment-methods', async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    
    let dateFilter;
    switch (period) {
      case 'week':
        dateFilter = "DATE(sale_date) >= DATE('now', '-7 days')";
        break;
      case 'month':
        dateFilter = "strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')";
        break;
      case 'year':
        dateFilter = "strftime('%Y', sale_date) = strftime('%Y', 'now')";
        break;
      default:
        dateFilter = "strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')";
    }
    
    const paymentMethods = await getAllRows(`
      SELECT 
        payment_method,
        COUNT(*) as transaction_count,
        SUM(total) as total_amount,
        AVG(total) as avg_amount
      FROM sales 
      WHERE ${dateFilter} AND status = 'completed'
      GROUP BY payment_method
      ORDER BY total_amount DESC
    `);
    
    res.json({
      success: true,
      data: paymentMethods
    });
  } catch (error) {
    console.error('Error fetching payment methods data:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات طرق الدفع'
    });
  }
});

// GET /api/dashboard/alerts - Get system alerts
router.get('/alerts', async (req, res) => {
  try {
    const alerts = [];
    
    // Low stock alerts
    const lowStockProducts = await getAllRows(`
      SELECT name, stock, min_stock
      FROM products 
      WHERE stock <= min_stock AND status = 'active'
      ORDER BY stock ASC
    `);
    
    lowStockProducts.forEach(product => {
      alerts.push({
        type: 'warning',
        title: 'مخزون منخفض',
        message: `المنتج "${product.name}" متبقي منه ${product.stock} قطعة فقط`,
        priority: product.stock === 0 ? 'high' : 'medium',
        timestamp: new Date().toISOString()
      });
    });
    
    // Out of stock alerts
    const outOfStockCount = await getRow(`
      SELECT COUNT(*) as count
      FROM products 
      WHERE stock = 0 AND status = 'active'
    `);
    
    if (outOfStockCount.count > 0) {
      alerts.push({
        type: 'error',
        title: 'منتجات نفدت من المخزون',
        message: `${outOfStockCount.count} منتج نفد من المخزون`,
        priority: 'high',
        timestamp: new Date().toISOString()
      });
    }
    
    // Today's sales milestone
    const todayRevenue = await getRow(`
      SELECT COALESCE(SUM(total), 0) as revenue
      FROM sales 
      WHERE DATE(sale_date) = DATE('now') AND status = 'completed'
    `);
    
    if (todayRevenue.revenue > 10000) {
      alerts.push({
        type: 'success',
        title: 'إنجاز مبيعات ممتاز',
        message: `تم تحقيق ${todayRevenue.revenue.toFixed(2)} ريال اليوم`,
        priority: 'low',
        timestamp: new Date().toISOString()
      });
    }
    
    res.json({
      success: true,
      data: alerts
    });
  } catch (error) {
    console.error('Error fetching alerts:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب التنبيهات'
    });
  }
});

module.exports = router;
