# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.10](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.9...v1.0.10) - 2025-01-02

### Commits

- [Refactor] use `es-object-atoms` and `get-proto` directly [`2c55da0`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/2c55da02fb56d9ce7a19e505f487dfb088256d24)
- [Refactor] use `isObject` helper instead of `Type` [`e4c24a4`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/e4c24a4cc1b9928da3eb1f0a032703235d32855e)
- [Deps] update `es-abstract`, `get-intrinsic` [`54005fb`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/54005fb9df56fb399e0c526600cb7799d23ef64f)
- [Deps] update `es-abstract` [`e71b3b7`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/e71b3b75847d4af71e42ff049bd2b4d55f0324fc)

## [v1.0.9](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.8...v1.0.9) - 2024-12-18

### Commits

- [Fix] avoid a crash with node `--disable-proto=throw` flag [`73f449d`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/73f449dff08fe574a4cd937d7265aceb1774de05)
- [Deps] update `dunder-proto`, `es-abstract`, `get-intrinsic`, `which-builtin-type` [`80c8227`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/80c8227aac6cef446ac8460f8bbdd9a83bd1d131)

## [v1.0.8](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.7...v1.0.8) - 2024-12-06

### Commits

- [Refactor] share the getDunderProto helper [`f4be71b`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/f4be71b2941f217311210a00d84d8338b4880e2a)
- [Refactor] extract helper to `dunder-proto` [`51dcd35`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/51dcd3547ef9808c9fee7aa4a638d3d5d93db70c)
- [Deps] update `call-bind` [`c06e8d3`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/c06e8d3c1c9b567ca5bd6bb8542ae2e342f276ff)
- [Deps] update `gopd` [`c951a70`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/c951a709d5483fecc4041287a676d881295d1940)
- [Deps] update `gopd` [`8bd7b85`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/8bd7b8524aacc0574c979e3fe4f82adc51b03f3f)
- [Deps] update `which-builtin-type` [`f46f624`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/f46f62423c1c452063f73d34a363c127410742b2)

## [v1.0.7](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.6...v1.0.7) - 2024-11-23

### Commits

- [Fix] cache `Object.prototype[__proto__]` getter [`68acea1`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/68acea171951197f83bb107af9e186ce68a5a78f)
- [Tests] remove unused test file [`055596a`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/055596aa2e050e08d35f8f19aa642e9b61a54f7f)
- [actions] split out node 10-20, and 20+ [`99a57c2`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/99a57c2551d11de70ea90b934e02b04ff3cec6c0)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`fe4bb92`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/fe4bb92a4d629e629f42f76567d837f8f17643a4)
- [Deps] update `es-abstract`, `globalthis`, `which-builtin-type` [`867aeb3`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/867aeb3c434ee03fd819da09d23a32a2c71290dc)
- [Tests] replace `aud` with `npm audit` [`5129c11`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/5129c112e18e3475afe42a836ac5d4d7ddfcfc18)
- [Deps] remove unused dep; add missing dev dep [`23cf537`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/23cf537aeac98bf27a8d39e24048e1c9fa4df7a7)
- [Dev Deps] add missing peer dep [`18862fa`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/18862fa57d2aa86825998db935ac6ffe23dec113)

## [v1.0.6](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.5...v1.0.6) - 2024-03-16

### Commits

- [meta] remove useless ESM [`cc16ab1`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/cc16ab1f3b050c9465b41fbbf79e284e8e9084ff)
- [Deps] update `call-bind`, `es-abstract`, `es-errors`, `get-intrinsic` [`ece7445`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/ece7445f03d148c67f5fc91ea455a05731b54cea)
- [Dev Deps] update `tape` [`a894b62`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/a894b624c16247cbb76a00e76a39e3d44ac5bccf)

## [v1.0.5](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.4...v1.0.5) - 2024-02-04

### Commits

- [Refactor] use `es-errors` where possible, so things that only need those do not need `get-intrinsic` [`08735cb`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/08735cba1dfeabae4e362b61e6de36843a472d22)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `get-intrinsic` [`b5700ec`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/b5700eca8af88bbc693b304567d6124d13c03827)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`f80fcbb`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/f80fcbb140661c07aa2a94547ffae0a5a8f937ac)

## [v1.0.4](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.3...v1.0.4) - 2023-08-30

### Commits

- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`325044f`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/325044f152c87f18344ba558b5967e69298a5dd2)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`56b9555`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/56b9555a536207493c4e2c17f1fee2390535a659)

## [v1.0.3](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.2...v1.0.3) - 2022-11-07

### Commits

- [actions] reuse common workflows [`0a76d97`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/0a76d972c13cd319504d3eecf8e7e00e39327c88)
- [meta] use `npmignore` to autogenerate an npmignore file [`c4861b4`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/c4861b45aeebddd1d998bdd44438b29e4bf974e8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`e5352ab`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/e5352ab099f7104c085ffd2ee8fbfec69546fa2c)
- [actions] update codecov uploader [`dbcedf8`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/dbcedf83e967e17316fc98fb18011c603bb54823)
- [Deps] update `define-properties`, `es-abstract`, `globalthis`, `which-builtin-type` [`84fbfab`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/84fbfabe53774587be4748df303cec8acded7c0f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`75dc8a3`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/75dc8a337f11f11a947241c968bb7f5b843a1d17)
- [actions] update rebase action to use reusable workflow [`49463a7`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/49463a7a6d9aaa08c201ff4206efcade7a997175)
- [Dev Deps] update `@es-shims/api` [`adf75cb`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/adf75cb9dbda9052437a8e48e33da28dc52aa63c)

## [v1.0.2](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.1...v1.0.2) - 2021-10-03

### Commits

- [Deps] update `es-abstract` [`947bbb1`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/947bbb1bff0cc08ab80bb809a19771c6087e9fda)
- [actions] update workflows` [`af4708d`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/af4708d75212ee5781b549b1ff86f903fc659e3f)

## [v1.0.1](https://github.com/es-shims/Reflect.getPrototypeOf/compare/v1.0.0...v1.0.1) - 2021-09-08

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`86f4aa3`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/86f4aa392c73220ecb561d644def5c3efab4f9f5)
- [Refactor] remove unnecessary ESM files [`162e4bf`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/162e4bfb58fb4eba6f2f26f11b0f46a8ea6587a7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `tape` [`5515a8d`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/5515a8d17fc1978792db7e57a79f7f2fa60dc55e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`db05b43`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/db05b43dff616acab04544ab04bd385d462b8572)
- [readme] add github actions/codecov badges [`d9ea295`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/d9ea295264f1d415b5083b3d4116b14a5a8ee7ff)
- [Deps] update `es-abstract`, `which-builtin-type` [`1753dee`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/1753deeaadbd66bb65e140e4bc81cee6652f07cd)
- [Dev Deps] update `eslint`, `tape` [`4bce2db`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/4bce2db34c4182edd0bf3610eaeb1ea42844ee15)
- [Deps] update `es-abstract`, `get-intrinsic` [`1dca685`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/1dca685d1e5305b6720882ac8f4d5038a8309431)
- [meta] use `prepublishOnly` script for npm 7+ [`1108273`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/1108273eba643ac8b135d177c21dd371ccb32901)
- [actions] update workflows [`8674d66`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/8674d662705675531d216bdba2b7e1e26be1b12b)
- [Deps] update `es-abstract` [`4e4fdcf`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/4e4fdcf854f3cd71d71aa80723664f5d2b814dad)

## v1.0.0 - 2021-01-12

### Commits

- es-shims initial commit [`ffd0b98`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/ffd0b980b47f0b2db6bc3373364e950f6fce33cb)
- Initial commit [`64c868f`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/64c868fa1bb3630f4ab823aaba98d0f28b3f8078)
- [meta] add Automatic Rebase and Require Allow Edits workflows [`6a006b4`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/6a006b4ddebfe68fd23b2870c83aadefecf80b04)
- [meta] Only apps should have lockfiles [`d1a29b5`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/d1a29b5b37d31129ecc1fecac1922d4c4935f9fa)
- [Deps] update `call-bind` [`bfcc1a0`](https://github.com/es-shims/Reflect.getPrototypeOf/commit/bfcc1a0bd04fffe5fe53e791dd40f7988bbd5d03)
