import { apiService } from './apiClient';

// Auth types
export interface User {
  id: number;
  username: string;
  roleId: number;
  roleName: string;
  createdAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
  expiresAt: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  roleId: number;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Auth API endpoints
const ENDPOINTS = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  REFRESH: '/auth/refresh',
  LOGOUT: '/auth/logout',
  PROFILE: '/auth/profile',
  CHANGE_PASSWORD: '/auth/change-password',
};

// Auth Service
export const authService = {
  // Login
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiService.post<LoginResponse>(ENDPOINTS.LOGIN, credentials);
    
    // Store tokens in localStorage
    localStorage.setItem('authToken', response.token);
    localStorage.setItem('refreshToken', response.refreshToken);
    localStorage.setItem('user', JSON.stringify(response.user));
    
    return response;
  },

  // Register
  register: async (userData: RegisterRequest): Promise<User> => {
    return await apiService.post<User>(ENDPOINTS.REGISTER, userData);
  },

  // Refresh token
  refreshToken: async (): Promise<LoginResponse> => {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiService.post<LoginResponse>(ENDPOINTS.REFRESH, {
      refreshToken,
    });

    // Update stored tokens
    localStorage.setItem('authToken', response.token);
    localStorage.setItem('refreshToken', response.refreshToken);
    localStorage.setItem('user', JSON.stringify(response.user));

    return response;
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      await apiService.post<void>(ENDPOINTS.LOGOUT);
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      // Clear stored data
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    return await apiService.get<User>(ENDPOINTS.PROFILE);
  },

  // Change password
  changePassword: async (passwordData: ChangePasswordRequest): Promise<void> => {
    return await apiService.post<void>(ENDPOINTS.CHANGE_PASSWORD, passwordData);
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('authToken');
    return !!token;
  },

  // Get current user from localStorage
  getCurrentUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Error parsing user data:', error);
        return null;
      }
    }
    return null;
  },

  // Get auth token
  getToken: (): string | null => {
    return localStorage.getItem('authToken');
  },

  // Check if user has specific role
  hasRole: (roleName: string): boolean => {
    const user = authService.getCurrentUser();
    return user?.roleName === roleName;
  },

  // Check if user has any of the specified roles
  hasAnyRole: (roleNames: string[]): boolean => {
    const user = authService.getCurrentUser();
    return user ? roleNames.includes(user.roleName) : false;
  },
};

export default authService;
