// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { operationOptionsToRequestParameters as tspOperationOptionsToRequestParameters, } from "@typespec/ts-http-runtime";
/**
 * Helper function to convert OperationOptions to RequestParameters
 * @param options - the options that are used by Modular layer to send the request
 * @returns the result of the conversion in RequestParameters of RLC layer
 */
export function operationOptionsToRequestParameters(options) {
    return tspOperationOptionsToRequestParameters(options);
}
//# sourceMappingURL=operationOptionHelpers.js.map