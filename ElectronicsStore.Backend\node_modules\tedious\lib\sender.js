"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sendInParallel = sendInParallel;
exports.sendMessage = sendMessage;
var _dgram = _interopRequireDefault(require("dgram"));
var _net = _interopRequireDefault(require("net"));
var _nodeUrl = _interopRequireDefault(require("node:url"));
var _abortError = _interopRequireDefault(require("./errors/abort-error"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
async function sendInParallel(addresses, port, request, signal) {
  if (signal.aborted) {
    throw new _abortError.default();
  }
  return await new Promise((resolve, reject) => {
    const sockets = [];
    let errorCount = 0;
    const onError = err => {
      errorCount++;
      if (errorCount === addresses.length) {
        signal.removeEventListener('abort', onAbort);
        clearSockets();
        reject(err);
      }
    };
    const onMessage = message => {
      signal.removeEventListener('abort', onAbort);
      clearSockets();
      resolve(message);
    };
    const onAbort = () => {
      clearSockets();
      reject(new _abortError.default());
    };
    const clearSockets = () => {
      for (const socket of sockets) {
        socket.removeListener('error', onError);
        socket.removeListener('message', onMessage);
        socket.close();
      }
    };
    signal.addEventListener('abort', onAbort, {
      once: true
    });
    for (let j = 0; j < addresses.length; j++) {
      const udpType = addresses[j].family === 6 ? 'udp6' : 'udp4';
      const socket = _dgram.default.createSocket(udpType);
      sockets.push(socket);
      socket.on('error', onError);
      socket.on('message', onMessage);
      socket.send(request, 0, request.length, port, addresses[j].address);
    }
  });
}
async function sendMessage(host, port, lookup, signal, request) {
  if (signal.aborted) {
    throw new _abortError.default();
  }
  let addresses;
  if (_net.default.isIP(host)) {
    addresses = [{
      address: host,
      family: _net.default.isIPv6(host) ? 6 : 4
    }];
  } else {
    addresses = await new Promise((resolve, reject) => {
      const onAbort = () => {
        reject(new _abortError.default());
      };
      signal.addEventListener('abort', onAbort);
      lookup(_nodeUrl.default.domainToASCII(host), {
        all: true
      }, (err, addresses) => {
        signal.removeEventListener('abort', onAbort);
        err ? reject(err) : resolve(addresses);
      });
    });
  }
  return await sendInParallel(addresses, port, request, signal);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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