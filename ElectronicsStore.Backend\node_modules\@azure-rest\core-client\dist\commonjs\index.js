"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.operationOptionsToRequestParameters = exports.addCredentialPipelinePolicy = exports.createRestError = void 0;
const tslib_1 = require("tslib");
/**
 * Azure Rest Core Client library for JavaScript
 * @packageDocumentation
 */
var restError_js_1 = require("./restError.js");
Object.defineProperty(exports, "createRestError", { enumerable: true, get: function () { return restError_js_1.createRestError; } });
var clientHelpers_js_1 = require("./clientHelpers.js");
Object.defineProperty(exports, "addCredentialPipelinePolicy", { enumerable: true, get: function () { return clientHelpers_js_1.addCredentialPipelinePolicy; } });
var operationOptionHelpers_js_1 = require("./operationOptionHelpers.js");
Object.defineProperty(exports, "operationOptionsToRequestParameters", { enumerable: true, get: function () { return operationOptionHelpers_js_1.operationOptionsToRequestParameters; } });
tslib_1.__exportStar(require("./getClient.js"), exports);
tslib_1.__exportStar(require("./common.js"), exports);
//# sourceMappingURL=index.js.map