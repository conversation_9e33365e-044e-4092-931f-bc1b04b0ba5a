"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getViewsGranularity = exports.getSectionTypeGranularity = exports.getDefaultReferenceDate = exports.SECTION_TYPE_GRANULARITY = void 0;
var _timeUtils = require("./time-utils");
var _dateUtils = require("./date-utils");
const SECTION_TYPE_GRANULARITY = exports.SECTION_TYPE_GRANULARITY = {
  year: 1,
  month: 2,
  day: 3,
  hours: 4,
  minutes: 5,
  seconds: 6,
  milliseconds: 7
};
const getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));
exports.getSectionTypeGranularity = getSectionTypeGranularity;
const getViewsGranularity = views => Math.max(...views.map(view => SECTION_TYPE_GRANULARITY[view] ?? 1));
exports.getViewsGranularity = getViewsGranularity;
const roundDate = (utils, granularity, date) => {
  if (granularity === SECTION_TYPE_GRANULARITY.year) {
    return utils.startOfYear(date);
  }
  if (granularity === SECTION_TYPE_GRANULARITY.month) {
    return utils.startOfMonth(date);
  }
  if (granularity === SECTION_TYPE_GRANULARITY.day) {
    return utils.startOfDay(date);
  }

  // We don't have startOfHour / startOfMinute / startOfSecond
  let roundedDate = date;
  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {
    roundedDate = utils.setMinutes(roundedDate, 0);
  }
  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {
    roundedDate = utils.setSeconds(roundedDate, 0);
  }
  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {
    roundedDate = utils.setMilliseconds(roundedDate, 0);
  }
  return roundedDate;
};
const getDefaultReferenceDate = ({
  props,
  utils,
  granularity,
  timezone,
  getTodayDate: inGetTodayDate
}) => {
  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, (0, _dateUtils.getTodayDate)(utils, timezone));
  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {
    referenceDate = roundDate(utils, granularity, props.minDate);
  }
  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {
    referenceDate = roundDate(utils, granularity, props.maxDate);
  }
  const isAfter = (0, _timeUtils.createIsAfterIgnoreDatePart)(props.disableIgnoringDatePartForTimeValidation ?? false, utils);
  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {
    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.minTime));
  }
  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {
    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.maxTime));
  }
  return referenceDate;
};
exports.getDefaultReferenceDate = getDefaultReferenceDate;