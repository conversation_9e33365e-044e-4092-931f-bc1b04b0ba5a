{"version": 3, "file": "date.js", "names": ["_core", "require", "globalDate", "global", "Date", "EPOCH_DATE", "LocalDate", "ofYearDay", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "id", "type", "name", "declaration", "generateTypeInfo", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "date", "useUTC", "of", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getFullYear", "getMonth", "getDate", "days", "until", "ChronoUnit", "DAYS", "buffer", "alloc", "writeUIntLE", "validate", "collation", "parse", "year", "TypeError", "isNaN", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/date.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport { ChronoUnit, LocalDate } from '@js-joda/core';\n\n// globalDate is to be used for JavaScript's global 'Date' object to avoid name clashing with the 'Date' constant below\nconst globalDate = global.Date;\nconst EPOCH_DATE = LocalDate.ofYearDay(1, 1);\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x03]);\n\nconst Date: DataType = {\n  id: 0x28,\n  type: 'DATEN',\n  name: 'Date',\n\n  declaration: function() {\n    return 'date';\n  },\n\n  generateTypeInfo: function() {\n    return Buffer.from([this.id]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    let date: LocalDate;\n    if (options.useUTC) {\n      date = LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());\n    } else {\n      date = LocalDate.of(value.getFullYear(), value.getMonth() + 1, value.getDate());\n    }\n\n    const days = EPOCH_DATE.until(date, ChronoUnit.DAYS);\n    const buffer = Buffer.alloc(3);\n    buffer.writeUIntLE(days, 0, 3);\n    yield buffer;\n  },\n\n  // TODO: value is technically of type 'unknown'.\n  validate: function(value, collation, options): null | Date {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof globalDate)) {\n      value = new globalDate(globalDate.parse(value));\n    }\n\n    value = value as Date;\n\n    let year;\n    if (options && options.useUTC) {\n      year = value.getUTCFullYear();\n    } else {\n      year = value.getFullYear();\n    }\n\n    if (year < 1 || year > 9999) {\n      throw new TypeError('Out of range.');\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid date.');\n    }\n\n    return value;\n  }\n};\n\nexport default Date;\nmodule.exports = Date;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AAEA;AACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI;AAC9B,MAAMC,UAAU,GAAGC,eAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMN,IAAc,GAAG;EACrBQ,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EAEZC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,MAAM;EACf,CAAC;EAEDC,gBAAgB,EAAE,SAAAA,CAAA,EAAW;IAC3B,OAAOP,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACE,EAAE,CAAC,CAAC;EAC/B,CAAC;EAEDK,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOZ,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,CAAEU,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGF,SAAS,CAACE,KAAY,CAAC,CAAC;;IAEtC,IAAIE,IAAe;IACnB,IAAIH,OAAO,CAACI,MAAM,EAAE;MAClBD,IAAI,GAAGhB,eAAS,CAACkB,EAAE,CAACJ,KAAK,CAACK,cAAc,CAAC,CAAC,EAAEL,KAAK,CAACM,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEN,KAAK,CAACO,UAAU,CAAC,CAAC,CAAC;IAC1F,CAAC,MAAM;MACLL,IAAI,GAAGhB,eAAS,CAACkB,EAAE,CAACJ,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC;IACjF;IAEA,MAAMC,IAAI,GAAG1B,UAAU,CAAC2B,KAAK,CAACV,IAAI,EAAEW,gBAAU,CAACC,IAAI,CAAC;IACpD,MAAMC,MAAM,GAAG1B,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,WAAW,CAACN,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B,MAAMI,MAAM;EACd,CAAC;EAED;EACAG,QAAQ,EAAE,SAAAA,CAASlB,KAAK,EAAEmB,SAAS,EAAEpB,OAAO,EAAe;IACzD,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAYlB,UAAU,CAAC,EAAE;MAClCkB,KAAK,GAAG,IAAIlB,UAAU,CAACA,UAAU,CAACsC,KAAK,CAACpB,KAAK,CAAC,CAAC;IACjD;IAEAA,KAAK,GAAGA,KAAa;IAErB,IAAIqB,IAAI;IACR,IAAItB,OAAO,IAAIA,OAAO,CAACI,MAAM,EAAE;MAC7BkB,IAAI,GAAGrB,KAAK,CAACK,cAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLgB,IAAI,GAAGrB,KAAK,CAACQ,WAAW,CAAC,CAAC;IAC5B;IAEA,IAAIa,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,IAAI,EAAE;MAC3B,MAAM,IAAIC,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,IAAIC,KAAK,CAACvB,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIsB,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAOtB,KAAK;EACd;AACF,CAAC;AAAC,IAAAwB,QAAA,GAEaxC,IAAI;AAAAyC,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACnBG,MAAM,CAACF,OAAO,GAAGzC,IAAI"}