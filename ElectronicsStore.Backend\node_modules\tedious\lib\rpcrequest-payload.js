"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var _allHeaders = require("./all-headers");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
// const OPTION = {
//   WITH_RECOMPILE: 0x01,
//   NO_METADATA: 0x02,
//   REUSE_METADATA: 0x04
// };
const STATUS = {
  BY_REF_VALUE: 0x01,
  DEFAULT_VALUE: 0x02
};

/*
  s2.2.6.5
 */
class RpcRequestPayload {
  constructor(procedure, parameters, txnDescriptor, options, collation) {
    this.procedure = procedure;
    this.parameters = parameters;
    this.options = options;
    this.txnDescriptor = txnDescriptor;
    this.collation = collation;
  }
  [Symbol.iterator]() {
    return this.generateData();
  }
  *generateData() {
    const buffer = new _writableTrackingBuffer.default(500);
    if (this.options.tdsVersion >= '7_2') {
      const outstandingRequestCount = 1;
      (0, _allHeaders.writeToTrackingBuffer)(buffer, this.txnDescriptor, outstandingRequestCount);
    }
    if (typeof this.procedure === 'string') {
      buffer.writeUsVarchar(this.procedure);
    } else {
      buffer.writeUShort(0xFFFF);
      buffer.writeUShort(this.procedure);
    }
    const optionFlags = 0;
    buffer.writeUInt16LE(optionFlags);
    yield buffer.data;
    const parametersLength = this.parameters.length;
    for (let i = 0; i < parametersLength; i++) {
      yield* this.generateParameterData(this.parameters[i]);
    }
  }
  toString(indent = '') {
    return indent + ('RPC Request - ' + this.procedure);
  }
  *generateParameterData(parameter) {
    const buffer = new _writableTrackingBuffer.default(1 + 2 + Buffer.byteLength(parameter.name, 'ucs-2') + 1);
    if (parameter.name) {
      buffer.writeBVarchar('@' + parameter.name);
    } else {
      buffer.writeBVarchar('');
    }
    let statusFlags = 0;
    if (parameter.output) {
      statusFlags |= STATUS.BY_REF_VALUE;
    }
    buffer.writeUInt8(statusFlags);
    yield buffer.data;
    const param = {
      value: parameter.value
    };
    const type = parameter.type;
    if ((type.id & 0x30) === 0x20) {
      if (parameter.length) {
        param.length = parameter.length;
      } else if (type.resolveLength) {
        param.length = type.resolveLength(parameter);
      }
    }
    if (parameter.precision) {
      param.precision = parameter.precision;
    } else if (type.resolvePrecision) {
      param.precision = type.resolvePrecision(parameter);
    }
    if (parameter.scale) {
      param.scale = parameter.scale;
    } else if (type.resolveScale) {
      param.scale = type.resolveScale(parameter);
    }
    if (this.collation) {
      param.collation = this.collation;
    }
    yield type.generateTypeInfo(param, this.options);
    yield type.generateParameterLength(param, this.options);
    yield* type.generateParameterData(param, this.options);
  }
}
var _default = RpcRequestPayload;
exports.default = _default;
module.exports = RpcRequestPayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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