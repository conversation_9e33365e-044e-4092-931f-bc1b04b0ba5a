import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
  ShoppingCart as CartIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Clear as ClearIcon,
  Person as CustomerIcon,
} from '@mui/icons-material';
import PageContainer from '../components/PageContainer';

interface Product {
  id: number;
  name: string;
  price: number;
  stock: number;
  image: string;
  barcode?: string;
}

interface CartItem {
  product: Product;
  quantity: number;
  total: number;
}

interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
}

const POS: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [amountPaid, setAmountPaid] = useState<number>(0);
  const [discount, setDiscount] = useState<number>(0);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [lastSale, setLastSale] = useState<any>(null);

  // Mock products data
  useEffect(() => {
    const mockProducts: Product[] = [
      {
        id: 1,
        name: 'iPhone 15 Pro',
        price: 4500,
        stock: 25,
        image: '/api/placeholder/150/150',
        barcode: '123456789012',
      },
      {
        id: 2,
        name: 'Samsung Galaxy S24',
        price: 3800,
        stock: 15,
        image: '/api/placeholder/150/150',
        barcode: '123456789013',
      },
      {
        id: 3,
        name: 'MacBook Pro M3',
        price: 8500,
        stock: 8,
        image: '/api/placeholder/150/150',
        barcode: '123456789014',
      },
      {
        id: 4,
        name: 'AirPods Pro',
        price: 950,
        stock: 50,
        image: '/api/placeholder/150/150',
        barcode: '123456789015',
      },
      {
        id: 5,
        name: 'Dell XPS 13',
        price: 4200,
        stock: 12,
        image: '/api/placeholder/150/150',
        barcode: '123456789016',
      },
    ];
    setProducts(mockProducts);
  }, []);

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode?.includes(searchTerm)
  );

  const addToCart = (product: Product) => {
    if (product.stock <= 0) {
      alert('المنتج غير متوفر في المخزون');
      return;
    }

    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      if (existingItem.quantity >= product.stock) {
        alert('لا يمكن إضافة المزيد - المخزون غير كافي');
        return;
      }
      updateQuantity(product.id, existingItem.quantity + 1);
    } else {
      const newItem: CartItem = {
        product,
        quantity: 1,
        total: product.price,
      };
      setCart([...cart, newItem]);
    }
  };

  const updateQuantity = (productId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    const product = products.find(p => p.id === productId);
    if (product && newQuantity > product.stock) {
      alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
      return;
    }

    setCart(cart.map(item =>
      item.product.id === productId
        ? { ...item, quantity: newQuantity, total: item.product.price * newQuantity }
        : item
    ));
  };

  const removeFromCart = (productId: number) => {
    setCart(cart.filter(item => item.product.id !== productId));
  };

  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setDiscount(0);
    setAmountPaid(0);
  };

  const getSubtotal = () => {
    return cart.reduce((sum, item) => sum + item.total, 0);
  };

  const getTax = () => {
    return getSubtotal() * 0.15; // 15% VAT
  };

  const getTotal = () => {
    return getSubtotal() + getTax() - discount;
  };

  const getChange = () => {
    return Math.max(0, amountPaid - getTotal());
  };

  const handlePayment = () => {
    if (cart.length === 0) {
      alert('السلة فارغة');
      return;
    }
    setShowPaymentDialog(true);
  };

  const completeSale = () => {
    if (paymentMethod !== 'credit' && amountPaid < getTotal()) {
      alert('المبلغ المدفوع أقل من المطلوب');
      return;
    }

    // Create sale record
    const saleData = {
      id: Date.now(),
      orderNumber: `ORD-${Date.now()}`,
      customer: selectedCustomer,
      items: cart,
      subtotal: getSubtotal(),
      tax: getTax(),
      discount: discount,
      total: getTotal(),
      amountPaid: amountPaid,
      change: getChange(),
      paymentMethod: paymentMethod,
      date: new Date().toISOString(),
    };

    setLastSale(saleData);
    setShowPaymentDialog(false);
    setShowReceiptDialog(true);
    
    // Update stock (in real app, this would be API call)
    const updatedProducts = products.map(product => {
      const cartItem = cart.find(item => item.product.id === product.id);
      if (cartItem) {
        return { ...product, stock: product.stock - cartItem.quantity };
      }
      return product;
    });
    setProducts(updatedProducts);
    
    clearCart();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <PageContainer>
      <Box sx={{ p: 2 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 700, textAlign: 'center' }}>
          نقاط البيع
        </Typography>

        <Grid container spacing={3}>
          {/* Products Section */}
          <Grid item xs={12} md={8}>
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <TextField
                  fullWidth
                  placeholder="البحث عن منتج (الاسم أو الباركود)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />
              </CardContent>
            </Card>

            <Grid container spacing={2}>
              {filteredProducts.map((product) => (
                <Grid item xs={12} sm={6} md={4} key={product.id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'scale(1.02)',
                        boxShadow: 4,
                      },
                    }}
                    onClick={() => addToCart(product)}
                  >
                    <CardMedia
                      component="img"
                      height="120"
                      image={product.image}
                      alt={product.name}
                    />
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="subtitle2" noWrap>
                        {product.name}
                      </Typography>
                      <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                        {formatCurrency(product.price)}
                      </Typography>
                      <Chip
                        label={`متوفر: ${product.stock}`}
                        size="small"
                        color={product.stock > 10 ? 'success' : product.stock > 0 ? 'warning' : 'error'}
                        sx={{ mt: 1 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Cart Section */}
          <Grid item xs={12} md={4}>
            <Card sx={{ position: 'sticky', top: 20 }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                    <CartIcon sx={{ mr: 1 }} />
                    السلة ({cart.length})
                  </Typography>
                  <IconButton onClick={clearCart} color="error">
                    <ClearIcon />
                  </IconButton>
                </Box>

                {cart.length === 0 ? (
                  <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                    السلة فارغة
                  </Typography>
                ) : (
                  <>
                    <TableContainer sx={{ maxHeight: 300, mb: 2 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>المنتج</TableCell>
                            <TableCell>الكمية</TableCell>
                            <TableCell>السعر</TableCell>
                            <TableCell></TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {cart.map((item) => (
                            <TableRow key={item.product.id}>
                              <TableCell>
                                <Typography variant="body2" noWrap>
                                  {item.product.name}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Box display="flex" alignItems="center">
                                  <IconButton
                                    size="small"
                                    onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                                  >
                                    <RemoveIcon />
                                  </IconButton>
                                  <Typography sx={{ mx: 1 }}>{item.quantity}</Typography>
                                  <IconButton
                                    size="small"
                                    onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                                  >
                                    <AddIcon />
                                  </IconButton>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {formatCurrency(item.total)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => removeFromCart(item.product.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Divider sx={{ my: 2 }} />

                    {/* Totals */}
                    <Box>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography>المجموع الفرعي:</Typography>
                        <Typography>{formatCurrency(getSubtotal())}</Typography>
                      </Box>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography>الضريبة (15%):</Typography>
                        <Typography>{formatCurrency(getTax())}</Typography>
                      </Box>
                      {discount > 0 && (
                        <Box display="flex" justifyContent="space-between" mb={1}>
                          <Typography>الخصم:</Typography>
                          <Typography color="success.main">-{formatCurrency(discount)}</Typography>
                        </Box>
                      )}
                      <Divider sx={{ my: 1 }} />
                      <Box display="flex" justifyContent="space-between" mb={2}>
                        <Typography variant="h6" fontWeight="bold">الإجمالي:</Typography>
                        <Typography variant="h6" fontWeight="bold" color="primary">
                          {formatCurrency(getTotal())}
                        </Typography>
                      </Box>
                    </Box>

                    <Button
                      fullWidth
                      variant="contained"
                      size="large"
                      startIcon={<PaymentIcon />}
                      onClick={handlePayment}
                      sx={{ mt: 2 }}
                    >
                      الدفع
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Payment Dialog */}
        <Dialog open={showPaymentDialog} onClose={() => setShowPaymentDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>إتمام عملية الدفع</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>طريقة الدفع</InputLabel>
                  <Select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    label="طريقة الدفع"
                  >
                    <MenuItem value="cash">نقدي</MenuItem>
                    <MenuItem value="card">بطاقة</MenuItem>
                    <MenuItem value="transfer">تحويل</MenuItem>
                    <MenuItem value="credit">آجل</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="الخصم"
                  type="number"
                  value={discount}
                  onChange={(e) => setDiscount(Number(e.target.value))}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
                  }}
                />
              </Grid>

              {paymentMethod !== 'credit' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="المبلغ المدفوع"
                    type="number"
                    value={amountPaid}
                    onChange={(e) => setAmountPaid(Number(e.target.value))}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">ريال</InputAdornment>,
                    }}
                  />
                  {amountPaid > getTotal() && (
                    <Alert severity="info" sx={{ mt: 1 }}>
                      الباقي: {formatCurrency(getChange())}
                    </Alert>
                  )}
                </Grid>
              )}

              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="h6" textAlign="center">
                    المبلغ المطلوب: {formatCurrency(getTotal())}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowPaymentDialog(false)}>إلغاء</Button>
            <Button variant="contained" onClick={completeSale}>
              إتمام البيع
            </Button>
          </DialogActions>
        </Dialog>

        {/* Receipt Dialog */}
        <Dialog open={showReceiptDialog} onClose={() => setShowReceiptDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle sx={{ textAlign: 'center' }}>
            <ReceiptIcon sx={{ mr: 1 }} />
            فاتورة البيع
          </DialogTitle>
          <DialogContent>
            {lastSale && (
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>متجر الإلكترونيات</Typography>
                <Typography variant="body2" gutterBottom>رقم الطلب: {lastSale.orderNumber}</Typography>
                <Typography variant="body2" gutterBottom>
                  التاريخ: {new Date(lastSale.date).toLocaleString('ar-SA')}
                </Typography>
                
                <Divider sx={{ my: 2 }} />
                
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>المنتج</TableCell>
                      <TableCell>الكمية</TableCell>
                      <TableCell>السعر</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lastSale.items.map((item: CartItem, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{item.product.name}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.total)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ textAlign: 'right' }}>
                  <Typography>المجموع الفرعي: {formatCurrency(lastSale.subtotal)}</Typography>
                  <Typography>الضريبة: {formatCurrency(lastSale.tax)}</Typography>
                  {lastSale.discount > 0 && (
                    <Typography>الخصم: -{formatCurrency(lastSale.discount)}</Typography>
                  )}
                  <Typography variant="h6" fontWeight="bold">
                    الإجمالي: {formatCurrency(lastSale.total)}
                  </Typography>
                  <Typography>المدفوع: {formatCurrency(lastSale.amountPaid)}</Typography>
                  {lastSale.change > 0 && (
                    <Typography>الباقي: {formatCurrency(lastSale.change)}</Typography>
                  )}
                </Box>
                
                <Typography variant="body2" sx={{ mt: 2 }}>
                  شكراً لتسوقكم معنا!
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowReceiptDialog(false)}>إغلاق</Button>
            <Button variant="contained" startIcon={<ReceiptIcon />}>
              طباعة
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </PageContainer>
  );
};

export default POS;
