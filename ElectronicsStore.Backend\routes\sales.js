const express = require('express');
const router = express.Router();
const { runQuery, getRow, getAllRows, db } = require('../config/database');

// GET /api/sales - Get all sales
router.get('/', async (req, res) => {
  try {
    const { status, payment_method, date_from, date_to, page = 1, limit = 50 } = req.query;
    
    let sql = `
      SELECT s.*, 
        GROUP_CONCAT(
          json_object(
            'product_id', si.product_id,
            'product_name', si.product_name,
            'quantity', si.quantity,
            'unit_price', si.unit_price,
            'total', si.total
          )
        ) as items
      FROM sales s
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE 1=1
    `;
    let params = [];
    
    if (status && status !== 'all') {
      sql += ' AND s.status = ?';
      params.push(status);
    }
    
    if (payment_method && payment_method !== 'all') {
      sql += ' AND s.payment_method = ?';
      params.push(payment_method);
    }
    
    if (date_from) {
      sql += ' AND DATE(s.sale_date) >= ?';
      params.push(date_from);
    }
    
    if (date_to) {
      sql += ' AND DATE(s.sale_date) <= ?';
      params.push(date_to);
    }
    
    sql += ' GROUP BY s.id ORDER BY s.sale_date DESC';
    
    // Add pagination
    const offset = (page - 1) * limit;
    sql += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const sales = await getAllRows(sql, params);
    
    // Parse items JSON for each sale
    const salesWithItems = sales.map(sale => ({
      ...sale,
      items: sale.items ? sale.items.split(',').map(item => JSON.parse(item)) : []
    }));
    
    // Get total count for pagination
    let countSql = 'SELECT COUNT(*) as total FROM sales WHERE 1=1';
    let countParams = [];
    
    if (status && status !== 'all') {
      countSql += ' AND status = ?';
      countParams.push(status);
    }
    
    if (payment_method && payment_method !== 'all') {
      countSql += ' AND payment_method = ?';
      countParams.push(payment_method);
    }
    
    if (date_from) {
      countSql += ' AND DATE(sale_date) >= ?';
      countParams.push(date_from);
    }
    
    if (date_to) {
      countSql += ' AND DATE(sale_date) <= ?';
      countParams.push(date_to);
    }
    
    const { total } = await getRow(countSql, countParams);
    
    res.json({
      success: true,
      data: salesWithItems,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching sales:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المبيعات'
    });
  }
});

// GET /api/sales/:id - Get single sale
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sale = await getRow('SELECT * FROM sales WHERE id = ?', [id]);
    if (!sale) {
      return res.status(404).json({
        success: false,
        message: 'البيع غير موجود'
      });
    }
    
    const items = await getAllRows('SELECT * FROM sale_items WHERE sale_id = ?', [id]);
    
    res.json({
      success: true,
      data: {
        ...sale,
        items
      }
    });
  } catch (error) {
    console.error('Error fetching sale:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب البيع'
    });
  }
});

// POST /api/sales - Create new sale
router.post('/', async (req, res) => {
  const transaction = db.prepare('BEGIN TRANSACTION');
  
  try {
    const {
      customer_id,
      customer_name,
      customer_email,
      items,
      subtotal,
      tax,
      discount,
      total,
      amount_paid,
      change_amount,
      payment_method,
      sales_person,
      notes
    } = req.body;
    
    // Validation
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد منتجات في البيع'
      });
    }
    
    if (!payment_method || !total) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة مفقودة'
      });
    }
    
    // Start transaction
    transaction.run();
    
    // Generate order number
    const orderNumber = `ORD-${Date.now()}`;
    
    // Create sale record
    const saleResult = await runQuery(`
      INSERT INTO sales (
        order_number, customer_id, customer_name, customer_email,
        subtotal, tax, discount, total, amount_paid, change_amount,
        payment_method, sales_person, notes, sale_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [
      orderNumber, customer_id, customer_name, customer_email,
      parseFloat(subtotal), parseFloat(tax), parseFloat(discount) || 0,
      parseFloat(total), parseFloat(amount_paid), parseFloat(change_amount) || 0,
      payment_method, sales_person, notes
    ]);
    
    const saleId = saleResult.id;
    
    // Create sale items and update stock
    for (const item of items) {
      // Add sale item
      await runQuery(`
        INSERT INTO sale_items (
          sale_id, product_id, product_name, quantity, unit_price, total
        ) VALUES (?, ?, ?, ?, ?, ?)
      `, [
        saleId, item.product_id, item.product_name,
        parseInt(item.quantity), parseFloat(item.unit_price),
        parseFloat(item.total)
      ]);
      
      // Update product stock
      await runQuery(`
        UPDATE products 
        SET stock = stock - ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [parseInt(item.quantity), item.product_id]);
    }
    
    // Update customer statistics if customer exists
    if (customer_id) {
      await runQuery(`
        UPDATE customers SET
          total_orders = total_orders + 1,
          total_spent = total_spent + ?,
          last_order = CURRENT_DATE,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [parseFloat(total), customer_id]);
    }
    
    // Commit transaction
    db.prepare('COMMIT').run();
    
    // Get the complete sale with items
    const completeSale = await getRow('SELECT * FROM sales WHERE id = ?', [saleId]);
    const saleItems = await getAllRows('SELECT * FROM sale_items WHERE sale_id = ?', [saleId]);
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء البيع بنجاح',
      data: {
        ...completeSale,
        items: saleItems
      }
    });
  } catch (error) {
    // Rollback transaction on error
    db.prepare('ROLLBACK').run();
    console.error('Error creating sale:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء البيع'
    });
  }
});

// PUT /api/sales/:id/status - Update sale status
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['pending', 'completed', 'cancelled', 'refunded'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'حالة البيع غير صحيحة'
      });
    }
    
    const sale = await getRow('SELECT * FROM sales WHERE id = ?', [id]);
    if (!sale) {
      return res.status(404).json({
        success: false,
        message: 'البيع غير موجود'
      });
    }
    
    await runQuery(`
      UPDATE sales 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `, [status, id]);
    
    const updatedSale = await getRow('SELECT * FROM sales WHERE id = ?', [id]);
    
    res.json({
      success: true,
      message: 'تم تحديث حالة البيع بنجاح',
      data: updatedSale
    });
  } catch (error) {
    console.error('Error updating sale status:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة البيع'
    });
  }
});

// GET /api/sales/reports/daily - Get daily sales report
router.get('/reports/daily', async (req, res) => {
  try {
    const { date = new Date().toISOString().split('T')[0] } = req.query;
    
    const dailyStats = await getRow(`
      SELECT 
        COUNT(*) as total_sales,
        SUM(total) as total_revenue,
        SUM(subtotal) as subtotal,
        SUM(tax) as total_tax,
        SUM(discount) as total_discount,
        AVG(total) as avg_sale_amount,
        COUNT(CASE WHEN payment_method = 'cash' THEN 1 END) as cash_sales,
        COUNT(CASE WHEN payment_method = 'card' THEN 1 END) as card_sales,
        COUNT(CASE WHEN payment_method = 'transfer' THEN 1 END) as transfer_sales
      FROM sales 
      WHERE DATE(sale_date) = ? AND status = 'completed'
    `, [date]);
    
    const topProducts = await getAllRows(`
      SELECT 
        si.product_name,
        SUM(si.quantity) as total_quantity,
        SUM(si.total) as total_revenue
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      WHERE DATE(s.sale_date) = ? AND s.status = 'completed'
      GROUP BY si.product_id, si.product_name
      ORDER BY total_quantity DESC
      LIMIT 10
    `, [date]);
    
    res.json({
      success: true,
      data: {
        date,
        stats: dailyStats,
        topProducts
      }
    });
  } catch (error) {
    console.error('Error fetching daily sales report:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات اليومي'
    });
  }
});

module.exports = router;
