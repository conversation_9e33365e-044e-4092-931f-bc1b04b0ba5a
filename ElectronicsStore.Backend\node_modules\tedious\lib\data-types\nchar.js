"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const NChar = {
  id: 0xEF,
  type: 'NCHAR',
  name: '<PERSON><PERSON>',
  maximumLength: 4000,
  declaration: function (parameter) {
    // const value = parameter.value as null | string | { toString(): string };
    const value = parameter.value; // Temporary solution. Remove 'any' later.

    let length;
    if (parameter.length) {
      length = parameter.length;
    } else if (parameter.value != null) {
      length = value.toString().length || 1;
    } else if (parameter.value === null && !parameter.output) {
      length = 1;
    } else {
      length = this.maximumLength;
    }
    if (length < this.maximumLength) {
      return 'nchar(' + length + ')';
    } else {
      return 'nchar(' + this.maximumLength + ')';
    }
  },
  resolveLength: function (parameter) {
    // const value = parameter.value as null | string | { toString(): string };
    const value = parameter.value; // Temporary solution. Remove 'any' later.

    if (parameter.length != null) {
      return parameter.length;
    } else if (parameter.value != null) {
      if (Buffer.isBuffer(parameter.value)) {
        return parameter.value.length / 2 || 1;
      } else {
        return value.toString().length || 1;
      }
    } else {
      return this.maximumLength;
    }
  },
  generateTypeInfo: function (parameter) {
    const buffer = Buffer.alloc(8);
    buffer.writeUInt8(this.id, 0);
    buffer.writeUInt16LE(parameter.length * 2, 1);
    if (parameter.collation) {
      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);
    }
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const {
      value
    } = parameter;
    if (value instanceof Buffer) {
      const length = value.length;
      const buffer = Buffer.alloc(2);
      buffer.writeUInt16LE(length, 0);
      return buffer;
    } else {
      const length = Buffer.byteLength(value.toString(), 'ucs2');
      const buffer = Buffer.alloc(2);
      buffer.writeUInt16LE(length, 0);
      return buffer;
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const value = parameter.value;
    if (value instanceof Buffer) {
      yield value;
    } else {
      yield Buffer.from(value, 'ucs2');
    }
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'string') {
      throw new TypeError('Invalid string.');
    }
    return value;
  }
};
var _default = NChar;
exports.default = _default;
module.exports = NChar;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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