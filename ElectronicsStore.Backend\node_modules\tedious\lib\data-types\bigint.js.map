{"version": 3, "file": "bigint.js", "names": ["_intn", "_interopRequireDefault", "require", "_writableTrackingBuffer", "obj", "__esModule", "default", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "BigInt", "id", "type", "name", "declaration", "generateTypeInfo", "IntN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "WritableTrackingBuffer", "writeInt64LE", "Number", "data", "validate", "isNaN", "TypeError", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "_default", "exports", "module"], "sources": ["../../src/data-types/bigint.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport IntN from './intn';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst DATA_LENGTH = Buffer.from([0x08]);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst BigInt: DataType = {\n  id: 0x7F,\n  type: 'INT8',\n  name: 'BigInt',\n\n  declaration: function() {\n    return 'bigint';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([IntN.id, 0x08]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = new WritableTrackingBuffer(8);\n    buffer.writeInt64LE(Number(parameter.value));\n    yield buffer.data;\n  },\n\n  validate: function(value): null | number {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'number') {\n      value = Number(value);\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n\n    if (value < Number.MIN_SAFE_INTEGER || value > Number.MAX_SAFE_INTEGER) {\n      throw new TypeError(`Value must be between ${Number.MIN_SAFE_INTEGER} and ${Number.MAX_SAFE_INTEGER}, inclusive.  For smaller or bigger numbers, use VarChar type.`);\n    }\n\n    return value;\n  }\n};\n\nexport default BigInt;\nmodule.exports = BigInt;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,MAAgB,GAAG;EACvBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EAEdC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,QAAQ;EACjB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,aAAI,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAED,CAAEe,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAG,IAAIC,+BAAsB,CAAC,CAAC,CAAC;IAC5CD,MAAM,CAACE,YAAY,CAACC,MAAM,CAACP,SAAS,CAACE,KAAK,CAAC,CAAC;IAC5C,MAAME,MAAM,CAACI,IAAI;EACnB,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAASP,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGK,MAAM,CAACL,KAAK,CAAC;IACvB;IAEA,IAAIQ,KAAK,CAACR,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIS,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAIT,KAAK,GAAGK,MAAM,CAACK,gBAAgB,IAAIV,KAAK,GAAGK,MAAM,CAACM,gBAAgB,EAAE;MACtE,MAAM,IAAIF,SAAS,CAAE,yBAAwBJ,MAAM,CAACK,gBAAiB,QAAOL,MAAM,CAACM,gBAAiB,gEAA+D,CAAC;IACtK;IAEA,OAAOX,KAAK;EACd;AACF,CAAC;AAAC,IAAAY,QAAA,GAEatB,MAAM;AAAAuB,OAAA,CAAA5B,OAAA,GAAA2B,QAAA;AACrBE,MAAM,CAACD,OAAO,GAAGvB,MAAM"}