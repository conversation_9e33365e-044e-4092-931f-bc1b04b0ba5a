{"version": 3, "file": "datetimeoffset.js", "names": ["_core", "require", "_writableTrackingBuffer", "_interopRequireDefault", "obj", "__esModule", "default", "EPOCH_DATE", "LocalDate", "ofYearDay", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DateTimeOffset", "id", "type", "name", "declaration", "parameter", "resolveScale", "scale", "value", "generateTypeInfo", "generateParameterLength", "options", "Error", "generateParameterData", "buffer", "WritableTrackingBuffer", "timestamp", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getMilliseconds", "Math", "pow", "nanosecondDelta", "round", "writeUInt24LE", "writeUInt32LE", "writeUInt40LE", "date", "of", "getUTCFullYear", "getUTCMonth", "getUTCDate", "days", "until", "ChronoUnit", "DAYS", "offset", "getTimezoneOffset", "writeInt16LE", "data", "validate", "collation", "Date", "parse", "year", "useUTC", "getFullYear", "TypeError", "isNaN", "_default", "exports", "module"], "sources": ["../../src/data-types/datetimeoffset.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport { ChronoUnit, LocalDate } from '@js-joda/core';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst EPOCH_DATE = LocalDate.ofYearDay(1, 1);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst DateTimeOffset: DataType & { resolveScale: NonNullable<DataType['resolveScale']> } = {\n  id: 0x2B,\n  type: 'DATETIMEOFFSETN',\n  name: 'DateTimeOffset',\n  declaration: function(parameter) {\n    return 'datetimeoffset(' + (this.resolveScale(parameter)) + ')';\n  },\n  resolveScale: function(parameter) {\n    if (parameter.scale != null) {\n      return parameter.scale;\n    } else if (parameter.value === null) {\n      return 0;\n    } else {\n      return 7;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    return Buffer.from([this.id, parameter.scale!]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    switch (parameter.scale) {\n      case 0:\n      case 1:\n      case 2:\n        return Buffer.from([0x08]);\n\n      case 3:\n      case 4:\n        return Buffer.from([0x09]);\n\n      case 5:\n      case 6:\n      case 7:\n        return Buffer.from([0x0A]);\n\n      default:\n        throw new Error('invalid scale');\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value;\n    let scale = parameter.scale;\n\n    const buffer = new WritableTrackingBuffer(16);\n    scale = scale!;\n\n    let timestamp: number;\n    timestamp = ((value.getUTCHours() * 60 + value.getUTCMinutes()) * 60 + value.getUTCSeconds()) * 1000 + value.getMilliseconds();\n    timestamp = timestamp * Math.pow(10, scale - 3);\n    timestamp += (value.nanosecondDelta != null ? value.nanosecondDelta : 0) * Math.pow(10, scale);\n    timestamp = Math.round(timestamp);\n\n    switch (scale) {\n      case 0:\n      case 1:\n      case 2:\n        buffer.writeUInt24LE(timestamp);\n        break;\n      case 3:\n      case 4:\n        buffer.writeUInt32LE(timestamp);\n        break;\n      case 5:\n      case 6:\n      case 7:\n        buffer.writeUInt40LE(timestamp);\n    }\n\n    const date = LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());\n    const days = EPOCH_DATE.until(date, ChronoUnit.DAYS);\n    buffer.writeUInt24LE(days);\n\n    const offset = -value.getTimezoneOffset();\n    buffer.writeInt16LE(offset);\n    yield buffer.data;\n  },\n  validate: function(value: any, collation, options): null | number {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof Date)) {\n      value = new Date(Date.parse(value));\n    }\n\n    value = value as Date;\n\n    let year;\n    if (options && options.useUTC) {\n      year = value.getUTCFullYear();\n    } else {\n      year = value.getFullYear();\n    }\n\n    if (year < 1 || year > 9999) {\n      throw new TypeError('Out of range.');\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid date.');\n    }\n\n    return value;\n  }\n};\n\nexport default DateTimeOffset;\nmodule.exports = DateTimeOffset;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAiF,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,UAAU,GAAGC,eAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,cAAkF,GAAG;EACzFC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,OAAO,iBAAiB,GAAI,IAAI,CAACC,YAAY,CAACD,SAAS,CAAE,GAAG,GAAG;EACjE,CAAC;EACDC,YAAY,EAAE,SAAAA,CAASD,SAAS,EAAE;IAChC,IAAIA,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOF,SAAS,CAACE,KAAK;IACxB,CAAC,MAAM,IAAIF,SAAS,CAACG,KAAK,KAAK,IAAI,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;EAEDC,gBAAgBA,CAACJ,SAAS,EAAE;IAC1B,OAAOP,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACE,EAAE,EAAEI,SAAS,CAACE,KAAK,CAAE,CAAC;EACjD,CAAC;EAEDG,uBAAuBA,CAACL,SAAS,EAAEM,OAAO,EAAE;IAC1C,IAAIN,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,QAAQQ,SAAS,CAACE,KAAK;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOT,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B;QACE,MAAM,IAAIa,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC;EAED,CAAEC,qBAAqBA,CAACR,SAAS,EAAEM,OAAO,EAAE;IAC1C,IAAIN,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGH,SAAS,CAACG,KAAK;IAC7B,IAAID,KAAK,GAAGF,SAAS,CAACE,KAAK;IAE3B,MAAMO,MAAM,GAAG,IAAIC,+BAAsB,CAAC,EAAE,CAAC;IAC7CR,KAAK,GAAGA,KAAM;IAEd,IAAIS,SAAiB;IACrBA,SAAS,GAAG,CAAC,CAACR,KAAK,CAACS,WAAW,CAAC,CAAC,GAAG,EAAE,GAAGT,KAAK,CAACU,aAAa,CAAC,CAAC,IAAI,EAAE,GAAGV,KAAK,CAACW,aAAa,CAAC,CAAC,IAAI,IAAI,GAAGX,KAAK,CAACY,eAAe,CAAC,CAAC;IAC9HJ,SAAS,GAAGA,SAAS,GAAGK,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEf,KAAK,GAAG,CAAC,CAAC;IAC/CS,SAAS,IAAI,CAACR,KAAK,CAACe,eAAe,IAAI,IAAI,GAAGf,KAAK,CAACe,eAAe,GAAG,CAAC,IAAIF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEf,KAAK,CAAC;IAC9FS,SAAS,GAAGK,IAAI,CAACG,KAAK,CAACR,SAAS,CAAC;IAEjC,QAAQT,KAAK;MACX,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJO,MAAM,CAACW,aAAa,CAACT,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;QACJF,MAAM,CAACY,aAAa,CAACV,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJF,MAAM,CAACa,aAAa,CAACX,SAAS,CAAC;IACnC;IAEA,MAAMY,IAAI,GAAGjC,eAAS,CAACkC,EAAE,CAACrB,KAAK,CAACsB,cAAc,CAAC,CAAC,EAAEtB,KAAK,CAACuB,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEvB,KAAK,CAACwB,UAAU,CAAC,CAAC,CAAC;IAC9F,MAAMC,IAAI,GAAGvC,UAAU,CAACwC,KAAK,CAACN,IAAI,EAAEO,gBAAU,CAACC,IAAI,CAAC;IACpDtB,MAAM,CAACW,aAAa,CAACQ,IAAI,CAAC;IAE1B,MAAMI,MAAM,GAAG,CAAC7B,KAAK,CAAC8B,iBAAiB,CAAC,CAAC;IACzCxB,MAAM,CAACyB,YAAY,CAACF,MAAM,CAAC;IAC3B,MAAMvB,MAAM,CAAC0B,IAAI;EACnB,CAAC;EACDC,QAAQ,EAAE,SAAAA,CAASjC,KAAU,EAAEkC,SAAS,EAAE/B,OAAO,EAAiB;IAChE,IAAIH,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAYmC,IAAI,CAAC,EAAE;MAC5BnC,KAAK,GAAG,IAAImC,IAAI,CAACA,IAAI,CAACC,KAAK,CAACpC,KAAK,CAAC,CAAC;IACrC;IAEAA,KAAK,GAAGA,KAAa;IAErB,IAAIqC,IAAI;IACR,IAAIlC,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAAE;MAC7BD,IAAI,GAAGrC,KAAK,CAACsB,cAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLe,IAAI,GAAGrC,KAAK,CAACuC,WAAW,CAAC,CAAC;IAC5B;IAEA,IAAIF,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,IAAI,EAAE;MAC3B,MAAM,IAAIG,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,IAAIC,KAAK,CAACzC,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIwC,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAOxC,KAAK;EACd;AACF,CAAC;AAAC,IAAA0C,QAAA,GAEalD,cAAc;AAAAmD,OAAA,CAAA1D,OAAA,GAAAyD,QAAA;AAC7BE,MAAM,CAACD,OAAO,GAAGnD,cAAc"}