{"version": 3, "file": "bulk-load-payload.js", "names": ["BulkLoadPayload", "constructor", "bulkLoad", "iterator", "rowToPacketTransform", "Symbol", "asyncIterator", "toString", "indent", "exports"], "sources": ["../src/bulk-load-payload.ts"], "sourcesContent": ["import BulkLoad from './bulk-load';\n\nexport class BulkLoadPayload implements AsyncIterable<Buffer> {\n  declare bulkLoad: BulkLoad;\n  declare iterator: AsyncIterableIterator<Buffer>;\n\n  constructor(bulkLoad: BulkLoad) {\n    this.bulkLoad = bulkLoad;\n\n    // We need to grab the iterator here so that `error` event handlers are set up\n    // as early as possible (and are not potentially lost).\n    this.iterator = this.bulkLoad.rowToPacketTransform[Symbol.asyncIterator]();\n  }\n\n  [Symbol.asyncIterator]() {\n    return this.iterator;\n  }\n\n  toString(indent = '') {\n    return indent + ('BulkLoad');\n  }\n}\n"], "mappings": ";;;;;;AAEO,MAAMA,eAAe,CAAkC;EAI5DC,WAAWA,CAACC,QAAkB,EAAE;IAC9B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;;IAExB;IACA;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACD,QAAQ,CAACE,oBAAoB,CAACC,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EAC5E;EAEA,CAACD,MAAM,CAACC,aAAa,IAAI;IACvB,OAAO,IAAI,CAACH,QAAQ;EACtB;EAEAI,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,GAAI,UAAW;EAC9B;AACF;AAACC,OAAA,CAAAT,eAAA,GAAAA,eAAA"}