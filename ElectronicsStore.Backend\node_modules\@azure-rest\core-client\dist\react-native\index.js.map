{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;GAGG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,EACL,2BAA2B,GAE5B,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mCAAmC,EAAE,MAAM,6BAA6B,CAAC;AAClF,cAAc,gBAAgB,CAAC;AAC/B,cAAc,aAAa,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Azure Rest Core Client library for JavaScript\n * @packageDocumentation\n */\n\nexport { createRestError } from \"./restError.js\";\nexport {\n  addCredentialPipelinePolicy,\n  AddCredentialPipelinePolicyOptions,\n} from \"./clientHelpers.js\";\nexport { operationOptionsToRequestParameters } from \"./operationOptionHelpers.js\";\nexport * from \"./getClient.js\";\nexport * from \"./common.js\";\n"]}