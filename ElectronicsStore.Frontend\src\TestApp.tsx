import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

const TestApp: React.FC = () => {
  return (
    <Box sx={{ p: 4, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      <Typography variant="h2" sx={{ mb: 4, textAlign: 'center', color: '#1976d2' }}>
        🏪 متجر الإلكترونيات
      </Typography>
      
      <Card sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
        <CardContent>
          <Typography variant="h4" sx={{ mb: 2, color: '#2e7d32' }}>
            ✅ التطبيق يعمل بنجاح!
          </Typography>
          
          <Typography variant="body1" sx={{ mb: 2 }}>
            إذا كنت ترى هذه الرسالة، فهذا يعني أن:
          </Typography>
          
          <Box component="ul" sx={{ pl: 3 }}>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              ✅ React يعمل بشكل صحيح
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              ✅ Material-UI يعمل بشكل صحيح
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              ✅ TypeScript يعمل بشكل صحيح
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              ✅ Vite يعمل بشكل صحيح
            </Typography>
          </Box>
          
          <Typography variant="h6" sx={{ mt: 3, color: '#1976d2' }}>
            🚀 جاهز لإضافة Dashboard الكامل!
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TestApp;
