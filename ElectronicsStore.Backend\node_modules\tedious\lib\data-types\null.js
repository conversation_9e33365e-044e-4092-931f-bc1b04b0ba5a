"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const Null = {
  id: 0x1F,
  type: 'NULL',
  name: 'Null',
  declaration() {
    throw new Error('not implemented');
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = Null;
exports.default = _default;
module.exports = Null;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJOdWxsIiwiaWQiLCJ0eXBlIiwibmFtZSIsImRlY2xhcmF0aW9uIiwiRXJyb3IiLCJnZW5lcmF0ZVR5cGVJbmZvIiwiZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgiLCJnZW5lcmF0ZVBhcmFtZXRlckRhdGEiLCJ2YWxpZGF0ZSIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsImRlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvZGF0YS10eXBlcy9udWxsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgRGF0YVR5cGUgfSBmcm9tICcuLi9kYXRhLXR5cGUnO1xuXG5jb25zdCBOdWxsOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4MUYsXG4gIHR5cGU6ICdOVUxMJyxcbiAgbmFtZTogJ051bGwnLFxuXG4gIGRlY2xhcmF0aW9uKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVUeXBlSW5mbygpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVQYXJhbWV0ZXJEYXRhKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgdmFsaWRhdGUoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgTnVsbDtcbm1vZHVsZS5leHBvcnRzID0gTnVsbDtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRUEsTUFBTUEsSUFBYyxHQUFHO0VBQ3JCQyxFQUFFLEVBQUUsSUFBSTtFQUNSQyxJQUFJLEVBQUUsTUFBTTtFQUNaQyxJQUFJLEVBQUUsTUFBTTtFQUVaQyxXQUFXQSxDQUFBLEVBQUc7SUFDWixNQUFNLElBQUlDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURDLGdCQUFnQkEsQ0FBQSxFQUFHO0lBQ2pCLE1BQU0sSUFBSUQsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREUsdUJBQXVCQSxDQUFBLEVBQUc7SUFDeEIsTUFBTSxJQUFJRixLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVERyxxQkFBcUJBLENBQUEsRUFBRztJQUN0QixNQUFNLElBQUlILEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURJLFFBQVFBLENBQUEsRUFBRztJQUNULE1BQU0sSUFBSUosS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDO0FBQ0YsQ0FBQztBQUFDLElBQUFLLFFBQUEsR0FFYVYsSUFBSTtBQUFBVyxPQUFBLENBQUFDLE9BQUEsR0FBQUYsUUFBQTtBQUNuQkcsTUFBTSxDQUFDRixPQUFPLEdBQUdYLElBQUkifQ==