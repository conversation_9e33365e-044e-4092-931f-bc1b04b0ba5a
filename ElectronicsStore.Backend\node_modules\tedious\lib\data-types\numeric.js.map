{"version": 3, "file": "numeric.js", "names": ["_numericn", "_interopRequireDefault", "require", "_writableTrackingBuffer", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Numeric", "id", "type", "name", "declaration", "parameter", "resolvePrecision", "resolveScale", "precision", "value", "scale", "generateTypeInfo", "NumericN", "generateParameterLength", "options", "generateParameterData", "sign", "Math", "round", "abs", "pow", "buffer", "alloc", "writeUInt8", "writeUInt32LE", "WritableTrackingBuffer", "writeUInt64LE", "data", "validate", "parseFloat", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/numeric.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport NumericN from './numericn';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst Numeric: DataType & { resolveScale: NonNullable<DataType['resolveScale']>, resolvePrecision: NonNullable<DataType['resolvePrecision']> } = {\n  id: 0x3F,\n  type: 'NUMERIC',\n  name: 'Numeric',\n\n  declaration: function(parameter) {\n    return 'numeric(' + (this.resolvePrecision(parameter)) + ', ' + (this.resolveScale(parameter)) + ')';\n  },\n\n  resolvePrecision: function(parameter) {\n    if (parameter.precision != null) {\n      return parameter.precision;\n    } else if (parameter.value === null) {\n      return 1;\n    } else {\n      return 18;\n    }\n  },\n\n  resolveScale: function(parameter) {\n    if (parameter.scale != null) {\n      return parameter.scale;\n    } else {\n      return 0;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    let precision;\n    if (parameter.precision! <= 9) {\n      precision = 0x05;\n    } else if (parameter.precision! <= 19) {\n      precision = 0x09;\n    } else if (parameter.precision! <= 28) {\n      precision = 0x0D;\n    } else {\n      precision = 0x11;\n    }\n\n    return Buffer.from([NumericN.id, precision, parameter.precision!, parameter.scale!]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const precision = parameter.precision!;\n    if (precision <= 9) {\n      return Buffer.from([0x05]);\n    } else if (precision <= 19) {\n      return Buffer.from([0x09]);\n    } else if (precision <= 28) {\n      return Buffer.from([0x0D]);\n    } else {\n      return Buffer.from([0x11]);\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const sign = parameter.value < 0 ? 0 : 1;\n    const value = Math.round(Math.abs(parameter.value * Math.pow(10, parameter.scale!)));\n    if (parameter.precision! <= 9) {\n      const buffer = Buffer.alloc(5);\n      buffer.writeUInt8(sign, 0);\n      buffer.writeUInt32LE(value, 1);\n      yield buffer;\n    } else if (parameter.precision! <= 19) {\n      const buffer = new WritableTrackingBuffer(10);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      yield buffer.data;\n    } else if (parameter.precision! <= 28) {\n      const buffer = new WritableTrackingBuffer(14);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      buffer.writeUInt32LE(0x00000000);\n      yield buffer.data;\n    } else {\n      const buffer = new WritableTrackingBuffer(18);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      buffer.writeUInt32LE(0x00000000);\n      buffer.writeUInt32LE(0x00000000);\n      yield buffer.data;\n    }\n  },\n\n  validate: function(value): null | number {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    return value;\n  }\n};\n\nexport default Numeric;\nmodule.exports = Numeric;\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,OAAwI,GAAG;EAC/IC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EAEfC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,OAAO,UAAU,GAAI,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAE,GAAG,IAAI,GAAI,IAAI,CAACE,YAAY,CAACF,SAAS,CAAE,GAAG,GAAG;EACtG,CAAC;EAEDC,gBAAgB,EAAE,SAAAA,CAASD,SAAS,EAAE;IACpC,IAAIA,SAAS,CAACG,SAAS,IAAI,IAAI,EAAE;MAC/B,OAAOH,SAAS,CAACG,SAAS;IAC5B,CAAC,MAAM,IAAIH,SAAS,CAACI,KAAK,KAAK,IAAI,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;EAEDF,YAAY,EAAE,SAAAA,CAASF,SAAS,EAAE;IAChC,IAAIA,SAAS,CAACK,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOL,SAAS,CAACK,KAAK;IACxB,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;EAEDC,gBAAgBA,CAACN,SAAS,EAAE;IAC1B,IAAIG,SAAS;IACb,IAAIH,SAAS,CAACG,SAAS,IAAK,CAAC,EAAE;MAC7BA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIH,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrCA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIH,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrCA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACLA,SAAS,GAAG,IAAI;IAClB;IAEA,OAAOV,MAAM,CAACC,IAAI,CAAC,CAACa,iBAAQ,CAACX,EAAE,EAAEO,SAAS,EAAEH,SAAS,CAACG,SAAS,EAAGH,SAAS,CAACK,KAAK,CAAE,CAAC;EACtF,CAAC;EAEDG,uBAAuBA,CAACR,SAAS,EAAES,OAAO,EAAE;IAC1C,IAAIT,SAAS,CAACI,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOZ,WAAW;IACpB;IAEA,MAAMW,SAAS,GAAGH,SAAS,CAACG,SAAU;IACtC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM,IAAIS,SAAS,IAAI,EAAE,EAAE;MAC1B,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM,IAAIS,SAAS,IAAI,EAAE,EAAE;MAC1B,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,CAAEgB,qBAAqBA,CAACV,SAAS,EAAES,OAAO,EAAE;IAC1C,IAAIT,SAAS,CAACI,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMO,IAAI,GAAGX,SAAS,CAACI,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACxC,MAAMA,KAAK,GAAGQ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACd,SAAS,CAACI,KAAK,GAAGQ,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEf,SAAS,CAACK,KAAM,CAAC,CAAC,CAAC;IACpF,IAAIL,SAAS,CAACG,SAAS,IAAK,CAAC,EAAE;MAC7B,MAAMa,MAAM,GAAGvB,MAAM,CAACwB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACE,UAAU,CAACP,IAAI,EAAE,CAAC,CAAC;MAC1BK,MAAM,CAACG,aAAa,CAACf,KAAK,EAAE,CAAC,CAAC;MAC9B,MAAMY,MAAM;IACd,CAAC,MAAM,IAAIhB,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrC,MAAMa,MAAM,GAAG,IAAII,+BAAsB,CAAC,EAAE,CAAC;MAC7CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAACjB,KAAK,CAAC;MAC3B,MAAMY,MAAM,CAACM,IAAI;IACnB,CAAC,MAAM,IAAItB,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrC,MAAMa,MAAM,GAAG,IAAII,+BAAsB,CAAC,EAAE,CAAC;MAC7CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAACjB,KAAK,CAAC;MAC3BY,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChC,MAAMH,MAAM,CAACM,IAAI;IACnB,CAAC,MAAM;MACL,MAAMN,MAAM,GAAG,IAAII,+BAAsB,CAAC,EAAE,CAAC;MAC7CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAACjB,KAAK,CAAC;MAC3BY,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChCH,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChC,MAAMH,MAAM,CAACM,IAAI;IACnB;EACF,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAASnB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGoB,UAAU,CAACpB,KAAK,CAAC;IACzB,IAAIqB,KAAK,CAACrB,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIsB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOtB,KAAK;EACd;AACF,CAAC;AAAC,IAAAuB,QAAA,GAEahC,OAAO;AAAAiC,OAAA,CAAArC,OAAA,GAAAoC,QAAA;AACtBE,MAAM,CAACD,OAAO,GAAGjC,OAAO"}