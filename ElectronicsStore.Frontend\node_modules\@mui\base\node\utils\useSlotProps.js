"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSlotProps = useSlotProps;
var _utils = require("@mui/utils");
var _appendOwnerState = require("./appendOwnerState");
var _mergeSlotProps = require("./mergeSlotProps");
var _resolveComponentProps = require("./resolveComponentProps");
/**
 * @ignore - do not document.
 * Builds the props to be passed into the slot of an unstyled component.
 * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.
 * If the slot component is not a host component, it also merges in the `ownerState`.
 *
 * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.
 */
function useSlotProps(parameters) {
  var _parameters$additiona;
  const {
    elementType,
    externalSlotProps,
    ownerState,
    skipResolvingSlotProps = false,
    ...rest
  } = parameters;
  const resolvedComponentsProps = skipResolvingSlotProps ? {} : (0, _resolveComponentProps.resolveComponentProps)(externalSlotProps, ownerState);
  const {
    props: mergedProps,
    internalRef
  } = (0, _mergeSlotProps.mergeSlotProps)({
    ...rest,
    externalSlotProps: resolvedComponentsProps
  });
  const ref = (0, _utils.unstable_useForkRef)(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref);
  const props = (0, _appendOwnerState.appendOwnerState)(elementType, {
    ...mergedProps,
    ref
  }, ownerState);
  return props;
}