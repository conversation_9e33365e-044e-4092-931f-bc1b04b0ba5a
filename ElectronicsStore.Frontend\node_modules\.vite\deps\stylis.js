import {
  CHARSET,
  COMMENT,
  COUNTER_STYLE,
  DECLARATION,
  DOCUMENT,
  FONT_FACE,
  FONT_FEATURE_VALUES,
  IMPORT,
  KEYFRAMES,
  LAYER,
  MEDIA,
  MOZ,
  MS,
  NAMESPACE,
  PAGE,
  R<PERSON><PERSON>SET,
  SCOPE,
  SUPPORTS,
  VIEWPORT,
  WEBKIT,
  abs,
  alloc,
  append,
  assign,
  caret,
  char,
  character,
  characters,
  charat,
  column,
  combine,
  comment,
  commenter,
  compile,
  copy,
  dealloc,
  declaration,
  delimit,
  delimiter,
  escaping,
  filter,
  from,
  hash,
  identifier,
  indexof,
  length,
  lift,
  line,
  match,
  middleware,
  namespace,
  next,
  node,
  parse,
  peek,
  position,
  prefix,
  prefixer,
  prev,
  replace,
  ruleset,
  rulesheet,
  serialize,
  sizeof,
  slice,
  stringify,
  strlen,
  substr,
  token,
  tokenize,
  tokenizer,
  trim,
  whitespace
} from "./chunk-PYHZN3DE.js";
import "./chunk-HXA6O6EE.js";
export {
  CHARSET,
  COMMENT,
  COUNTER_STYLE,
  DECLARATION,
  DOCUMENT,
  FONT_FACE,
  FONT_FEATURE_VALUES,
  IMPORT,
  KEYFRAMES,
  LAYER,
  MEDIA,
  MOZ,
  MS,
  NAMESPACE,
  PAGE,
  RULESET,
  SCOPE,
  SUPPORTS,
  VIEWPORT,
  WEBKIT,
  abs,
  alloc,
  append,
  assign,
  caret,
  char,
  character,
  characters,
  charat,
  column,
  combine,
  comment,
  commenter,
  compile,
  copy,
  dealloc,
  declaration,
  delimit,
  delimiter,
  escaping,
  filter,
  from,
  hash,
  identifier,
  indexof,
  length,
  lift,
  line,
  match,
  middleware,
  namespace,
  next,
  node,
  parse,
  peek,
  position,
  prefix,
  prefixer,
  prev,
  replace,
  ruleset,
  rulesheet,
  serialize,
  sizeof,
  slice,
  stringify,
  strlen,
  substr,
  token,
  tokenize,
  tokenizer,
  trim,
  whitespace
};
//# sourceMappingURL=stylis.js.map
