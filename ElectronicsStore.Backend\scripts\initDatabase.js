const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');

// Create database directory if it doesn't exist
const dbDir = path.join(__dirname, '../database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(dbDir, 'store.db');

// Create database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  } else {
    console.log('✅ Connected to SQLite database');
    initializeDatabase();
  }
});

async function initializeDatabase() {
  try {
    console.log('🔄 Initializing database...');
    
    // Enable foreign keys
    db.run('PRAGMA foreign_keys = ON');
    
    // Create admin user
    const adminPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    
    db.run(`
      INSERT OR IGNORE INTO users (
        username, email, password_hash, full_name, role, status
      ) VALUES (?, ?, ?, ?, ?, ?)
    `, [
      'admin',
      '<EMAIL>',
      hashedPassword,
      'مدير النظام',
      'admin',
      'active'
    ], function(err) {
      if (err) {
        console.error('Error creating admin user:', err);
      } else {
        console.log('👤 Admin user created (username: admin, password: admin123)');
      }
    });
    
    // Insert sample categories
    const categories = [
      'هواتف ذكية',
      'لابتوب',
      'أجهزة لوحية',
      'إكسسوارات',
      'سماعات',
      'شواحن',
      'كابلات',
      'حافظات'
    ];
    
    categories.forEach(category => {
      db.run(`
        INSERT OR IGNORE INTO categories (name, description, status)
        VALUES (?, ?, ?)
      `, [category, `فئة ${category}`, 'active']);
    });
    
    // Insert sample brands
    const brands = [
      'Apple',
      'Samsung',
      'Huawei',
      'Xiaomi',
      'Dell',
      'HP',
      'Lenovo',
      'Asus',
      'Sony',
      'JBL',
      'Anker'
    ];
    
    brands.forEach(brand => {
      db.run(`
        INSERT OR IGNORE INTO brands (name, description, status)
        VALUES (?, ?, ?)
      `, [brand, `علامة ${brand} التجارية`, 'active']);
    });
    
    // Insert sample products
    const sampleProducts = [
      {
        name: 'iPhone 15 Pro',
        description: 'أحدث هاتف من آبل بمعالج A17 Pro',
        price: 4500,
        cost: 3800,
        stock: 25,
        min_stock: 5,
        category: 'هواتف ذكية',
        brand: 'Apple',
        barcode: '1234567890123',
        status: 'active'
      },
      {
        name: 'Samsung Galaxy S24',
        description: 'هاتف سامسونج الرائد بكاميرا متطورة',
        price: 3800,
        cost: 3200,
        stock: 15,
        min_stock: 5,
        category: 'هواتف ذكية',
        brand: 'Samsung',
        barcode: '1234567890124',
        status: 'active'
      },
      {
        name: 'MacBook Pro M3',
        description: 'لابتوب آبل بمعالج M3 للمحترفين',
        price: 8500,
        cost: 7200,
        stock: 8,
        min_stock: 3,
        category: 'لابتوب',
        brand: 'Apple',
        barcode: '1234567890125',
        status: 'active'
      },
      {
        name: 'AirPods Pro',
        description: 'سماعات آبل اللاسلكية مع إلغاء الضوضاء',
        price: 950,
        cost: 750,
        stock: 50,
        min_stock: 10,
        category: 'سماعات',
        brand: 'Apple',
        barcode: '1234567890126',
        status: 'active'
      },
      {
        name: 'Dell XPS 13',
        description: 'لابتوب ديل خفيف الوزن للأعمال',
        price: 4200,
        cost: 3500,
        stock: 12,
        min_stock: 5,
        category: 'لابتوب',
        brand: 'Dell',
        barcode: '1234567890127',
        status: 'active'
      },
      {
        name: 'شاحن Anker PowerPort',
        description: 'شاحن سريع متعدد المنافذ',
        price: 150,
        cost: 100,
        stock: 100,
        min_stock: 20,
        category: 'شواحن',
        brand: 'Anker',
        barcode: '1234567890128',
        status: 'active'
      },
      {
        name: 'كابل USB-C',
        description: 'كابل شحن ونقل بيانات عالي الجودة',
        price: 45,
        cost: 25,
        stock: 200,
        min_stock: 50,
        category: 'كابلات',
        brand: 'Anker',
        barcode: '1234567890129',
        status: 'active'
      },
      {
        name: 'حافظة iPhone 15',
        description: 'حافظة حماية شفافة مقاومة للصدمات',
        price: 85,
        cost: 45,
        stock: 75,
        min_stock: 15,
        category: 'حافظات',
        brand: 'Apple',
        barcode: '1234567890130',
        status: 'active'
      }
    ];
    
    sampleProducts.forEach(product => {
      db.run(`
        INSERT OR IGNORE INTO products (
          name, description, price, cost, stock, min_stock,
          category, brand, barcode, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        product.name, product.description, product.price, product.cost,
        product.stock, product.min_stock, product.category, product.brand,
        product.barcode, product.status
      ]);
    });
    
    // Insert sample customers
    const sampleCustomers = [
      {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        address: 'شارع الملك فهد، الرياض',
        city: 'الرياض',
        postal_code: '12345',
        customer_type: 'individual',
        status: 'active',
        credit_limit: 5000,
        join_date: '2024-01-15'
      },
      {
        name: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '+966502345678',
        address: 'شارع التحلية، جدة',
        city: 'جدة',
        postal_code: '23456',
        customer_type: 'individual',
        status: 'vip',
        credit_limit: 10000,
        join_date: '2023-12-10'
      },
      {
        name: 'محمد السعيد',
        email: '<EMAIL>',
        phone: '+966503456789',
        address: 'شارع العليا، الرياض',
        city: 'الرياض',
        postal_code: '11564',
        customer_type: 'business',
        business_name: 'شركة التقنية المتقدمة',
        tax_number: '300000000000003',
        status: 'active',
        credit_limit: 25000,
        join_date: '2024-02-01'
      }
    ];
    
    sampleCustomers.forEach(customer => {
      db.run(`
        INSERT OR IGNORE INTO customers (
          name, email, phone, address, city, postal_code,
          customer_type, business_name, tax_number, status,
          credit_limit, join_date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        customer.name, customer.email, customer.phone, customer.address,
        customer.city, customer.postal_code, customer.customer_type,
        customer.business_name || null, customer.tax_number || null,
        customer.status, customer.credit_limit, customer.join_date
      ]);
    });
    
    console.log('📦 Sample data inserted successfully');
    console.log('✅ Database initialization completed');
    console.log('\n🔑 Default Admin Credentials:');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('\n🚀 You can now start the server with: npm start');
    
  } catch (error) {
    console.error('❌ Error initializing database:', error);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
      } else {
        console.log('📊 Database connection closed');
      }
      process.exit(0);
    });
  }
}
