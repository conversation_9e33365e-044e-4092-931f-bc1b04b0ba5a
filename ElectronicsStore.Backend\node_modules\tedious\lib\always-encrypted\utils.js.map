{"version": 3, "file": "utils.js", "names": ["_types", "require", "shouldHonorAE", "stmtColumnEncryptionSetting", "columnEncryptionSetting", "SQLServerStatementColumnEncryptionSetting", "Disabled", "ResultSetOnly", "Enabled", "exports"], "sources": ["../../src/always-encrypted/utils.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { SQLServerStatementColumnEncryptionSetting } from './types';\n\nexport const shouldHonorAE = (stmtColumnEncryptionSetting: SQLServerStatementColumnEncryptionSetting, columnEncryptionSetting: boolean): boolean => {\n  switch (stmtColumnEncryptionSetting) {\n    case SQLServerStatementColumnEncryptionSetting.Disabled:\n    case SQLServerStatementColumnEncryptionSetting.ResultSetOnly:\n      return false;\n    case SQLServerStatementColumnEncryptionSetting.Enabled:\n      return true;\n    default:\n      return columnEncryptionSetting;\n  }\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAHA;AACA;;AAIO,MAAMC,aAAa,GAAGA,CAACC,2BAAsE,EAAEC,uBAAgC,KAAc;EAClJ,QAAQD,2BAA2B;IACjC,KAAKE,gDAAyC,CAACC,QAAQ;IACvD,KAAKD,gDAAyC,CAACE,aAAa;MAC1D,OAAO,KAAK;IACd,KAAKF,gDAAyC,CAACG,OAAO;MACpD,OAAO,IAAI;IACb;MACE,OAAOJ,uBAAuB;EAClC;AACF,CAAC;AAACK,OAAA,CAAAP,aAAA,GAAAA,aAAA"}