import { apiService, PaginatedResponse } from './apiClient';

// Sales types
export interface SalesInvoice {
  id: number;
  invoiceNumber: string;
  customerId?: number;
  customerName?: string;
  totalAmount: number;
  discountAmount: number;
  taxAmount: number;
  finalAmount: number;
  paymentMethod: 'cash' | 'card' | 'credit';
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: string;
  details: SalesInvoiceDetail[];
}

export interface SalesInvoiceDetail {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreateSalesInvoiceData {
  customerId?: number;
  customerName?: string;
  paymentMethod: 'cash' | 'card' | 'credit';
  discountAmount?: number;
  taxAmount?: number;
  details: Array<{
    productId: number;
    quantity: number;
    unitPrice: number;
  }>;
}

export interface SalesStatistics {
  totalSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  topSellingProducts: Array<{
    productId: number;
    productName: string;
    totalQuantity: number;
    totalRevenue: number;
  }>;
  salesByPaymentMethod: Array<{
    paymentMethod: string;
    count: number;
    totalAmount: number;
  }>;
  dailySales: Array<{
    date: string;
    salesCount: number;
    totalAmount: number;
  }>;
}

// Sales API endpoints
const ENDPOINTS = {
  SALES: '/sales',
  SALES_BY_ID: (id: number) => `/sales/${id}`,
  SALES_STATISTICS: '/sales/statistics',
  DAILY_SALES: '/sales/daily',
};

// Sales Service
export const salesService = {
  // Get all sales with pagination and filters
  getSales: async (params?: {
    pageNumber?: number;
    pageSize?: number;
    customerId?: number;
    paymentMethod?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<SalesInvoice>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.pageNumber) queryParams.append('pageNumber', params.pageNumber.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params?.customerId) queryParams.append('customerId', params.customerId.toString());
    if (params?.paymentMethod) queryParams.append('paymentMethod', params.paymentMethod);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortDirection) queryParams.append('sortDirection', params.sortDirection);

    const url = `${ENDPOINTS.SALES}?${queryParams.toString()}`;
    return await apiService.get<PaginatedResponse<SalesInvoice>>(url);
  },

  // Get sales invoice by ID
  getSalesInvoiceById: async (id: number): Promise<SalesInvoice> => {
    return await apiService.get<SalesInvoice>(ENDPOINTS.SALES_BY_ID(id));
  },

  // Create new sales invoice
  createSalesInvoice: async (salesData: CreateSalesInvoiceData): Promise<SalesInvoice> => {
    return await apiService.post<SalesInvoice>(ENDPOINTS.SALES, salesData);
  },

  // Update sales invoice status
  updateSalesInvoiceStatus: async (id: number, status: 'pending' | 'completed' | 'cancelled'): Promise<SalesInvoice> => {
    return await apiService.patch<SalesInvoice>(ENDPOINTS.SALES_BY_ID(id), { status });
  },

  // Delete sales invoice
  deleteSalesInvoice: async (id: number): Promise<void> => {
    return await apiService.delete<void>(ENDPOINTS.SALES_BY_ID(id));
  },

  // Get sales statistics
  getSalesStatistics: async (params?: {
    dateFrom?: string;
    dateTo?: string;
  }): Promise<SalesStatistics> => {
    const queryParams = new URLSearchParams();
    
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);

    const url = `${ENDPOINTS.SALES_STATISTICS}?${queryParams.toString()}`;
    return await apiService.get<SalesStatistics>(url);
  },

  // Get daily sales
  getDailySales: async (params?: {
    dateFrom?: string;
    dateTo?: string;
  }): Promise<Array<{
    date: string;
    salesCount: number;
    totalAmount: number;
  }>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);

    const url = `${ENDPOINTS.DAILY_SALES}?${queryParams.toString()}`;
    return await apiService.get(url);
  },

  // Print invoice
  printInvoice: async (id: number): Promise<Blob> => {
    return await apiService.get<Blob>(`/sales/${id}/print`, {
      responseType: 'blob',
    });
  },

  // Export sales data
  exportSales: async (params?: {
    dateFrom?: string;
    dateTo?: string;
    format?: 'csv' | 'excel';
  }): Promise<Blob> => {
    const queryParams = new URLSearchParams();
    
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);
    if (params?.format) queryParams.append('format', params.format);

    const url = `/sales/export?${queryParams.toString()}`;
    return await apiService.get<Blob>(url, {
      responseType: 'blob',
    });
  },
};

export default salesService;
