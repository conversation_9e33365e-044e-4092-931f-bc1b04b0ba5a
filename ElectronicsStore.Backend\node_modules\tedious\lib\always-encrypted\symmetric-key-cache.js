"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getKey = void 0;
var _symmetricKey = _interopRequireDefault(require("./symmetric-key"));
var _lruCache = _interopRequireDefault(require("lru-cache"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

const cache = new _lruCache.default(0);
const getKey = async (keyInfo, options) => {
  if (!options.trustedServerNameAE) {
    throw new Error('Server name should not be null in getKey');
  }
  const serverName = options.trustedServerNameAE;
  const keyLookupValue = `${serverName}:${Buffer.from(keyInfo.encryptedKey).toString('base64')}:${keyInfo.keyStoreName}`;
  if (cache.has(keyLookupValue)) {
    return cache.get(keyLookupValue);
  } else {
    const provider = options.encryptionKeyStoreProviders && options.encryptionKeyStoreProviders[keyInfo.keyStoreName];
    if (!provider) {
      throw new Error(`Failed to decrypt a column encryption key. Invalid key store provider name: ${keyInfo.keyStoreName}. A key store provider name must denote either a system key store provider or a registered custom key store provider. Valid (currently registered) custom key store provider names are: ${options.encryptionKeyStoreProviders}. Please verify key store provider information in column master key definitions in the database, and verify all custom key store providers used in your application are registered properly.`);
    }
    const plaintextKey = await provider.decryptColumnEncryptionKey(keyInfo.keyPath, keyInfo.algorithmName, keyInfo.encryptedKey);
    const encryptionKey = new _symmetricKey.default(plaintextKey);
    if (options.columnEncryptionKeyCacheTTL > 0) {
      cache.set(keyLookupValue, encryptionKey, options.columnEncryptionKeyCacheTTL);
    }
    return encryptionKey;
  }
};
exports.getKey = getKey;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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