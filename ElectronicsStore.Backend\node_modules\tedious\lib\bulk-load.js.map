{"version": 3, "file": "bulk-load.js", "names": ["_events", "require", "_writableTrackingBuffer", "_interopRequireDefault", "_stream", "_token", "obj", "__esModule", "default", "FLAGS", "nullable", "caseSen", "updateableReadWrite", "updateableUnknown", "identity", "computed", "fixedLenCLRType", "sparseColumnSet", "hidden", "key", "nullableUnknown", "DONE_STATUS", "FINAL", "MORE", "ERROR", "INXACT", "COUNT", "ATTN", "SRVERROR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "from", "TOKEN_TYPE", "ROW", "textPointerAndTimestampBuffer", "textP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RowTransform", "Transform", "constructor", "bulkLoad", "writableObjectMode", "mainOptions", "options", "columns", "columnMetadataWritten", "_transform", "row", "_encoding", "callback", "push", "getColMetaData", "i", "length", "c", "value", "Array", "isArray", "objName", "firstRow<PERSON><PERSON>ten", "type", "validate", "collation", "error", "parameter", "scale", "precision", "name", "generateParameterLength", "chunk", "generateParameterData", "process", "nextTick", "_flush", "createDoneToken", "BulkLoad", "EventEmitter", "table", "connectionOptions", "checkConstraints", "fireTriggers", "keepNulls", "lockTable", "order", "TypeError", "column", "direction", "Object", "entries", "undefined", "canceled", "executionStarted", "columnsByName", "streamingMode", "rowToPacketTransform", "bulkOptions", "addColumn", "output", "Error", "id", "<PERSON><PERSON><PERSON><PERSON>", "resolvePrecision", "resolveScale", "getOptionsSql", "addOptions", "orderColumns", "join", "getBulkInsertSql", "sql", "len", "declaration", "getTableCreationSql", "tBuf", "WritableTrackingBuffer", "writeUInt8", "COLMETADATA", "writeUInt16LE", "j", "tdsVersion", "writeUInt32LE", "flags", "writeBuffer", "generateTypeInfo", "hasTableName", "writeUsVarchar", "writeBVarchar", "data", "setTimeout", "timeout", "DONE", "status", "cancel", "emit", "_default", "exports", "module"], "sources": ["../src/bulk-load.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport WritableTrackingBuffer from './tracking-buffer/writable-tracking-buffer';\nimport Connection, { type InternalConnectionOptions } from './connection';\n\nimport { Transform } from 'stream';\nimport { TYPE as TOKEN_TYPE } from './token/token';\n\nimport { type DataType, type Parameter } from './data-type';\nimport { Collation } from './collation';\n\n/**\n * @private\n */\nconst FLAGS = {\n  nullable: 1 << 0,\n  caseSen: 1 << 1,\n  updateableReadWrite: 1 << 2,\n  updateableUnknown: 1 << 3,\n  identity: 1 << 4,\n  computed: 1 << 5, // introduced in TDS 7.2\n  fixedLenCLRType: 1 << 8, // introduced in TDS 7.2\n  sparseColumnSet: 1 << 10, // introduced in TDS 7.3.B\n  hidden: 1 << 13, // introduced in TDS 7.2\n  key: 1 << 14, // introduced in TDS 7.2\n  nullableUnknown: 1 << 15 // introduced in TDS 7.2\n};\n\n/**\n * @private\n */\nconst DONE_STATUS = {\n  FINAL: 0x00,\n  MORE: 0x1,\n  ERROR: 0x2,\n  INXACT: 0x4,\n  COUNT: 0x10,\n  ATTN: 0x20,\n  SRVERROR: 0x100\n};\n\n/**\n * @private\n */\ninterface InternalOptions {\n  checkConstraints: boolean;\n  fireTriggers: boolean;\n  keepNulls: boolean;\n  lockTable: boolean;\n  order: { [columnName: string]: 'ASC' | 'DESC' };\n}\n\nexport interface Options {\n  /**\n   * Honors constraints during bulk load, using T-SQL\n   * [CHECK_CONSTRAINTS](https://technet.microsoft.com/en-us/library/ms186247(v=sql.105).aspx).\n   * (default: `false`)\n   */\n  checkConstraints?: InternalOptions['checkConstraints'] | undefined;\n\n  /**\n   * Honors insert triggers during bulk load, using the T-SQL [FIRE_TRIGGERS](https://technet.microsoft.com/en-us/library/ms187640(v=sql.105).aspx). (default: `false`)\n   */\n  fireTriggers?: InternalOptions['fireTriggers'] | undefined;\n\n  /**\n   * Honors null value passed, ignores the default values set on table, using T-SQL [KEEP_NULLS](https://msdn.microsoft.com/en-us/library/ms187887(v=sql.120).aspx). (default: `false`)\n   */\n  keepNulls?: InternalOptions['keepNulls'] | undefined;\n\n  /**\n   * Places a bulk update(BU) lock on table while performing bulk load, using T-SQL [TABLOCK](https://technet.microsoft.com/en-us/library/ms180876(v=sql.105).aspx). (default: `false`)\n   */\n  lockTable?: InternalOptions['lockTable'] | undefined;\n\n  /**\n   * Specifies the ordering of the data to possibly increase bulk insert performance, using T-SQL [ORDER](https://docs.microsoft.com/en-us/previous-versions/sql/sql-server-2008-r2/ms177468(v=sql.105)). (default: `{}`)\n   */\n  order?: InternalOptions['order'] | undefined;\n}\n\n\nexport type Callback =\n  /**\n   * A function which will be called after the [[BulkLoad]] finishes executing.\n   *\n   * @param rowCount the number of rows inserted\n   */\n  (err: Error | undefined | null, rowCount?: number) => void;\n\ninterface Column extends Parameter {\n  objName: string;\n  collation: Collation | undefined;\n}\n\ninterface ColumnOptions {\n  output?: boolean;\n\n  /**\n   * For VarChar, NVarChar, VarBinary. Use length as `Infinity` for VarChar(max), NVarChar(max) and VarBinary(max).\n   */\n  length?: number;\n\n  /**\n   * For Numeric, Decimal.\n   */\n  precision?: number;\n\n  /**\n   * For Numeric, Decimal, Time, DateTime2, DateTimeOffset.\n   */\n  scale?: number;\n\n  /**\n   * If the name of the column is different from the name of the property found on `rowObj` arguments passed to [[addRow]], then you can use this option to specify the property name.\n   */\n  objName?: string;\n\n  /**\n   * Indicates whether the column accepts NULL values.\n   */\n  nullable?: boolean;\n}\n\nconst rowTokenBuffer = Buffer.from([ TOKEN_TYPE.ROW ]);\nconst textPointerAndTimestampBuffer = Buffer.from([\n  // TextPointer length\n  0x10,\n\n  // TextPointer\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n\n  // Timestamp\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n]);\nconst textPointerNullBuffer = Buffer.from([0x00]);\n\n// A transform that converts rows to packets.\nclass RowTransform extends Transform {\n  /**\n   * @private\n   */\n  declare columnMetadataWritten: boolean;\n  /**\n   * @private\n   */\n  declare bulkLoad: BulkLoad;\n  /**\n   * @private\n   */\n  declare mainOptions: BulkLoad['options'];\n  /**\n   * @private\n   */\n  declare columns: BulkLoad['columns'];\n\n  /**\n   * @private\n   */\n  constructor(bulkLoad: BulkLoad) {\n    super({ writableObjectMode: true });\n\n    this.bulkLoad = bulkLoad;\n    this.mainOptions = bulkLoad.options;\n    this.columns = bulkLoad.columns;\n\n    this.columnMetadataWritten = false;\n  }\n\n  /**\n   * @private\n   */\n  _transform(row: Array<unknown> | { [colName: string]: unknown }, _encoding: string, callback: (error?: Error) => void) {\n    if (!this.columnMetadataWritten) {\n      this.push(this.bulkLoad.getColMetaData());\n      this.columnMetadataWritten = true;\n    }\n\n    this.push(rowTokenBuffer);\n\n    for (let i = 0; i < this.columns.length; i++) {\n      const c = this.columns[i];\n      let value = Array.isArray(row) ? row[i] : row[c.objName];\n\n      if (!this.bulkLoad.firstRowWritten) {\n        try {\n          value = c.type.validate(value, c.collation);\n        } catch (error: any) {\n          return callback(error);\n        }\n      }\n\n      const parameter = {\n        length: c.length,\n        scale: c.scale,\n        precision: c.precision,\n        value: value\n      };\n\n      if (c.type.name === 'Text' || c.type.name === 'Image' || c.type.name === 'NText') {\n        if (value == null) {\n          this.push(textPointerNullBuffer);\n          continue;\n        }\n\n        this.push(textPointerAndTimestampBuffer);\n      }\n\n      this.push(c.type.generateParameterLength(parameter, this.mainOptions));\n      for (const chunk of c.type.generateParameterData(parameter, this.mainOptions)) {\n        this.push(chunk);\n      }\n    }\n\n    process.nextTick(callback);\n  }\n\n  /**\n   * @private\n   */\n  _flush(callback: () => void) {\n    this.push(this.bulkLoad.createDoneToken());\n\n    process.nextTick(callback);\n  }\n}\n\n/**\n * A BulkLoad instance is used to perform a bulk insert.\n *\n * Use [[Connection.newBulkLoad]] to create a new instance, and [[Connection.execBulkLoad]] to execute it.\n *\n * Example of BulkLoad Usages:\n *\n * ```js\n * // optional BulkLoad options\n * const options = { keepNulls: true };\n *\n * // instantiate - provide the table where you'll be inserting to, options and a callback\n * const bulkLoad = connection.newBulkLoad('MyTable', options, (error, rowCount) => {\n *   console.log('inserted %d rows', rowCount);\n * });\n *\n * // setup your columns - always indicate whether the column is nullable\n * bulkLoad.addColumn('myInt', TYPES.Int, { nullable: false });\n * bulkLoad.addColumn('myString', TYPES.NVarChar, { length: 50, nullable: true });\n *\n * // execute\n * connection.execBulkLoad(bulkLoad, [\n *   { myInt: 7, myString: 'hello' },\n *   { myInt: 23, myString: 'world' }\n * ]);\n * ```\n */\nclass BulkLoad extends EventEmitter {\n  /**\n   * @private\n   */\n  declare error: Error | undefined;\n  /**\n   * @private\n   */\n  declare canceled: boolean;\n  /**\n   * @private\n   */\n  declare executionStarted: boolean;\n  /**\n   * @private\n   */\n  declare streamingMode: boolean;\n  /**\n   * @private\n   */\n  declare table: string;\n  /**\n   * @private\n   */\n  declare timeout: number | undefined;\n\n  /**\n   * @private\n   */\n  declare options: InternalConnectionOptions;\n  /**\n   * @private\n   */\n  declare callback: Callback;\n\n  /**\n   * @private\n   */\n  declare columns: Array<Column>;\n  /**\n   * @private\n   */\n  declare columnsByName: { [name: string]: Column };\n\n  /**\n   * @private\n   */\n  declare firstRowWritten: boolean;\n  /**\n   * @private\n   */\n  declare rowToPacketTransform: RowTransform;\n\n  /**\n   * @private\n   */\n  declare bulkOptions: InternalOptions;\n\n  /**\n   * @private\n   */\n  declare connection: Connection | undefined;\n  /**\n   * @private\n   */\n  declare rows: Array<any> | undefined;\n  /**\n   * @private\n   */\n  declare rst: Array<any> | undefined;\n  /**\n   * @private\n   */\n  declare rowCount: number | undefined;\n\n  declare collation: Collation | undefined;\n\n  /**\n   * @private\n   */\n  constructor(table: string, collation: Collation | undefined, connectionOptions: InternalConnectionOptions, {\n    checkConstraints = false,\n    fireTriggers = false,\n    keepNulls = false,\n    lockTable = false,\n    order = {},\n  }: Options, callback: Callback) {\n    if (typeof checkConstraints !== 'boolean') {\n      throw new TypeError('The \"options.checkConstraints\" property must be of type boolean.');\n    }\n\n    if (typeof fireTriggers !== 'boolean') {\n      throw new TypeError('The \"options.fireTriggers\" property must be of type boolean.');\n    }\n\n    if (typeof keepNulls !== 'boolean') {\n      throw new TypeError('The \"options.keepNulls\" property must be of type boolean.');\n    }\n\n    if (typeof lockTable !== 'boolean') {\n      throw new TypeError('The \"options.lockTable\" property must be of type boolean.');\n    }\n\n    if (typeof order !== 'object' || order === null) {\n      throw new TypeError('The \"options.order\" property must be of type object.');\n    }\n\n    for (const [column, direction] of Object.entries(order)) {\n      if (direction !== 'ASC' && direction !== 'DESC') {\n        throw new TypeError('The value of the \"' + column + '\" key in the \"options.order\" object must be either \"ASC\" or \"DESC\".');\n      }\n    }\n\n    super();\n\n    this.error = undefined;\n    this.canceled = false;\n    this.executionStarted = false;\n\n    this.collation = collation;\n\n    this.table = table;\n    this.options = connectionOptions;\n    this.callback = callback;\n    this.columns = [];\n    this.columnsByName = {};\n    this.firstRowWritten = false;\n    this.streamingMode = false;\n\n    this.rowToPacketTransform = new RowTransform(this); // eslint-disable-line no-use-before-define\n\n    this.bulkOptions = { checkConstraints, fireTriggers, keepNulls, lockTable, order };\n  }\n\n  /**\n   * Adds a column to the bulk load.\n   *\n   * The column definitions should match the table you are trying to insert into.\n   * Attempting to call addColumn after the first row has been added will throw an exception.\n   *\n   * ```js\n   * bulkLoad.addColumn('MyIntColumn', TYPES.Int, { nullable: false });\n   * ```\n   *\n   * @param name The name of the column.\n   * @param type One of the supported `data types`.\n   * @param __namedParameters Additional column type information. At a minimum, `nullable` must be set to true or false.\n   * @param length For VarChar, NVarChar, VarBinary. Use length as `Infinity` for VarChar(max), NVarChar(max) and VarBinary(max).\n   * @param nullable Indicates whether the column accepts NULL values.\n   * @param objName If the name of the column is different from the name of the property found on `rowObj` arguments passed to [[addRow]] or [[Connection.execBulkLoad]], then you can use this option to specify the property name.\n   * @param precision For Numeric, Decimal.\n   * @param scale For Numeric, Decimal, Time, DateTime2, DateTimeOffset.\n  */\n  addColumn(name: string, type: DataType, { output = false, length, precision, scale, objName = name, nullable = true }: ColumnOptions) {\n    if (this.firstRowWritten) {\n      throw new Error('Columns cannot be added to bulk insert after the first row has been written.');\n    }\n    if (this.executionStarted) {\n      throw new Error('Columns cannot be added to bulk insert after execution has started.');\n    }\n\n    const column: Column = {\n      type: type,\n      name: name,\n      value: null,\n      output: output,\n      length: length,\n      precision: precision,\n      scale: scale,\n      objName: objName,\n      nullable: nullable,\n      collation: this.collation\n    };\n\n    if ((type.id & 0x30) === 0x20) {\n      if (column.length == null && type.resolveLength) {\n        column.length = type.resolveLength(column);\n      }\n    }\n\n    if (type.resolvePrecision && column.precision == null) {\n      column.precision = type.resolvePrecision(column);\n    }\n\n    if (type.resolveScale && column.scale == null) {\n      column.scale = type.resolveScale(column);\n    }\n\n    this.columns.push(column);\n\n    this.columnsByName[name] = column;\n  }\n\n  /**\n   * @private\n   */\n  getOptionsSql() {\n    const addOptions = [];\n\n    if (this.bulkOptions.checkConstraints) {\n      addOptions.push('CHECK_CONSTRAINTS');\n    }\n\n    if (this.bulkOptions.fireTriggers) {\n      addOptions.push('FIRE_TRIGGERS');\n    }\n\n    if (this.bulkOptions.keepNulls) {\n      addOptions.push('KEEP_NULLS');\n    }\n\n    if (this.bulkOptions.lockTable) {\n      addOptions.push('TABLOCK');\n    }\n\n    if (this.bulkOptions.order) {\n      const orderColumns = [];\n\n      for (const [column, direction] of Object.entries(this.bulkOptions.order)) {\n        orderColumns.push(`${column} ${direction}`);\n      }\n\n      if (orderColumns.length) {\n        addOptions.push(`ORDER (${orderColumns.join(', ')})`);\n      }\n    }\n\n    if (addOptions.length > 0) {\n      return ` WITH (${addOptions.join(',')})`;\n    } else {\n      return '';\n    }\n  }\n\n  /**\n   * @private\n   */\n  getBulkInsertSql() {\n    let sql = 'insert bulk ' + this.table + '(';\n    for (let i = 0, len = this.columns.length; i < len; i++) {\n      const c = this.columns[i];\n      if (i !== 0) {\n        sql += ', ';\n      }\n      sql += '[' + c.name + '] ' + (c.type.declaration(c));\n    }\n    sql += ')';\n\n    sql += this.getOptionsSql();\n    return sql;\n  }\n\n  /**\n   * This is simply a helper utility function which returns a `CREATE TABLE SQL` statement based on the columns added to the bulkLoad object.\n   * This may be particularly handy when you want to insert into a temporary table (a table which starts with `#`).\n   *\n   * ```js\n   * var sql = bulkLoad.getTableCreationSql();\n   * ```\n   *\n   * A side note on bulk inserting into temporary tables: if you want to access a local temporary table after executing the bulk load,\n   * you'll need to use the same connection and execute your requests using [[Connection.execSqlBatch]] instead of [[Connection.execSql]]\n   */\n  getTableCreationSql() {\n    let sql = 'CREATE TABLE ' + this.table + '(\\n';\n    for (let i = 0, len = this.columns.length; i < len; i++) {\n      const c = this.columns[i];\n      if (i !== 0) {\n        sql += ',\\n';\n      }\n      sql += '[' + c.name + '] ' + (c.type.declaration(c));\n      if (c.nullable !== undefined) {\n        sql += ' ' + (c.nullable ? 'NULL' : 'NOT NULL');\n      }\n    }\n    sql += '\\n)';\n    return sql;\n  }\n\n  /**\n   * @private\n   */\n  getColMetaData() {\n    const tBuf = new WritableTrackingBuffer(100, null, true);\n    // TokenType\n    tBuf.writeUInt8(TOKEN_TYPE.COLMETADATA);\n    // Count\n    tBuf.writeUInt16LE(this.columns.length);\n\n    for (let j = 0, len = this.columns.length; j < len; j++) {\n      const c = this.columns[j];\n      // UserType\n      if (this.options.tdsVersion < '7_2') {\n        tBuf.writeUInt16LE(0);\n      } else {\n        tBuf.writeUInt32LE(0);\n      }\n\n      // Flags\n      let flags = FLAGS.updateableReadWrite;\n      if (c.nullable) {\n        flags |= FLAGS.nullable;\n      } else if (c.nullable === undefined && this.options.tdsVersion >= '7_2') {\n        flags |= FLAGS.nullableUnknown;\n      }\n      tBuf.writeUInt16LE(flags);\n\n      // TYPE_INFO\n      tBuf.writeBuffer(c.type.generateTypeInfo(c, this.options));\n\n      // TableName\n      if (c.type.hasTableName) {\n        tBuf.writeUsVarchar(this.table, 'ucs2');\n      }\n\n      // ColName\n      tBuf.writeBVarchar(c.name, 'ucs2');\n    }\n    return tBuf.data;\n  }\n\n  /**\n   * Sets a timeout for this bulk load.\n   *\n   * ```js\n   * bulkLoad.setTimeout(timeout);\n   * ```\n   *\n   * @param timeout The number of milliseconds before the bulk load is considered failed, or 0 for no timeout.\n   *   When no timeout is set for the bulk load, the [[ConnectionOptions.requestTimeout]] of the Connection is used.\n   */\n  setTimeout(timeout?: number) {\n    this.timeout = timeout;\n  }\n\n  /**\n   * @private\n   */\n  createDoneToken() {\n    // It might be nice to make DoneToken a class if anything needs to create them, but for now, just do it here\n    const tBuf = new WritableTrackingBuffer(this.options.tdsVersion < '7_2' ? 9 : 13);\n    tBuf.writeUInt8(TOKEN_TYPE.DONE);\n    const status = DONE_STATUS.FINAL;\n    tBuf.writeUInt16LE(status);\n    tBuf.writeUInt16LE(0); // CurCmd (TDS ignores this)\n    tBuf.writeUInt32LE(0); // row count - doesn't really matter\n    if (this.options.tdsVersion >= '7_2') {\n      tBuf.writeUInt32LE(0); // row count is 64 bits in >= TDS 7.2\n    }\n    return tBuf.data;\n  }\n\n  /**\n   * @private\n   */\n  cancel() {\n    if (this.canceled) {\n      return;\n    }\n\n    this.canceled = true;\n    this.emit('cancel');\n  }\n}\n\nexport default BulkLoad;\nmodule.exports = BulkLoad;\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAGA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAmD,SAAAE,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAKnD;AACA;AACA;AACA,MAAMG,KAAK,GAAG;EACZC,QAAQ,EAAE,CAAC,IAAI,CAAC;EAChBC,OAAO,EAAE,CAAC,IAAI,CAAC;EACfC,mBAAmB,EAAE,CAAC,IAAI,CAAC;EAC3BC,iBAAiB,EAAE,CAAC,IAAI,CAAC;EACzBC,QAAQ,EAAE,CAAC,IAAI,CAAC;EAChBC,QAAQ,EAAE,CAAC,IAAI,CAAC;EAAE;EAClBC,eAAe,EAAE,CAAC,IAAI,CAAC;EAAE;EACzBC,eAAe,EAAE,CAAC,IAAI,EAAE;EAAE;EAC1BC,MAAM,EAAE,CAAC,IAAI,EAAE;EAAE;EACjBC,GAAG,EAAE,CAAC,IAAI,EAAE;EAAE;EACdC,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC;AAC3B,CAAC;;AAED;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,GAAG;EACTC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;;AAiFA,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAEC,WAAU,CAACC,GAAG,CAAE,CAAC;AACtD,MAAMC,6BAA6B,GAAGJ,MAAM,CAACC,IAAI,CAAC;AAChD;AACA,IAAI;AAEJ;AACA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAE9F;AACA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAC/C,CAAC;AACF,MAAMI,qBAAqB,GAAGL,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;;AAEjD;AACA,MAAMK,YAAY,SAASC,iBAAS,CAAC;EACnC;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;EACEC,WAAWA,CAACC,QAAkB,EAAE;IAC9B,KAAK,CAAC;MAAEC,kBAAkB,EAAE;IAAK,CAAC,CAAC;IAEnC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,WAAW,GAAGF,QAAQ,CAACG,OAAO;IACnC,IAAI,CAACC,OAAO,GAAGJ,QAAQ,CAACI,OAAO;IAE/B,IAAI,CAACC,qBAAqB,GAAG,KAAK;EACpC;;EAEA;AACF;AACA;EACEC,UAAUA,CAACC,GAAoD,EAAEC,SAAiB,EAAEC,QAAiC,EAAE;IACrH,IAAI,CAAC,IAAI,CAACJ,qBAAqB,EAAE;MAC/B,IAAI,CAACK,IAAI,CAAC,IAAI,CAACV,QAAQ,CAACW,cAAc,CAAC,CAAC,CAAC;MACzC,IAAI,CAACN,qBAAqB,GAAG,IAAI;IACnC;IAEA,IAAI,CAACK,IAAI,CAACpB,cAAc,CAAC;IAEzB,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,OAAO,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,MAAME,CAAC,GAAG,IAAI,CAACV,OAAO,CAACQ,CAAC,CAAC;MACzB,IAAIG,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,GAAGA,GAAG,CAACK,CAAC,CAAC,GAAGL,GAAG,CAACO,CAAC,CAACI,OAAO,CAAC;MAExD,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACmB,eAAe,EAAE;QAClC,IAAI;UACFJ,KAAK,GAAGD,CAAC,CAACM,IAAI,CAACC,QAAQ,CAACN,KAAK,EAAED,CAAC,CAACQ,SAAS,CAAC;QAC7C,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnB,OAAOd,QAAQ,CAACc,KAAK,CAAC;QACxB;MACF;MAEA,MAAMC,SAAS,GAAG;QAChBX,MAAM,EAAEC,CAAC,CAACD,MAAM;QAChBY,KAAK,EAAEX,CAAC,CAACW,KAAK;QACdC,SAAS,EAAEZ,CAAC,CAACY,SAAS;QACtBX,KAAK,EAAEA;MACT,CAAC;MAED,IAAID,CAAC,CAACM,IAAI,CAACO,IAAI,KAAK,MAAM,IAAIb,CAAC,CAACM,IAAI,CAACO,IAAI,KAAK,OAAO,IAAIb,CAAC,CAACM,IAAI,CAACO,IAAI,KAAK,OAAO,EAAE;QAChF,IAAIZ,KAAK,IAAI,IAAI,EAAE;UACjB,IAAI,CAACL,IAAI,CAACd,qBAAqB,CAAC;UAChC;QACF;QAEA,IAAI,CAACc,IAAI,CAACf,6BAA6B,CAAC;MAC1C;MAEA,IAAI,CAACe,IAAI,CAACI,CAAC,CAACM,IAAI,CAACQ,uBAAuB,CAACJ,SAAS,EAAE,IAAI,CAACtB,WAAW,CAAC,CAAC;MACtE,KAAK,MAAM2B,KAAK,IAAIf,CAAC,CAACM,IAAI,CAACU,qBAAqB,CAACN,SAAS,EAAE,IAAI,CAACtB,WAAW,CAAC,EAAE;QAC7E,IAAI,CAACQ,IAAI,CAACmB,KAAK,CAAC;MAClB;IACF;IAEAE,OAAO,CAACC,QAAQ,CAACvB,QAAQ,CAAC;EAC5B;;EAEA;AACF;AACA;EACEwB,MAAMA,CAACxB,QAAoB,EAAE;IAC3B,IAAI,CAACC,IAAI,CAAC,IAAI,CAACV,QAAQ,CAACkC,eAAe,CAAC,CAAC,CAAC;IAE1CH,OAAO,CAACC,QAAQ,CAACvB,QAAQ,CAAC;EAC5B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,QAAQ,SAASC,oBAAY,CAAC;EAClC;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAKE;AACF;AACA;EACErC,WAAWA,CAACsC,KAAa,EAAEf,SAAgC,EAAEgB,iBAA4C,EAAE;IACzGC,gBAAgB,GAAG,KAAK;IACxBC,YAAY,GAAG,KAAK;IACpBC,SAAS,GAAG,KAAK;IACjBC,SAAS,GAAG,KAAK;IACjBC,KAAK,GAAG,CAAC;EACF,CAAC,EAAElC,QAAkB,EAAE;IAC9B,IAAI,OAAO8B,gBAAgB,KAAK,SAAS,EAAE;MACzC,MAAM,IAAIK,SAAS,CAAC,kEAAkE,CAAC;IACzF;IAEA,IAAI,OAAOJ,YAAY,KAAK,SAAS,EAAE;MACrC,MAAM,IAAII,SAAS,CAAC,8DAA8D,CAAC;IACrF;IAEA,IAAI,OAAOH,SAAS,KAAK,SAAS,EAAE;MAClC,MAAM,IAAIG,SAAS,CAAC,2DAA2D,CAAC;IAClF;IAEA,IAAI,OAAOF,SAAS,KAAK,SAAS,EAAE;MAClC,MAAM,IAAIE,SAAS,CAAC,2DAA2D,CAAC;IAClF;IAEA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C,MAAM,IAAIC,SAAS,CAAC,sDAAsD,CAAC;IAC7E;IAEA,KAAK,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE;MACvD,IAAIG,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;QAC/C,MAAM,IAAIF,SAAS,CAAC,oBAAoB,GAAGC,MAAM,GAAG,qEAAqE,CAAC;MAC5H;IACF;IAEA,KAAK,CAAC,CAAC;IAEP,IAAI,CAACtB,KAAK,GAAG0B,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAE7B,IAAI,CAAC7B,SAAS,GAAGA,SAAS;IAE1B,IAAI,CAACe,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClC,OAAO,GAAGmC,iBAAiB;IAChC,IAAI,CAAC7B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACL,OAAO,GAAG,EAAE;IACjB,IAAI,CAACgD,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACjC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACkC,aAAa,GAAG,KAAK;IAE1B,IAAI,CAACC,oBAAoB,GAAG,IAAIzD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEpD,IAAI,CAAC0D,WAAW,GAAG;MAAEhB,gBAAgB;MAAEC,YAAY;MAAEC,SAAS;MAAEC,SAAS;MAAEC;IAAM,CAAC;EACpF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEa,SAASA,CAAC7B,IAAY,EAAEP,IAAc,EAAE;IAAEqC,MAAM,GAAG,KAAK;IAAE5C,MAAM;IAAEa,SAAS;IAAED,KAAK;IAAEP,OAAO,GAAGS,IAAI;IAAExD,QAAQ,GAAG;EAAoB,CAAC,EAAE;IACpI,IAAI,IAAI,CAACgD,eAAe,EAAE;MACxB,MAAM,IAAIuC,KAAK,CAAC,8EAA8E,CAAC;IACjG;IACA,IAAI,IAAI,CAACP,gBAAgB,EAAE;MACzB,MAAM,IAAIO,KAAK,CAAC,qEAAqE,CAAC;IACxF;IAEA,MAAMb,MAAc,GAAG;MACrBzB,IAAI,EAAEA,IAAI;MACVO,IAAI,EAAEA,IAAI;MACVZ,KAAK,EAAE,IAAI;MACX0C,MAAM,EAAEA,MAAM;MACd5C,MAAM,EAAEA,MAAM;MACda,SAAS,EAAEA,SAAS;MACpBD,KAAK,EAAEA,KAAK;MACZP,OAAO,EAAEA,OAAO;MAChB/C,QAAQ,EAAEA,QAAQ;MAClBmD,SAAS,EAAE,IAAI,CAACA;IAClB,CAAC;IAED,IAAI,CAACF,IAAI,CAACuC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;MAC7B,IAAId,MAAM,CAAChC,MAAM,IAAI,IAAI,IAAIO,IAAI,CAACwC,aAAa,EAAE;QAC/Cf,MAAM,CAAChC,MAAM,GAAGO,IAAI,CAACwC,aAAa,CAACf,MAAM,CAAC;MAC5C;IACF;IAEA,IAAIzB,IAAI,CAACyC,gBAAgB,IAAIhB,MAAM,CAACnB,SAAS,IAAI,IAAI,EAAE;MACrDmB,MAAM,CAACnB,SAAS,GAAGN,IAAI,CAACyC,gBAAgB,CAAChB,MAAM,CAAC;IAClD;IAEA,IAAIzB,IAAI,CAAC0C,YAAY,IAAIjB,MAAM,CAACpB,KAAK,IAAI,IAAI,EAAE;MAC7CoB,MAAM,CAACpB,KAAK,GAAGL,IAAI,CAAC0C,YAAY,CAACjB,MAAM,CAAC;IAC1C;IAEA,IAAI,CAACzC,OAAO,CAACM,IAAI,CAACmC,MAAM,CAAC;IAEzB,IAAI,CAACO,aAAa,CAACzB,IAAI,CAAC,GAAGkB,MAAM;EACnC;;EAEA;AACF;AACA;EACEkB,aAAaA,CAAA,EAAG;IACd,MAAMC,UAAU,GAAG,EAAE;IAErB,IAAI,IAAI,CAACT,WAAW,CAAChB,gBAAgB,EAAE;MACrCyB,UAAU,CAACtD,IAAI,CAAC,mBAAmB,CAAC;IACtC;IAEA,IAAI,IAAI,CAAC6C,WAAW,CAACf,YAAY,EAAE;MACjCwB,UAAU,CAACtD,IAAI,CAAC,eAAe,CAAC;IAClC;IAEA,IAAI,IAAI,CAAC6C,WAAW,CAACd,SAAS,EAAE;MAC9BuB,UAAU,CAACtD,IAAI,CAAC,YAAY,CAAC;IAC/B;IAEA,IAAI,IAAI,CAAC6C,WAAW,CAACb,SAAS,EAAE;MAC9BsB,UAAU,CAACtD,IAAI,CAAC,SAAS,CAAC;IAC5B;IAEA,IAAI,IAAI,CAAC6C,WAAW,CAACZ,KAAK,EAAE;MAC1B,MAAMsB,YAAY,GAAG,EAAE;MAEvB,KAAK,MAAM,CAACpB,MAAM,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACO,WAAW,CAACZ,KAAK,CAAC,EAAE;QACxEsB,YAAY,CAACvD,IAAI,CAAE,GAAEmC,MAAO,IAAGC,SAAU,EAAC,CAAC;MAC7C;MAEA,IAAImB,YAAY,CAACpD,MAAM,EAAE;QACvBmD,UAAU,CAACtD,IAAI,CAAE,UAASuD,YAAY,CAACC,IAAI,CAAC,IAAI,CAAE,GAAE,CAAC;MACvD;IACF;IAEA,IAAIF,UAAU,CAACnD,MAAM,GAAG,CAAC,EAAE;MACzB,OAAQ,UAASmD,UAAU,CAACE,IAAI,CAAC,GAAG,CAAE,GAAE;IAC1C,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;EACEC,gBAAgBA,CAAA,EAAG;IACjB,IAAIC,GAAG,GAAG,cAAc,GAAG,IAAI,CAAC/B,KAAK,GAAG,GAAG;IAC3C,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEyD,GAAG,GAAG,IAAI,CAACjE,OAAO,CAACS,MAAM,EAAED,CAAC,GAAGyD,GAAG,EAAEzD,CAAC,EAAE,EAAE;MACvD,MAAME,CAAC,GAAG,IAAI,CAACV,OAAO,CAACQ,CAAC,CAAC;MACzB,IAAIA,CAAC,KAAK,CAAC,EAAE;QACXwD,GAAG,IAAI,IAAI;MACb;MACAA,GAAG,IAAI,GAAG,GAAGtD,CAAC,CAACa,IAAI,GAAG,IAAI,GAAIb,CAAC,CAACM,IAAI,CAACkD,WAAW,CAACxD,CAAC,CAAE;IACtD;IACAsD,GAAG,IAAI,GAAG;IAEVA,GAAG,IAAI,IAAI,CAACL,aAAa,CAAC,CAAC;IAC3B,OAAOK,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,mBAAmBA,CAAA,EAAG;IACpB,IAAIH,GAAG,GAAG,eAAe,GAAG,IAAI,CAAC/B,KAAK,GAAG,KAAK;IAC9C,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEyD,GAAG,GAAG,IAAI,CAACjE,OAAO,CAACS,MAAM,EAAED,CAAC,GAAGyD,GAAG,EAAEzD,CAAC,EAAE,EAAE;MACvD,MAAME,CAAC,GAAG,IAAI,CAACV,OAAO,CAACQ,CAAC,CAAC;MACzB,IAAIA,CAAC,KAAK,CAAC,EAAE;QACXwD,GAAG,IAAI,KAAK;MACd;MACAA,GAAG,IAAI,GAAG,GAAGtD,CAAC,CAACa,IAAI,GAAG,IAAI,GAAIb,CAAC,CAACM,IAAI,CAACkD,WAAW,CAACxD,CAAC,CAAE;MACpD,IAAIA,CAAC,CAAC3C,QAAQ,KAAK8E,SAAS,EAAE;QAC5BmB,GAAG,IAAI,GAAG,IAAItD,CAAC,CAAC3C,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;MACjD;IACF;IACAiG,GAAG,IAAI,KAAK;IACZ,OAAOA,GAAG;EACZ;;EAEA;AACF;AACA;EACEzD,cAAcA,CAAA,EAAG;IACf,MAAM6D,IAAI,GAAG,IAAIC,+BAAsB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACxD;IACAD,IAAI,CAACE,UAAU,CAACjF,WAAU,CAACkF,WAAW,CAAC;IACvC;IACAH,IAAI,CAACI,aAAa,CAAC,IAAI,CAACxE,OAAO,CAACS,MAAM,CAAC;IAEvC,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAER,GAAG,GAAG,IAAI,CAACjE,OAAO,CAACS,MAAM,EAAEgE,CAAC,GAAGR,GAAG,EAAEQ,CAAC,EAAE,EAAE;MACvD,MAAM/D,CAAC,GAAG,IAAI,CAACV,OAAO,CAACyE,CAAC,CAAC;MACzB;MACA,IAAI,IAAI,CAAC1E,OAAO,CAAC2E,UAAU,GAAG,KAAK,EAAE;QACnCN,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACLJ,IAAI,CAACO,aAAa,CAAC,CAAC,CAAC;MACvB;;MAEA;MACA,IAAIC,KAAK,GAAG9G,KAAK,CAACG,mBAAmB;MACrC,IAAIyC,CAAC,CAAC3C,QAAQ,EAAE;QACd6G,KAAK,IAAI9G,KAAK,CAACC,QAAQ;MACzB,CAAC,MAAM,IAAI2C,CAAC,CAAC3C,QAAQ,KAAK8E,SAAS,IAAI,IAAI,CAAC9C,OAAO,CAAC2E,UAAU,IAAI,KAAK,EAAE;QACvEE,KAAK,IAAI9G,KAAK,CAACW,eAAe;MAChC;MACA2F,IAAI,CAACI,aAAa,CAACI,KAAK,CAAC;;MAEzB;MACAR,IAAI,CAACS,WAAW,CAACnE,CAAC,CAACM,IAAI,CAAC8D,gBAAgB,CAACpE,CAAC,EAAE,IAAI,CAACX,OAAO,CAAC,CAAC;;MAE1D;MACA,IAAIW,CAAC,CAACM,IAAI,CAAC+D,YAAY,EAAE;QACvBX,IAAI,CAACY,cAAc,CAAC,IAAI,CAAC/C,KAAK,EAAE,MAAM,CAAC;MACzC;;MAEA;MACAmC,IAAI,CAACa,aAAa,CAACvE,CAAC,CAACa,IAAI,EAAE,MAAM,CAAC;IACpC;IACA,OAAO6C,IAAI,CAACc,IAAI;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,UAAUA,CAACC,OAAgB,EAAE;IAC3B,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;;EAEA;AACF;AACA;EACEtD,eAAeA,CAAA,EAAG;IAChB;IACA,MAAMsC,IAAI,GAAG,IAAIC,+BAAsB,CAAC,IAAI,CAACtE,OAAO,CAAC2E,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;IACjFN,IAAI,CAACE,UAAU,CAACjF,WAAU,CAACgG,IAAI,CAAC;IAChC,MAAMC,MAAM,GAAG5G,WAAW,CAACC,KAAK;IAChCyF,IAAI,CAACI,aAAa,CAACc,MAAM,CAAC;IAC1BlB,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACvBJ,IAAI,CAACO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC5E,OAAO,CAAC2E,UAAU,IAAI,KAAK,EAAE;MACpCN,IAAI,CAACO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;;IACA,OAAOP,IAAI,CAACc,IAAI;EAClB;;EAEA;AACF;AACA;EACEK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACzC,QAAQ,EAAE;MACjB;IACF;IAEA,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC0C,IAAI,CAAC,QAAQ,CAAC;EACrB;AACF;AAAC,IAAAC,QAAA,GAEc1D,QAAQ;AAAA2D,OAAA,CAAA7H,OAAA,GAAA4H,QAAA;AACvBE,MAAM,CAACD,OAAO,GAAG3D,QAAQ"}