{"version": 3, "file": "moneyn.js", "names": ["MoneyN", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/moneyn.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst MoneyN: DataType = {\n  id: 0x6E,\n  type: 'MONEYN',\n  name: 'MoneyN',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default MoneyN;\nmodule.exports = MoneyN;\n"], "mappings": ";;;;;;AAEA,MAAMA,MAAgB,GAAG;EACvBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EAEdC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAEaV,MAAM;AAAAW,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACrBG,MAAM,CAACF,OAAO,GAAGX,MAAM"}