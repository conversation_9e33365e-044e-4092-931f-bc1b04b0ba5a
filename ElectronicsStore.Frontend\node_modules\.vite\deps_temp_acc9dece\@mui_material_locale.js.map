{"version": 3, "sources": ["../../@mui/material/locale/index.js"], "sourcesContent": ["export const amET = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'መንገድ አሳይ'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'ወደ መጀመሪያው ገጽ ይሂዱ';\n          }\n          if (type === 'last') {\n            return 'ወደ መጨረሻው ገጽ ይሂዱ';\n          }\n          if (type === 'next') {\n            return 'ወደ ቀጣዩ ገጽ ይሂዱ';\n          }\n          // if (type === 'previous') {\n          return 'ወደ ቀዳሚው ገጽ ይሂዱ';\n        },\n        labelRowsPerPage: 'ረድፎች በአንድ ገጽ:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} ከ ${count !== -1 ? count : `${to} በላይ`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ኮከ${value !== 1 ? 'ቦች' : 'ብ'}`,\n        emptyLabelText: 'ባዶ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'አጽዳ',\n        closeText: 'ዝጋ',\n        loadingText: 'በመጫን ላይ…',\n        noOptionsText: 'አማራጮች የሉም',\n        openText: 'ክፈት'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'ዝጋ'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'የገጽ አሰሳ',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'ወደ '}ገጽ ${page}${selected ? '' : ' ሂድ'}`;\n          }\n          if (type === 'first') {\n            return 'ወደ መጀመሪያው ገጽ ይሂዱ';\n          }\n          if (type === 'last') {\n            return 'ወደ መጨረሻው ገጽ ይሂዱ';\n          }\n          if (type === 'next') {\n            return 'ወደ ቀጣዩ ገጽ ይሂዱ';\n          }\n          // if (type === 'previous') {\n          return 'ወደ ቀዳሚው ገጽ ይሂዱ';\n        }\n      }\n    }\n  }\n};\nexport const arEG = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'إظهار المسار'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'انتقل إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'انتقل إلى الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'انتقل إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'انتقل إلى الصفحة السابقة';\n        },\n        labelRowsPerPage: 'عدد الصفوف في الصفحة:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} من ${count !== -1 ? count : ` أكثر من${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'نجوم' : 'نجمة'}`,\n        emptyLabelText: 'فارغ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'مسح',\n        closeText: 'إغلاق',\n        loadingText: 'جار التحميل...',\n        noOptionsText: 'لا يوجد خيارات',\n        openText: 'فتح'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'إغلاق'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'التنقل عبر الصفحات',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'انتقل إلى '} صفحة ${page}`;\n          }\n          if (type === 'first') {\n            return 'انتقل إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'انتقل إلى الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'انتقل إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'انتقل إلى الصفحة السابقة';\n        }\n      }\n    }\n  }\n};\nexport const arSA = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'إظهار المسار'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'الانتقال إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'الانتقال إلى الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'الانتقال إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'الانتقال إلى الصفحة السابقة';\n        },\n        labelRowsPerPage: 'عدد الصفوف في الصفحة:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} من ${count !== -1 ? count : ` أكثر من${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'نجوم' : 'نجمة'}`,\n        emptyLabelText: 'فارغ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'مسح',\n        closeText: 'إغلاق',\n        loadingText: 'جار التحميل...',\n        noOptionsText: 'لا توجد خيارات',\n        openText: 'فتح'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'إغلاق'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'التنقل عبر الصفحات',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'الانتقال إلى '} صفحة ${page}`;\n          }\n          if (type === 'first') {\n            return 'الانتقال إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'الانتقال الي الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'الانتقال إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'الانتقال إلى الصفحة السابقة';\n        }\n      }\n    }\n  }\n};\nexport const arSD = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'إظهار المسار'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'انتقل إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'انتقل إلى الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'انتقل إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'انتقل إلى الصفحة السابقة';\n        },\n        labelRowsPerPage: 'عدد الصفوف في الصفحة:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} من ${count !== -1 ? count : ` أكثر من${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'نجوم' : 'نجمة'}`,\n        emptyLabelText: 'فارغ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'مسح',\n        closeText: 'إغلاق',\n        loadingText: 'جار التحميل...',\n        noOptionsText: 'لا يوجد خيارات',\n        openText: 'فتح'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'إغلاق'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'التنقل عبر الصفحات',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'انتقل إلى '} صفحة ${page}`;\n          }\n          if (type === 'first') {\n            return 'انتقل إلى الصفحة الأولى';\n          }\n          if (type === 'last') {\n            return 'انتقل الي الصفحة الأخيرة';\n          }\n          if (type === 'next') {\n            return 'انتقل إلى الصفحة التالية';\n          }\n          // if (type === 'previous') {\n          return 'انتقل إلى الصفحة السابقة';\n        }\n      }\n    }\n  }\n};\nexport const azAZ = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Yolu göstər'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Birinci səhifəyə keç';\n          }\n          if (type === 'last') {\n            return 'Sonuncu səhifəyə keç';\n          }\n          if (type === 'next') {\n            return 'Növbəti səhifəyə keç';\n          }\n          // if (type === 'previous') {\n          return 'Əvvəlki səhifəyə keç';\n        },\n        labelRowsPerPage: 'Səhifəyə düşən sətrlər:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} dən ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          let pluralForm = 'Ulduz';\n          const lastDigit = value % 10;\n          if (lastDigit > 1 && lastDigit < 5) {\n            pluralForm = 'Ulduzlar';\n          }\n          return `${value} ${pluralForm}`;\n        },\n        emptyLabelText: 'Boş'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Silmək',\n        closeText: 'Bağlamaq',\n        loadingText: 'Yüklənir…',\n        noOptionsText: 'Seçimlər mövcud deyil',\n        openText: 'Открыть'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Bağlamaq'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Səhifənin naviqasiyası',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${page} ${selected ? 'səhifə' : 'səhifəyə keç'}`;\n          }\n          if (type === 'first') {\n            return 'Birinci səhifəyə keç';\n          }\n          if (type === 'last') {\n            return 'Sonuncu səhifəyə keç';\n          }\n          if (type === 'next') {\n            return 'Növbəti səhifəyə keç';\n          }\n          // if (type === 'previous') {\n          return 'Əvvəlki səhifəyə keç';\n        }\n      }\n    }\n  }\n};\nexport const bnBD = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'পথ দেখান'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'প্রথম পৃষ্ঠায় যান';\n          }\n          if (type === 'last') {\n            return 'শেষ পৃষ্ঠায় যান';\n          }\n          if (type === 'next') {\n            return 'পরবর্তী পৃষ্ঠায় যান';\n          }\n          // if (type === 'previous') {\n          return 'আগের পৃষ্ঠায় যান';\n        },\n        labelRowsPerPage: 'প্রতি পৃষ্ঠায় সারি:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} / ${count !== -1 ? count : `${to} থেকে বেশি`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} স্টার`,\n        emptyLabelText: 'খালি'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'পরিষ্কার করুন',\n        closeText: 'বন্ধ করুন',\n        loadingText: 'লোড হচ্ছে…',\n        noOptionsText: 'কোন অপশন নেই',\n        openText: 'ওপেন করুন'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'বন্ধ করুন'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'পেজিনেশন নেভিগেশন',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'যান '}পৃষ্ঠা ${page}-এ`;\n          }\n          if (type === 'first') {\n            return 'প্রথম পৃষ্ঠায় যান';\n          }\n          if (type === 'last') {\n            return 'শেষ পৃষ্ঠায় যান';\n          }\n          if (type === 'next') {\n            return 'পরবর্তী পৃষ্ঠায় যান';\n          }\n          // if (type === 'previous') {\n          return 'আগের পৃষ্ঠায় যান';\n        }\n      }\n    }\n  }\n};\nexport const beBY = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Паказаць шлях'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перайсці на першую старонку';\n          }\n          if (type === 'last') {\n            return 'Перайсці на апошнюю старонку';\n          }\n          if (type === 'next') {\n            return 'Перайсці на наступную старонку';\n          }\n          // if (type === 'previous') {\n          return 'Перайсці на папярэднюю старонку';\n        },\n        labelRowsPerPage: 'Радкоў на старонцы:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} з ${count !== -1 ? count : `больш чым ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          let pluralForm = 'Зорак';\n          const lastDigit = value % 10;\n          if (lastDigit > 1 && lastDigit < 5 && (value < 10 || value > 20)) {\n            pluralForm = 'Зоркі';\n          } else if (lastDigit === 1 && value % 100 !== 11) {\n            pluralForm = 'Зорка';\n          }\n          return `${value} ${pluralForm}`;\n        },\n        emptyLabelText: 'Рэйтынг адсутнічае'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Ачысціць',\n        closeText: 'Закрыць',\n        loadingText: 'Загрузка…',\n        noOptionsText: 'Няма варыянтаў',\n        openText: 'Адкрыць'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Закрыць'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Навігацыя па старонкам',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            if (selected) {\n              return `${page} старонка`;\n            }\n            return `Перайсці на ${page} старонку`;\n          }\n          if (type === 'first') {\n            return 'Перайсці на першую старонку';\n          }\n          if (type === 'last') {\n            return 'Перайсці на апошнюю старонку';\n          }\n          if (type === 'next') {\n            return 'Перайсці на наступную старонку';\n          }\n          // if (type === 'previous') {\n          return 'Перайсці на папярэднюю старонку';\n        }\n      }\n    }\n  }\n};\nexport const bgBG = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Показване на пътя'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Отиди на първата страница';\n          }\n          if (type === 'last') {\n            return 'Отиди на последната страница';\n          }\n          if (type === 'next') {\n            return 'Отиди на следващата страница';\n          }\n          // if (type === 'previous') {\n          return 'Отиди на предишната страница';\n        },\n        labelRowsPerPage: 'Редове на страница:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} от ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Звезд${value !== 1 ? 'и' : 'а'}`,\n        emptyLabelText: 'Изчисти'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Изчисти',\n        closeText: 'Затвори',\n        loadingText: 'Зареждане…',\n        noOptionsText: 'Няма налични опции',\n        openText: 'Отвори'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Затвори'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Пагинация',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Към '}страница ${page}`;\n          }\n          if (type === 'first') {\n            return 'Отиди на първата страница';\n          }\n          if (type === 'last') {\n            return 'Отиди на последната страница';\n          }\n          if (type === 'next') {\n            return 'Отиди на следващата страница';\n          }\n          // if (type === 'previous') {\n          return 'Отиди на предишната страница';\n        }\n      }\n    }\n  }\n};\nexport const caES = {\n  components: {\n    // MuiBreadcrumbs: {\n    //   defaultProps: {\n    //    expandText: 'Show path',\n    //   },\n    // },\n    MuiTablePagination: {\n      defaultProps: {\n        // getItemAriaLabel: (type) => {\n        //   if (type === 'first') {\n        //     return 'Go to first page';\n        //   }\n        //   if (type === 'last') {\n        //     return 'Go to last page';\n        //   }\n        //   if (type === 'next') {\n        //     return 'Go to next page';\n        //   }\n        //   // if (type === 'previous') {\n        //   return 'Go to previous page';\n        // },\n        labelRowsPerPage: 'Files per pàgina:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} de ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'Estrelles' : 'Estrella'}`,\n        emptyLabelText: 'Buit'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Netejar',\n        closeText: 'Tancar',\n        loadingText: 'Carregant…',\n        noOptionsText: 'Sense opcions',\n        openText: 'Obert'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Tancat'\n      }\n    }\n    // MuiPagination: {\n    //   defaultProps: {\n    //     'aria-label': 'Pagination navigation',\n    //     getItemAriaLabel: (type, page, selected) => {\n    //       if (type === 'page') {\n    //         return `${selected ? '' : 'Go to '}page ${page}`;\n    //       }\n    //       if (type === 'first') {\n    //         return 'Go to first page';\n    //       }\n    //       if (type === 'last') {\n    //         return 'Go to last page';\n    //       }\n    //       if (type === 'next') {\n    //         return 'Go to next page';\n    //       }\n    //       // if (type === 'previous') {\n    //       return 'Go to previous page';\n    //     },\n    //   },\n    // },\n  }\n};\nexport const csCZ = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Ukázat cestu'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Jít na první stránku';\n          }\n          if (type === 'last') {\n            return 'Jít na poslední stránku';\n          }\n          if (type === 'next') {\n            return 'Jít na další stránku';\n          }\n          // if (type === 'previous') {\n          return 'Jít na předchozí stránku';\n        },\n        labelRowsPerPage: 'Řádků na stránce:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} z ${count !== -1 ? count : `více než ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          if (value === 1) {\n            return `${value} hvězdička`;\n          }\n          if (value >= 2 && value <= 4) {\n            return `${value} hvězdičky`;\n          }\n          return `${value} hvězdiček`;\n        },\n        emptyLabelText: 'Prázdné'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Vymazat',\n        closeText: 'Zavřít',\n        loadingText: 'Načítání…',\n        noOptionsText: 'Žádné možnosti',\n        openText: 'Otevřít'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Zavřít'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigace stránkováním',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Jít na '}${page}. stránku`;\n          }\n          if (type === 'first') {\n            return 'Jít na první stránku';\n          }\n          if (type === 'last') {\n            return 'Jít na poslední stránku';\n          }\n          if (type === 'next') {\n            return 'Jít na další stránku';\n          }\n          // if (type === 'previous') {\n          return 'Jít na předchozí stránku';\n        }\n      }\n    }\n  }\n};\nexport const daDK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Vis sti'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Gå til den første side';\n          }\n          if (type === 'last') {\n            return 'Gå til den sidste side';\n          }\n          if (type === 'next') {\n            return 'Gå til den næste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til den forrige side';\n        },\n        labelRowsPerPage: 'Rækker pr side:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} af ${count !== -1 ? count : `mere end ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Stjern${value !== 1 ? 'er' : ''}`,\n        emptyLabelText: 'Tom'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Slet',\n        closeText: 'Luk',\n        loadingText: 'Indlæser…',\n        noOptionsText: 'Ingen muligheder',\n        openText: 'Åben'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Luk'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Sideinddelings navigation',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Go to '}page ${page}`;\n          }\n          if (type === 'first') {\n            return 'Gå til den første side';\n          }\n          if (type === 'last') {\n            return 'Gå til den sidste side';\n          }\n          if (type === 'next') {\n            return 'Gå til den næste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til den forrige side';\n        }\n      }\n    }\n  }\n};\nexport const deDE = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Pfad anzeigen'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Zur ersten Seite';\n          }\n          if (type === 'last') {\n            return 'Zur letzten Seite';\n          }\n          if (type === 'next') {\n            return 'Zur nächsten Seite';\n          }\n          // if (type === 'previous') {\n          return 'Zur vorherigen Seite';\n        },\n        labelRowsPerPage: 'Zeilen pro Seite:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} von ${count !== -1 ? count : `mehr als ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'Sterne' : 'Stern'}`,\n        emptyLabelText: 'Keine Wertung'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Leeren',\n        closeText: 'Schließen',\n        loadingText: 'Wird geladen…',\n        noOptionsText: 'Keine Optionen',\n        openText: 'Öffnen'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Schließen'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigation via Seitennummerierung',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Gehe zu '}Seite ${page}`;\n          }\n          if (type === 'first') {\n            return 'Zur ersten Seite';\n          }\n          if (type === 'last') {\n            return 'Zur letzten Seite';\n          }\n          if (type === 'next') {\n            return 'Zur nächsten Seite';\n          }\n          // if (type === 'previous') {\n          return 'Zur vorherigen Seite';\n        }\n      }\n    }\n  }\n};\nexport const elGR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Εμφάνιση διαδρομής'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Πρώτη σελίδα';\n          }\n          if (type === 'last') {\n            return 'Τελευταία σελίδα';\n          }\n          if (type === 'next') {\n            return 'Επόμενη σελίδα';\n          }\n\n          // if (type === \"previous\") {\n          return 'Προηγούμενη σελίδα';\n        },\n        labelRowsPerPage: 'Γραμμές ανα σελίδα:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} από ${count !== -1 ? count : `πάνω από ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Αστέρι${value !== 1 ? 'α' : ''}`,\n        emptyLabelText: 'Χωρίς βαθμολόγηση'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Καθαρισμός',\n        closeText: 'Κλείσιμο',\n        loadingText: 'Φόρτωση…',\n        noOptionsText: 'Δεν υπάρχουν επιλογές',\n        openText: 'Άνοιγμα'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Κλείσιμο'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Πλοήγηση σε σελίδες',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Μετάβαση '}σελίδα ${page}`;\n          }\n          if (type === 'first') {\n            return 'Πρώτη σελίδα';\n          }\n          if (type === 'last') {\n            return 'Τελευταία σελίδα';\n          }\n          if (type === 'next') {\n            return 'Επόμενη σελίδα';\n          }\n\n          // if (type === \"previous\") {\n          return 'Προηγούμενη σελίδα';\n        }\n      }\n    }\n  }\n};\n\n// default\nexport const enUS = {\n  /*\n  components: {\n    MuiBreadcrumbs: { defaultProps: {\n      expandText: 'Show path',\n    }},\n    MuiTablePagination: { defaultProps: {\n      getItemAriaLabel: (type) => {\n        if (type === 'first') {\n          return 'Go to first page';\n        }\n        if (type === 'last') {\n          return 'Go to last page';\n        }\n        if (type === 'next') {\n          return 'Go to next page';\n        }\n        // if (type === 'previous') {\n        return 'Go to previous page';\n      },\n      labelRowsPerPage: 'Rows per page:',\n      labelDisplayedRows: ({ from, to, count }) =>\n  `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`,\n    }},\n    MuiRating: { defaultProps: {\n      getLabelText: value => `${value} Star${value !== 1 ? 's' : ''}`,\n      emptyLabelText: 'Empty',\n    }},\n    MuiAutocomplete: { defaultProps: {\n      clearText: 'Clear',\n      closeText: 'Close',\n      loadingText: 'Loading…',\n      noOptionsText: 'No options',\n      openText: 'Open',\n    }},\n    MuiAlert: { defaultProps: {\n      closeText: 'Close',\n    }},\n    MuiPagination: {  defaultProps: {\n      'aria-label': 'Pagination navigation',\n      getItemAriaLabel: (type, page, selected) => {\n        if (type === 'page') {\n          return `${selected ? '' : 'Go to '}page ${page}`;\n        }\n        if (type === 'first') {\n          return 'Go to first page';\n        }\n        if (type === 'last') {\n          return 'Go to last page';\n        }\n        if (type === 'next') {\n          return 'Go to next page';\n        }\n        // if (type === 'previous') {\n        return 'Go to previous page';\n      },\n    }},\n  },\n  */\n};\nexport const esES = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Mostrar ruta'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Ir a la primera página';\n          }\n          if (type === 'last') {\n            return 'Ir a la última página';\n          }\n          if (type === 'next') {\n            return 'Ir a la página siguiente';\n          }\n          // if (type === 'previous') {\n          return 'Ir a la página anterior';\n        },\n        labelRowsPerPage: 'Filas por página:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} de ${count !== -1 ? count : `más de ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Estrella${value !== 1 ? 's' : ''}`,\n        emptyLabelText: 'Vacío'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Limpiar',\n        closeText: 'Cerrar',\n        loadingText: 'Cargando…',\n        noOptionsText: 'Sin opciones',\n        openText: 'Abierto'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Cerrar'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Paginador',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Ir a la '}página ${page}`;\n          }\n          if (type === 'first') {\n            return 'Ir a la primera página';\n          }\n          if (type === 'last') {\n            return 'Ir a la última página';\n          }\n          if (type === 'next') {\n            return 'Ir a la página siguiente';\n          }\n          // if (type === 'previous') {\n          return 'Ir a la página anterior';\n        }\n      }\n    }\n  }\n};\nexport const etEE = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Näita teed'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Vali esimene lehekülg';\n          }\n          if (type === 'last') {\n            return 'Vali viimane lehekülg';\n          }\n          if (type === 'next') {\n            return 'Vali järgmine lehekülg';\n          }\n          // if (type === 'previous') {\n          return 'Vali eelmine lehekülg';\n        },\n        labelRowsPerPage: 'Ridu leheküljel:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} / ${count !== -1 ? count : `rohkem kui ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Tärn${value !== 1 ? 'i' : ''}`,\n        emptyLabelText: 'Tühi'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Tühjenda',\n        closeText: 'Sulge',\n        loadingText: 'Laen…',\n        noOptionsText: 'Valikuid ei ole',\n        openText: 'Ava'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Sulge'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Lehekülgede valik',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Vali '}lehekülg ${page}`;\n          }\n          if (type === 'first') {\n            return 'Vali esimene lehekülg';\n          }\n          if (type === 'last') {\n            return 'Vali viimane lehekülg';\n          }\n          if (type === 'next') {\n            return 'Vali järgmine lehekülg';\n          }\n          // if (type === 'previous') {\n          return 'Vali eelmine lehekülg';\n        }\n      }\n    }\n  }\n};\nexport const faIR = {\n  components: {\n    // MuiBreadcrumbs: {\n    //   defaultProps: {\n    //     expandText: 'Show path',\n    //   },\n    // },\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'نمایش مسیر'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'رفتن به اولین صفحه';\n          }\n          if (type === 'last') {\n            return 'رفتن به آخرین صفحه';\n          }\n          if (type === 'next') {\n            return 'رفتن به صفحه‌ی بعدی';\n          }\n          // if (type === 'previous') {\n          return 'رفتن به صفحه‌ی قبلی';\n        },\n        labelRowsPerPage: 'تعداد سطرهای هر صفحه:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} از ${count !== -1 ? count : `بیشتر از ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ستاره`,\n        emptyLabelText: 'خالی'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'پاک‌کردن',\n        closeText: 'بستن',\n        loadingText: 'در حال بارگذاری…',\n        noOptionsText: 'بی‌نتیجه',\n        openText: 'بازکردن'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'بستن'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'ناوبری صفحه',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'رفتن به '}صفحهٔ ${page}`;\n          }\n          if (type === 'first') {\n            return 'رفتن به اولین صفحه';\n          }\n          if (type === 'last') {\n            return 'رفتن به آخرین صفحه';\n          }\n          if (type === 'next') {\n            return 'رفتن به صفحه‌ی بعدی';\n          }\n          // if (type === 'previous') {\n          return 'رفتن به صفحه‌ی قبلی';\n        }\n      }\n    }\n  }\n};\nexport const fiFI = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Näytä reitti'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Mene ensimmäiselle sivulle';\n          }\n          if (type === 'last') {\n            return 'Mene viimeiselle sivulle';\n          }\n          if (type === 'next') {\n            return 'Mene seuraavalle sivulle';\n          }\n          // if (type === 'previous') {\n          return 'Mene edelliselle sivulle';\n        },\n        labelRowsPerPage: 'Rivejä per sivu:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} / ${count !== -1 ? count : `enemmän kuin ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Täht${value !== 1 ? 'eä' : 'i'}`,\n        emptyLabelText: 'Tyhjä'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Tyhjennä',\n        closeText: 'Sulje',\n        loadingText: 'Ladataan…',\n        noOptionsText: 'Ei valintoja',\n        openText: 'Avaa'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Sulje'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Sivutus navigaatio',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? 'sivu' : 'Mene sivulle'} ${page}`;\n          }\n          if (type === 'first') {\n            return 'Mene ensimmäiselle sivulle';\n          }\n          if (type === 'last') {\n            return 'Mene viimeiselle sivulle';\n          }\n          if (type === 'next') {\n            return 'Mene seuraavalle sivulle';\n          }\n          // if (type === 'previous') {\n          return 'Mene edelliselle sivulle';\n        }\n      }\n    }\n  }\n};\nexport const frFR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Montrer le chemin'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Aller à la première page';\n          }\n          if (type === 'last') {\n            return 'Aller à la dernière page';\n          }\n          if (type === 'next') {\n            return 'Aller à la page suivante';\n          }\n          // if (type === 'previous') {\n          return 'Aller à la page précédente';\n        },\n        labelRowsPerPage: 'Lignes par page :',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} sur ${count !== -1 ? count : `plus que ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Etoile${value !== 1 ? 's' : ''}`,\n        emptyLabelText: 'Vide'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Vider',\n        closeText: 'Fermer',\n        loadingText: 'Chargement…',\n        noOptionsText: 'Pas de résultats',\n        openText: 'Ouvrir'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Fermer'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'navigation de pagination',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Aller à la '}page ${page}`;\n          }\n          if (type === 'first') {\n            return 'Aller à la première page';\n          }\n          if (type === 'last') {\n            return 'Aller à la dernière page';\n          }\n          if (type === 'next') {\n            return 'Aller à la page suivante';\n          }\n          // if (type === 'previous') {\n          return 'Aller à la page précédente';\n        }\n      }\n    }\n  }\n};\nexport const heIL = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'הצג נתיב'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'לעמוד הראשון';\n          }\n          if (type === 'last') {\n            return 'לעמוד האחרון';\n          }\n          if (type === 'next') {\n            return 'לעמוד הבא';\n          }\n          // if (type === 'previous') {\n          return 'לעמוד הקודם';\n        },\n        labelRowsPerPage: 'שורות בעמוד:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} מתוך ${count !== -1 ? count : `יותר מ ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} כוכב${value !== 1 ? 'ים' : ''}`,\n        emptyLabelText: 'ריק'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'נקה',\n        closeText: 'סגור',\n        loadingText: 'טוען…',\n        noOptionsText: 'אין אופציות',\n        openText: 'פתח'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'סגור'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'ניווט בעמודים',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'ל '}עמוד ${page}`;\n          }\n          if (type === 'first') {\n            return 'לעמוד הראשון';\n          }\n          if (type === 'last') {\n            return 'לעמוד האחרון';\n          }\n          if (type === 'next') {\n            return 'לעמוד הבא';\n          }\n          // if (type === 'previous') {\n          return 'לעמוד הקודם';\n        }\n      }\n    }\n  }\n};\nexport const hiIN = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'रास्ता दिखायें'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'पहले पृष्ठ पर जाएँ';\n          }\n          if (type === 'last') {\n            return 'अंतिम पृष्ठ पर जाएँ';\n          }\n          if (type === 'next') {\n            return 'अगले पृष्ठ पर जाएँ';\n          }\n          // if (type === 'previous') {\n          return 'पिछले पृष्ठ पर जाएँ';\n        },\n        labelRowsPerPage: 'पंक्तियाँ प्रति पृष्ठ:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to === -1 ? count : to} कुल ${count} में`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} तार${value !== 1 ? 'े' : 'ा'}`,\n        emptyLabelText: 'रिक्त'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'हटायें',\n        closeText: 'बंद करें',\n        loadingText: 'लोड हो रहा है…',\n        noOptionsText: 'कोई विकल्प नहीं',\n        openText: 'खोलें'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'बंद करें'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'पृस्ठानुसार संचालन',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `पृष्ठ ${page} ${selected ? '' : ' पर जाएँ'}`;\n          }\n          if (type === 'first') {\n            return 'पहले पृष्ठ पर जाएँ';\n          }\n          if (type === 'last') {\n            return 'अंतिम पृष्ठ पर जाएँ';\n          }\n          if (type === 'next') {\n            return 'अगले पृष्ठ पर जाएँ';\n          }\n          // if (type === 'previous') {\n          return 'पिछले पृष्ठ पर जाएँ';\n        }\n      }\n    }\n  }\n};\n\n// Croatian - Hrvatski\nexport const hrHR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Pokaži putanju'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Idi na prvu stranicu';\n          }\n          if (type === 'last') {\n            return 'Idi na posljednju stranicu';\n          }\n          if (type === 'next') {\n            return 'Idi na sljedeću stranicu';\n          }\n          // if (type === 'previous') {\n          return 'Idi na prethodnu stranicu';\n        },\n        labelRowsPerPage: 'Redova po stranici:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} od ${count !== -1 ? count : `više nego ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          const lastTwoDigits = value % 100;\n          if ([2, 3, 4].includes(lastDigit) && ![12, 13, 14].includes(lastTwoDigits)) {\n            return 'Zvijezde';\n          }\n          return 'Zvijezda';\n        },\n        emptyLabelText: 'Prazno'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Briši',\n        closeText: 'Zatvori',\n        loadingText: 'Učitavanje…',\n        noOptionsText: 'Nema opcija',\n        openText: 'Otvori'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Zatvori'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigacija po stranicama',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Idi na '}stranicu ${page}`;\n          }\n          if (type === 'first') {\n            return 'Idi na prvu stranicu';\n          }\n          if (type === 'last') {\n            return 'Idi na zadnju stranicu';\n          }\n          if (type === 'next') {\n            return 'Idi na sljedeću stranicu';\n          }\n          // if (type === 'previous') {\n          return 'Idi na prethodnu stranicu';\n        }\n      }\n    }\n  }\n};\nexport const huHU = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Útvonal'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Első oldalra';\n          }\n          if (type === 'last') {\n            return 'Utolsó oldalra';\n          }\n          if (type === 'next') {\n            return 'Következő oldalra';\n          }\n          // if (type === 'previous') {\n          return 'Előző oldalra';\n        },\n        labelRowsPerPage: 'Sorok száma:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} / ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Csillag`,\n        emptyLabelText: 'Üres'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Törlés',\n        closeText: 'Bezárás',\n        loadingText: 'Töltés…',\n        noOptionsText: 'Nincs találat',\n        openText: 'Megnyitás'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Bezárás'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Lapozás',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${page}. oldal${selected ? '' : 'ra'}`;\n          }\n          if (type === 'first') {\n            return 'Első oldalra';\n          }\n          if (type === 'last') {\n            return 'Utolsó oldalra';\n          }\n          if (type === 'next') {\n            return 'Következő oldalra';\n          }\n          // if (type === 'previous') {\n          return 'Előző oldalra';\n        }\n      }\n    }\n  }\n};\nexport const hyAM = {\n  components: {\n    // MuiBreadcrumbs: {\n    //   defaultProps: {\n    //     expandText: 'Show path',\n    //   },\n    // },\n    MuiTablePagination: {\n      defaultProps: {\n        // getItemAriaLabel: (type) => {\n        //   if (type === 'first') {\n        //     return 'Go to first page';\n        //   }\n        //   if (type === 'last') {\n        //     return 'Go to last page';\n        //   }\n        //   if (type === 'next') {\n        //     return 'Go to next page';\n        //   }\n        //   // if (type === 'previous') {\n        //   return 'Go to previous page';\n        // },\n        labelRowsPerPage: 'Տողեր մեկ էջում`'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} / ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Աստղ`,\n        emptyLabelText: 'Դատարկ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Մաքրել',\n        closeText: 'Փակել',\n        loadingText: 'Բեռնում…',\n        noOptionsText: 'Տարբերակներ չկան',\n        openText: 'Բացել'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Փակել'\n      }\n    }\n    // MuiPagination: {\n    //   defaultProps: {\n    //     'aria-label': 'Pagination navigation',\n    //     getItemAriaLabel: (type, page, selected) => {\n    //       if (type === 'page') {\n    //         return `${selected ? '' : 'Go to '}page ${page}`;\n    //       }\n    //       if (type === 'first') {\n    //         return 'Go to first page';\n    //       }\n    //       if (type === 'last') {\n    //         return 'Go to last page';\n    //       }\n    //       if (type === 'next') {\n    //         return 'Go to next page';\n    //       }\n    //       // if (type === 'previous') {\n    //       return 'Go to previous page';\n    //     },\n    //   },\n    // },\n  }\n};\nexport const idID = {\n  components: {\n    // MuiBreadcrumbs: {\n    //   defaultProps: {\n    //     expandText: 'Show path',\n    //   },\n    // },\n    MuiTablePagination: {\n      defaultProps: {\n        // getItemAriaLabel: (type) => {\n        //   if (type === 'first') {\n        //     return 'Go to first page';\n        //   }\n        //   if (type === 'last') {\n        //     return 'Go to last page';\n        //   }\n        //   if (type === 'next') {\n        //     return 'Go to next page';\n        //   }\n        //   // if (type === 'previous') {\n        //   return 'Go to previous page';\n        // },\n        labelRowsPerPage: 'Baris per halaman:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} dari ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Bintang`\n        // emptyLabelText: 'Empty',\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Hapus',\n        closeText: 'Tutup',\n        loadingText: 'Memuat…',\n        noOptionsText: 'Tidak ada opsi',\n        openText: 'Buka'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Tutup'\n      }\n    }\n    // MuiPagination: {\n    //   defaultProps: {\n    //     'aria-label': 'Pagination navigation',\n    //     getItemAriaLabel: (type, page, selected) => {\n    //       if (type === 'page') {\n    //         return `${selected ? '' : 'Go to '}page ${page}`;\n    //       }\n    //       if (type === 'first') {\n    //         return 'Go to first page';\n    //       }\n    //       if (type === 'last') {\n    //         return 'Go to last page';\n    //       }\n    //       if (type === 'next') {\n    //         return 'Go to next page';\n    //       }\n    //       // if (type === 'previous') {\n    //       return 'Go to previous page';\n    //     },\n    //   },\n    // },\n  }\n};\nexport const isIS = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Sýna slóð'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Fara á fyrstu síðu';\n          }\n          if (type === 'last') {\n            return 'Fara á síðustu síðu';\n          }\n          if (type === 'next') {\n            return 'Fara á næstu síðu';\n          }\n          // if (type === 'previous') {\n          return 'Fara á fyrri síðu';\n        },\n        labelRowsPerPage: 'Raðir á síðu:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} af ${count !== -1 ? count : `fleiri en ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value === 1 ? 'stjarna' : 'stjörnur'}`,\n        emptyLabelText: 'Tómt'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Hreinsa',\n        closeText: 'Loka',\n        loadingText: 'Hlaða…',\n        noOptionsText: 'Engar niðurstöður',\n        openText: 'Opna'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Loka'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Síðuflakk',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? 'Síða' : 'Fara á síðu'} ${page}`;\n          }\n          if (type === 'first') {\n            return 'Fara á fyrstu síðu';\n          }\n          if (type === 'last') {\n            return 'Fara á síðustu síðu';\n          }\n          if (type === 'next') {\n            return 'Fara á næstu síðu';\n          }\n          // if (type === 'previous') {\n          return 'Fara á fyrri síðu';\n        }\n      }\n    }\n  }\n};\nexport const itIT = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Visualizza percorso'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Vai alla prima pagina';\n          }\n          if (type === 'last') {\n            return \"Vai all'ultima pagina\";\n          }\n          if (type === 'next') {\n            return 'Vai alla pagina successiva';\n          }\n          // if (type === 'previous') {\n          return 'Vai alla pagina precedente';\n        },\n        labelRowsPerPage: 'Righe per pagina:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} di ${count !== -1 ? count : `più di ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Stell${value !== 1 ? 'e' : 'a'}`,\n        emptyLabelText: 'Vuoto'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Svuota',\n        closeText: 'Chiudi',\n        loadingText: 'Caricamento in corso…',\n        noOptionsText: 'Nessuna opzione',\n        openText: 'Apri'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Chiudi'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigazione impaginata',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Vai alla '}pagina ${page}`;\n          }\n          if (type === 'first') {\n            return 'Vai alla prima pagina';\n          }\n          if (type === 'last') {\n            return \"Vai all'ultima pagina\";\n          }\n          if (type === 'next') {\n            return 'Vai alla pagina successiva';\n          }\n          // if (type === 'previous') {\n          return 'Vai alla pagina precedente';\n        }\n      }\n    }\n  }\n};\nexport const jaJP = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'すべて表示'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return '最初のページへ';\n          }\n          if (type === 'last') {\n            return '最後のページへ';\n          }\n          if (type === 'next') {\n            return '次のページへ';\n          }\n          // if (type === 'previous') {\n          return '前のページへ';\n        },\n        labelRowsPerPage: 'ページあたりの行数:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}～${to} / ${count !== -1 ? count : `${to}以上`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `星${value}`,\n        emptyLabelText: '星なし'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'クリア',\n        closeText: '閉じる',\n        loadingText: '読み込み中…',\n        noOptionsText: 'データがありません',\n        openText: '開く'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: '閉じる'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'ページ選択',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `ページ${page}${selected ? '' : 'へ'}`;\n          }\n          if (type === 'first') {\n            return '最初のページへ';\n          }\n          if (type === 'last') {\n            return '最後のページへ';\n          }\n          if (type === 'next') {\n            return '次のページへ';\n          }\n          // if (type === 'previous') {\n          return '前のページへ';\n        }\n      }\n    }\n  }\n};\nexport const khKH = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'បង្ហាញផ្លូវ'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'ទៅទំព័រដំបូង';\n          }\n          if (type === 'last') {\n            return 'ទៅទំព័រចុងក្រោយ';\n          }\n          if (type === 'next') {\n            return 'ទៅទំព័របន្ទាប់';\n          }\n          // if (type === 'previous') {\n          return 'ទៅទំព័រមុន';\n        },\n        labelRowsPerPage: 'ចំនួនជួរដេកក្នុងមួយទំព័រ:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from} - ${to} នៃ ${count !== -1 ? count : `ច្រើនជាង ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ផ្កាយ${value !== 1 ? '' : ''}`,\n        emptyLabelText: 'ទទេ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'លុបចោល',\n        closeText: 'បិទ',\n        loadingText: 'កំពុងលោត…',\n        noOptionsText: 'គ្មានជម្រើស',\n        openText: 'បើក'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'បិទ'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'រុករកទំព័រ',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'ទៅ '}ទំព័រ ${page}`;\n          }\n          if (type === 'first') {\n            return 'ទៅទំព័រដំបូង';\n          }\n          if (type === 'last') {\n            return 'ទៅទំព័រចុងក្រោយ';\n          }\n          if (type === 'next') {\n            return 'ទៅទំព័របន្ទាប់';\n          }\n          // if (type === 'previous') {\n          return 'ទៅទំព័រមុន';\n        }\n      }\n    }\n  }\n};\nexport const koKR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: '경로 보기'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return '첫 번째 페이지로 이동';\n          }\n          if (type === 'last') {\n            return '마지막 페이지로 이동';\n          }\n          if (type === 'next') {\n            return '다음 페이지로 이동';\n          }\n          // if (type === 'previous') {\n          return '이전 페이지로 이동';\n        },\n        labelRowsPerPage: '페이지 당 행:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} / ${count !== -1 ? count : `${to}개 이상`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} 점`,\n        emptyLabelText: '빈 텍스트'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: '지우기',\n        closeText: '닫기',\n        loadingText: '불러오는 중…',\n        noOptionsText: '옵션 없음',\n        openText: '열기'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: '닫기'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': '페이지네이션 네비게이션',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${page} 번째 페이지${selected ? '' : '로 이동'}`;\n          }\n          if (type === 'first') {\n            return '첫 번째 페이지로 이동';\n          }\n          if (type === 'last') {\n            return '마지막 페이지로 이동';\n          }\n          if (type === 'next') {\n            return '다음 페이지로 이동';\n          }\n          // if (type === 'previous') {\n          return '이전 페이지로 이동';\n        }\n      }\n    }\n  }\n};\nexport const kuCKB = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'ڕێچکە پیشان بدە'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'چوونە یەکەم پەڕە';\n          }\n          if (type === 'last') {\n            return 'چوونە کۆتا پەڕە';\n          }\n          if (type === 'next') {\n            return 'چوونە پەڕەی دواتر';\n          }\n          // if (type === 'previous') {\n          return 'گەڕانەوە بۆ پەڕەی پێشوو';\n        },\n        labelRowsPerPage: 'ژمارەی ڕیزەکان لە هەر پەڕەیەک:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} لە ${count !== -1 ? count : ` زیاترە لە${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'ئەستێرەکان' : 'ئەستێرە'}`,\n        emptyLabelText: 'خاڵیە'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'سڕینەوە',\n        closeText: 'داخستن',\n        loadingText: 'لە بارکردندایە...',\n        noOptionsText: 'هیچ بژاردەیەک نیە',\n        openText: 'کردنەوە'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'داخستن'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'گەڕان لە پەڕەکان',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'چوون بۆ '} پەڕەی ${page}`;\n          }\n          if (type === 'first') {\n            return 'چوونە یەکەم پەڕە';\n          }\n          if (type === 'last') {\n            return 'چوونە کۆتا پەڕە';\n          }\n          if (type === 'next') {\n            return 'چوونە پەڕەی دواتر';\n          }\n          // if (type === 'previous') {\n          return 'گەڕانەوە بۆ پەڕەی پێشوو';\n        }\n      }\n    }\n  }\n};\nexport const kuLatn = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Rê nîşan bide'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Biçe rûpela yekem';\n          }\n          if (type === 'last') {\n            return 'Biçe rûpela dawî';\n          }\n          if (type === 'next') {\n            return 'Biçe rûpela din';\n          }\n          // if (type === 'previous') {\n          return 'Biçe rûpela berê';\n        },\n        labelRowsPerPage: 'Rêz li ser rûpelê:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} of ${count !== -1 ? count : `zêdetir ji ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Stêrk`,\n        emptyLabelText: 'Vala'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Paqij bike',\n        closeText: 'Bigre',\n        loadingText: 'Tê barkirin…',\n        noOptionsText: 'Vebijêrk tune',\n        openText: 'Veke'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Bigre'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navîgasyona rûpelan',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Biçe '}rûpel ${page}`;\n          }\n          if (type === 'first') {\n            return 'Biçe rûpela yekem';\n          }\n          if (type === 'last') {\n            return 'Biçe rûpela dawî';\n          }\n          if (type === 'next') {\n            return 'Biçe rûpela din';\n          }\n          // if (type === 'previous') {\n          return 'Biçe rûpela berê';\n        }\n      }\n    }\n  }\n};\nexport const kkKZ = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Толық жолды көрсету'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Бірінші бетке өту';\n          }\n          if (type === 'last') {\n            return 'Соңғы бетке өту';\n          }\n          if (type === 'next') {\n            return 'Келесі бетке өту';\n          }\n          // if (type === 'previous') {\n          return 'Алдыңғы бетке өту';\n        },\n        labelRowsPerPage: 'Беттегі қатарлар:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${count !== -1 ? count : `+${to}`} қатардың ішінен ${from}–${to}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} жұлдыз`,\n        emptyLabelText: 'Рейтинг жоқ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Тазарту',\n        closeText: 'Жабу',\n        loadingText: 'Жүктелуде…',\n        noOptionsText: 'Қол жетімді нұсқалар жоқ',\n        openText: 'Ашу'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Жабу'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Беттерді шарлау',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            if (selected) {\n              return `${page} — бет`;\n            }\n            return `${page} — бетке өту`;\n          }\n          if (type === 'first') {\n            return 'Бірінші бетке өту';\n          }\n          if (type === 'last') {\n            return 'Соңғы бетке өту';\n          }\n          if (type === 'next') {\n            return 'Келесі бетке өту';\n          }\n          // if (type === 'previous') {\n          return 'Алдыңғы бетке өту';\n        }\n      }\n    }\n  }\n};\n\n// Macedonian - Македонски\nexport const mkMK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Прикажи патека'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Оди на прва страница';\n          }\n          if (type === 'last') {\n            return 'Оди на последна страница';\n          }\n          if (type === 'next') {\n            return 'Оди на следна страница';\n          }\n          // if (type === 'previous') {\n          return 'Оди на предходна страница';\n        },\n        labelRowsPerPage: 'Редови по страница:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} од ${count !== -1 ? count : `повеќе од ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          return `${value} Ѕвезд${lastDigit === 1 ? 'а' : 'и'}`;\n        },\n        emptyLabelText: 'Празно'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Избриши',\n        closeText: 'Затвори',\n        loadingText: 'Се презема',\n        noOptionsText: 'Нема опција',\n        openText: 'Отвори'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Затвори'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Навигација низ страници',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Оди на '}страница ${page}`;\n          }\n          if (type === 'first') {\n            return 'Оди на прва страница';\n          }\n          if (type === 'last') {\n            return 'Оди на последна страница';\n          }\n          if (type === 'next') {\n            return 'Оди на следна страница';\n          }\n          // if (type === 'previous') {\n          return 'Оди на предходна страница';\n        }\n      }\n    }\n  }\n};\n\n// Myanmar - မြန်မာ\nexport const myMY = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'လမ်းကြောင်းပြပါ။'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'ပထမစာမျက်နှာသို့သွားပါ။';\n          }\n          if (type === 'last') {\n            return 'နောက်ဆုံးစာမျက်နှာသို့သွားပါ။';\n          }\n          if (type === 'next') {\n            return 'နောက်စာမျက်နှာသို့သွားပါ။';\n          }\n          // if (type === 'previous') {\n          return 'ယခင်စာမျက်နှာသို့သွားပါ။';\n        },\n        labelRowsPerPage: 'စာမျက်နှာအလိုက် အတန်းများ:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} ၏ ${count !== -1 ? count : `ထက်ပိုပြီး ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          return `${value} ကြယ်ပွင့်${lastDigit === 1 ? '၎' : ''}`;\n        },\n        emptyLabelText: 'ဗလာ'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'ရှင်းလင်းသော',\n        closeText: 'ပိတ်လိုက်',\n        loadingText: 'ဖွင့်နေသည်…',\n        noOptionsText: 'ရွေးချယ်ခွင့်မရှိပါ။',\n        openText: 'ဖွင့်သည်။'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'ပိတ်လိုက်'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Pagination အညွှန်း',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'သွားပါ။ '}စာမျက်နှာ ${page}`;\n          }\n          if (type === 'first') {\n            return 'ပထမစာမျက်နှာသို့သွားပါ။';\n          }\n          if (type === 'last') {\n            return 'နောက်ဆုံးစာမျက်နှာသို့သွားပါ။';\n          }\n          if (type === 'next') {\n            return 'နောက်စာမျက်နှာသို့သွားပါ။';\n          }\n          // if (type === 'previous') {\n          return 'ယခင်စာမျက်နှာသို့သွားပါ။';\n        }\n      }\n    }\n  }\n};\n\n// Malay-Melayu\nexport const msMS = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Tunjukkan laluan'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Pergi ke halaman pertama';\n          }\n          if (type === 'last') {\n            return 'Pergi ke halaman terakhir';\n          }\n          if (type === 'next') {\n            return 'Pergi ke halaman seterusnya';\n          }\n          // if (type === 'previous') {\n          return 'Pergi ke halaman sebelumnya';\n        },\n        labelRowsPerPage: 'Baris setiap halaman:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} daripada ${count !== -1 ? count : `lebih daripada ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          return `${value} Bintang${lastDigit === 1 ? 's' : ''}`;\n        },\n        emptyLabelText: 'kosong'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Jelas',\n        closeText: 'tutup',\n        loadingText: 'Memuatkan…',\n        noOptionsText: 'Tiada pilihan',\n        openText: 'Buka'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'tutup'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigasi penomboran',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Pergi ke '}muka surat ${page}`;\n          }\n          if (type === 'first') {\n            return 'Pergi ke halaman pertama';\n          }\n          if (type === 'last') {\n            return 'Pergi ke halaman terakhir';\n          }\n          if (type === 'next') {\n            return 'Pergi ke halaman seterusnya';\n          }\n          // if (type === 'previous') {\n          return 'Pergi ke halaman sebelumnya';\n        }\n      }\n    }\n  }\n};\n\n// Nepali-नेपाली\nexport const neNP = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'बाटो देखाउनुहोस्'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'पहिलो पृष्ठमा जानुहोस्';\n          }\n          if (type === 'last') {\n            return 'अन्तिम पृष्ठमा जानुहोस्';\n          }\n          if (type === 'next') {\n            return 'अर्को पृष्ठमा जानुहोस्';\n          }\n          // if (type === 'previous') {\n          return 'अघिल्लो पृष्ठमा जानुहोस्';\n        },\n        labelRowsPerPage: 'प्रति पृष्ठ पङ्क्तिहरू:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} को ${count !== -1 ? count : `धेरै ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          return `${value} तारा${lastDigit === 1 ? 'स' : ''}`;\n        },\n        emptyLabelText: 'खाली'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'खाली गर्नुहोस्',\n        closeText: 'बन्द गर्नुहोस्',\n        loadingText: 'लोड हुँदै...',\n        noOptionsText: 'कुनै विकल्प छैन',\n        openText: 'खोल्नुहोस्'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'बन्द गर्नुहोस्'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'पृष्ठांकन नेभिगेसन',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'जाऊ त्यहाँ '}पृष्ठ ${page}`;\n          }\n          if (type === 'first') {\n            return 'पहिलो पृष्ठमा जानुहोस्';\n          }\n          if (type === 'last') {\n            return 'अन्तिम पृष्ठमा जानुहोस्';\n          }\n          if (type === 'next') {\n            return 'अर्को पृष्ठमा जानुहोस्';\n          }\n          // if (type === 'previous') {\n          return 'अघिल्लो पृष्ठमा जानुहोस्';\n        }\n      }\n    }\n  }\n};\nexport const nbNO = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Vis sti'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Gå til første side';\n          }\n          if (type === 'last') {\n            return 'Gå til siste side';\n          }\n          if (type === 'next') {\n            return 'Gå til neste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til forrige side';\n        },\n        labelRowsPerPage: 'Rader per side:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} av ${count !== -1 ? count : `mer enn ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Stjerne${value !== 1 ? 'r' : ''}`,\n        emptyLabelText: 'Tom'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Tøm',\n        closeText: 'Lukk',\n        loadingText: 'Laster inn…',\n        noOptionsText: 'Ingen alternativer',\n        openText: 'Åpne'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Lukk'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Paginering navigasjon',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Gå til '}side ${page}`;\n          }\n          if (type === 'first') {\n            return 'Gå til første side';\n          }\n          if (type === 'last') {\n            return 'Gå til siste side';\n          }\n          if (type === 'next') {\n            return 'Gå til neste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til forrige side';\n        }\n      }\n    }\n  }\n};\nexport const nnNO = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Vis sti'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Gå til første side';\n          }\n          if (type === 'last') {\n            return 'Gå til siste side';\n          }\n          if (type === 'next') {\n            return 'Gå til neste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til førre side';\n        },\n        labelRowsPerPage: 'Rader per side:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} av ${count !== -1 ? count : `fleire enn ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} stjerne${value !== 1 ? 'r' : ''}`,\n        emptyLabelText: 'Tom'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Tøm',\n        closeText: 'Lukk',\n        loadingText: 'Lastar inn…',\n        noOptionsText: 'Ingen alternativ',\n        openText: 'Opna'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Lukk'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigasjon for paginering',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Gå til '}side ${page}`;\n          }\n          if (type === 'first') {\n            return 'Gå til første side';\n          }\n          if (type === 'last') {\n            return 'Gå til siste side';\n          }\n          if (type === 'next') {\n            return 'Gå til neste side';\n          }\n          // if (type === 'previous') {\n          return 'Gå til førre side';\n        }\n      }\n    }\n  }\n};\nexport const nlNL = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Pad tonen'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Ga naar eerste pagina';\n          }\n          if (type === 'last') {\n            return 'Ga naar laatste pagina';\n          }\n          if (type === 'next') {\n            return 'Ga naar volgende pagina';\n          }\n          // if (type === 'previous') {\n          return 'Ga naar vorige pagina';\n        },\n        labelRowsPerPage: 'Regels per pagina:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} van ${count !== -1 ? count : `meer dan ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Ster${value !== 1 ? 'ren' : ''}`,\n        emptyLabelText: 'Leeg'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Wissen',\n        closeText: 'Sluiten',\n        loadingText: 'Laden…',\n        noOptionsText: 'Geen opties',\n        openText: 'Openen'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Sluiten'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigatie via paginering',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Ga naar '}pagina ${page}`;\n          }\n          if (type === 'first') {\n            return 'Ga naar eerste pagina';\n          }\n          if (type === 'last') {\n            return 'Ga naar laatste pagina';\n          }\n          if (type === 'next') {\n            return 'Ga naar volgende pagina';\n          }\n          // if (type === 'previous') {\n          return 'Ga naar vorige pagina';\n        }\n      }\n    }\n  }\n};\nexport const plPL = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Pokaż ścieżkę'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Przejdź do pierwszej strony';\n          }\n          if (type === 'last') {\n            return 'Przejdź do ostatniej strony';\n          }\n          if (type === 'next') {\n            return 'Przejdź do następnej strony';\n          }\n          // if (type === 'previous') {\n          return 'Przejdź do poprzedniej strony';\n        },\n        labelRowsPerPage: 'Wierszy na stronę:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} z ${count !== -1 ? count : `ponad ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          let pluralForm = 'gwiazdek';\n          const lastDigit = value % 10;\n          if ((value < 10 || value > 20) && lastDigit > 1 && lastDigit < 5) {\n            pluralForm = 'gwiazdki';\n          } else if (value === 1) {\n            pluralForm = 'gwiazdka';\n          }\n          return `${value} ${pluralForm}`;\n        },\n        emptyLabelText: 'Brak gwiazdek'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Wyczyść',\n        closeText: 'Zamknij',\n        loadingText: 'Ładowanie…',\n        noOptionsText: 'Brak opcji',\n        openText: 'Otwórz'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Zamknij'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Nawigacja podziału na strony',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return selected ? `${page}. strona` : `Przejdź do ${page}. strony`;\n          }\n          if (type === 'first') {\n            return 'Przejdź do pierwszej strony';\n          }\n          if (type === 'last') {\n            return 'Przejdź do ostatniej strony';\n          }\n          if (type === 'next') {\n            return 'Przejdź do następnej strony';\n          }\n          // if (type === 'previous') {\n          return 'Przejdź do poprzedniej strony';\n        }\n      }\n    }\n  }\n};\nexport const ptBR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Mostrar caminho'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Ir para a primeira página';\n          }\n          if (type === 'last') {\n            return 'Ir para a última página';\n          }\n          if (type === 'next') {\n            return 'Ir para a próxima página';\n          }\n          // if (type === 'previous') {\n          return 'Ir para a página anterior';\n        },\n        labelRowsPerPage: 'Linhas por página:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} de ${count !== -1 ? count : `mais de ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Estrela${value !== 1 ? 's' : ''}`,\n        emptyLabelText: 'Vazio'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Limpar',\n        closeText: 'Fechar',\n        loadingText: 'Carregando…',\n        noOptionsText: 'Sem opções',\n        openText: 'Abrir'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Fechar'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navegar pela paginação',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Ir para a '}página ${page}`;\n          }\n          if (type === 'first') {\n            return 'Ir para a primeira página';\n          }\n          if (type === 'last') {\n            return 'Ir para a última página';\n          }\n          if (type === 'next') {\n            return 'Ir para a próxima página';\n          }\n          // if (type === 'previous') {\n          return 'Ir para a página anterior';\n        }\n      }\n    }\n  }\n};\nexport const ptPT = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Mostrar caminho'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Primeira página';\n          }\n          if (type === 'last') {\n            return 'Última página';\n          }\n          if (type === 'next') {\n            return 'Próxima página';\n          }\n          // if (type === 'previous') {\n          return 'Página anterior';\n        },\n        labelRowsPerPage: 'Linhas por página:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} de ${count !== -1 ? count : `mais de ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Estrela${value !== 1 ? 's' : ''}`,\n        emptyLabelText: 'Vazio'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Limpar',\n        closeText: 'Fechar',\n        loadingText: 'A carregar…',\n        noOptionsText: 'Sem opções',\n        openText: 'Abrir'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Fechar'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navegar por páginas',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Ir para a '}página ${page}`;\n          }\n          if (type === 'first') {\n            return 'Primeira página';\n          }\n          if (type === 'last') {\n            return 'Última página';\n          }\n          if (type === 'next') {\n            return 'Próxima página';\n          }\n          // if (type === 'previous') {\n          return 'Página anterior';\n        }\n      }\n    }\n  }\n};\nexport const roRO = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Arată calea'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Mergi la prima pagină';\n          }\n          if (type === 'last') {\n            return 'Mergi la ultima pagină';\n          }\n          if (type === 'next') {\n            return 'Mergi la pagina următoare';\n          }\n          // if (type === 'previous') {\n          return 'Mergi la pagina precedentă';\n        },\n        labelRowsPerPage: 'Rânduri pe pagină:'\n        // labelDisplayedRows: ({ from, to, count }) =>\n        //   `${from}–${to} din ${count !== -1 ? count : `more than ${to}`}`,\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} St${value !== 1 ? 'ele' : 'ea'}`,\n        emptyLabelText: 'Gol'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Șterge',\n        closeText: 'Închide',\n        loadingText: 'Se încarcă…',\n        noOptionsText: 'Nicio opțiune',\n        openText: 'Deschide'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Închide'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigare prin paginare',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Mergi la '}pagina ${page}`;\n          }\n          if (type === 'first') {\n            return 'Mergi la prima pagină';\n          }\n          if (type === 'last') {\n            return 'Mergi la ultima pagină';\n          }\n          if (type === 'next') {\n            return 'Mergi la pagina următoare';\n          }\n          // if (type === 'previous') {\n          return 'Mergi la pagina precedentă';\n        }\n      }\n    }\n  }\n};\n\n// Serbian - Srpski\nexport const srRS = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Pokaži putanju'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Idi na prvu stranicu';\n          }\n          if (type === 'last') {\n            return 'Idi na poslednju stranicu';\n          }\n          if (type === 'next') {\n            return 'Idi na sledeću stranicu';\n          }\n          // if (type === 'previous') {\n          return 'Idi na prethodnu stranicu';\n        },\n        labelRowsPerPage: 'Redova po stranici:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} od ${count !== -1 ? count : `više nego ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          const lastDigit = value % 10;\n          const lastTwoDigits = value % 100;\n          if ([2, 3, 4].includes(lastDigit) && ![12, 13, 14].includes(lastTwoDigits)) {\n            return 'Zvezde';\n          }\n          return 'Zvezda';\n        },\n        emptyLabelText: 'Prazno'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Briši',\n        closeText: 'Zatvori',\n        loadingText: 'Učitavanje…',\n        noOptionsText: 'Nema opcija',\n        openText: 'Otvori'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Zatvori'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigacija po stranicama',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Idi na '}stranicu ${page}`;\n          }\n          if (type === 'first') {\n            return 'Idi na prvu stranicu';\n          }\n          if (type === 'last') {\n            return 'Idi na zadnju stranicu';\n          }\n          if (type === 'next') {\n            return 'Idi na sledeću stranicu';\n          }\n          // if (type === 'previous') {\n          return 'Idi na prethodnu stranicu';\n        }\n      }\n    }\n  }\n};\nexport const ruRU = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Показать полный путь'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перейти на первую страницу';\n          }\n          if (type === 'last') {\n            return 'Перейти на последнюю страницу';\n          }\n          if (type === 'next') {\n            return 'Перейти на следующую страницу';\n          }\n          // if (type === 'previous') {\n          return 'Перейти на предыдущую страницу';\n        },\n        labelRowsPerPage: 'Строк на странице:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} из ${count !== -1 ? count : `более чем ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          let pluralForm = 'Звёзд';\n          const lastDigit = value % 10;\n          if (lastDigit > 1 && lastDigit < 5) {\n            pluralForm = 'Звезды';\n          } else if (lastDigit === 1) {\n            pluralForm = 'Звезда';\n          }\n          return `${value} ${pluralForm}`;\n        },\n        emptyLabelText: 'Рейтинг отсутствует'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Очистить',\n        closeText: 'Закрыть',\n        loadingText: 'Загрузка…',\n        noOptionsText: 'Нет доступных вариантов',\n        openText: 'Открыть'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Закрыть'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Навигация по страницам',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            if (selected) {\n              return `${page} страница`;\n            }\n            return `Перейти на ${page} страницу`;\n          }\n          if (type === 'first') {\n            return 'Перейти на первую страницу';\n          }\n          if (type === 'last') {\n            return 'Перейти на последнюю страницу';\n          }\n          if (type === 'next') {\n            return 'Перейти на следующую страницу';\n          }\n          // if (type === 'previous') {\n          return 'Перейти на предыдущую страницу';\n        }\n      }\n    }\n  }\n};\nexport const siLK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'ගමන් මඟ පෙන්වන්න'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'පළමු පිටුවට යන්න';\n          }\n          if (type === 'last') {\n            return 'අවසාන පිටුවට යන්න';\n          }\n          if (type === 'next') {\n            return 'මීළඟ පිටුවට යන්න';\n          }\n          // if (type === 'previous') {\n          return 'පෙර පිටුවට යන්න';\n        },\n        labelRowsPerPage: 'පිටුවක පේළි:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} දක්වා ${count !== -1 ? count : `${to} ට වැඩි ප්‍රමාණයකින්`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `තරු ${value}`,\n        emptyLabelText: 'හිස්'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'මකන්න',\n        closeText: 'වසන්න',\n        loadingText: 'නැංවෙමින්…',\n        noOptionsText: 'විකල්ප නැත',\n        openText: 'විවෘත කරන්න'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'වසන්න'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'පිටු අතර සංචරණය',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `පිටුව ${page} ${selected ? '' : 'ට යන්න'}`;\n          }\n          if (type === 'first') {\n            return 'පළමු පිටුවට යන්න';\n          }\n          if (type === 'last') {\n            return 'අවසාන පිටුවට යන්න';\n          }\n          if (type === 'next') {\n            return 'මීළඟ පිටුවට යන්න';\n          }\n          // if (type === 'previous') {\n          return 'පෙර පිටුවට යන්න';\n        }\n      }\n    }\n  }\n};\nexport const skSK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Ukázať cestu '\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Ísť na prvú stránku';\n          }\n          if (type === 'last') {\n            return 'Ísť na poslednú stránku';\n          }\n          if (type === 'next') {\n            return 'Ísť na ďaľšiu stránku';\n          }\n          // if (type === 'previous') {\n          return 'Ísť na predchádzajúcu stránku';\n        },\n        labelRowsPerPage: 'Riadkov na stránke:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} z ${count !== -1 ? count : `viac ako ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          if (value === 1) {\n            return `${value} hviezdička`;\n          }\n          if (value >= 2 && value <= 4) {\n            return `${value} hviezdičky`;\n          }\n          return `${value} hviezdičiek`;\n        },\n        emptyLabelText: 'Prázdne'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Vymazať',\n        closeText: 'Zavrieť',\n        loadingText: 'Načítanie…',\n        noOptionsText: 'Žiadne možnosti',\n        openText: 'Otvoriť'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Zavrieť'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Navigácia stránkovanim',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Ísť na '}stránku ${page}`;\n          }\n          if (type === 'first') {\n            return 'Ísť na prvú stránku';\n          }\n          if (type === 'last') {\n            return 'Ísť na poslednú stránku';\n          }\n          if (type === 'next') {\n            return 'Ísť na ďaľšiu stránku';\n          }\n          // if (type === 'previous') {\n          return 'Ísť na predchádzajúcu stránku';\n        }\n      }\n    }\n  }\n};\nexport const svSE = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Visa sökväg'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Gå till första sidan';\n          }\n          if (type === 'last') {\n            return 'Gå till sista sidan';\n          }\n          if (type === 'next') {\n            return 'Gå till nästa sida';\n          }\n          // if (type === 'previous') {\n          return 'Gå till föregående sida';\n        },\n        labelRowsPerPage: 'Rader per sida:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} av ${count !== -1 ? count : `fler än ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ${value !== 1 ? 'Stjärnor' : 'Stjärna'}`,\n        emptyLabelText: 'Tom'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Rensa',\n        closeText: 'Stäng',\n        loadingText: 'Laddar…',\n        noOptionsText: 'Inga alternativ',\n        openText: 'Öppna'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Stäng'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Sidnavigering',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Gå till '}sida ${page}`;\n          }\n          if (type === 'first') {\n            return 'Gå till första sidan';\n          }\n          if (type === 'last') {\n            return 'Gå till sista sidan';\n          }\n          if (type === 'next') {\n            return 'Gå till nästa sida';\n          }\n          // if (type === 'previous') {\n          return 'Gå till föregående sida';\n        }\n      }\n    }\n  }\n};\nexport const thTH = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'แสดงเส้นทาง'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'ไปที่หน้าแรก';\n          }\n          if (type === 'last') {\n            return 'ไปที่หน้าสุดท้าย';\n          }\n          if (type === 'next') {\n            return 'ไปที่หน้าถัดไป';\n          }\n          // if (type === 'previous') {\n          return 'ไปที่หน้าก่อน';\n        },\n        labelRowsPerPage: 'จำนวนแถวต่อหน้า:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} จาก ${count !== -1 ? count : `มากกว่า ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ดาว`,\n        emptyLabelText: 'ว่างเปล่า'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'เคลียร์',\n        closeText: 'ปิด',\n        loadingText: 'กำลังโหลด…',\n        noOptionsText: 'ไม่มีตัวเลือก',\n        openText: 'เปิด'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'ปิด'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': '',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'ไปที่'}หน้า ${page}`;\n          }\n          if (type === 'first') {\n            return 'ไปที่หน้าแรก';\n          }\n          if (type === 'last') {\n            return 'ไปที่หน้าสุดท้าย';\n          }\n          if (type === 'next') {\n            return 'ไปที่หน้าถัดไป';\n          }\n          // if (type === 'previous') {\n          return 'ไปที่หน้าก่อน';\n        }\n      }\n    }\n  }\n};\nexport const trTR = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Yolu göster'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'İlk sayfaya git';\n          }\n          if (type === 'last') {\n            return 'Son sayfaya git';\n          }\n          if (type === 'next') {\n            return 'Sonraki sayfaya git';\n          }\n          // if (type === 'previous') {\n          return 'Önceki sayfaya git';\n        },\n        labelRowsPerPage: 'Sayfa başına satır:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} / ${count !== -1 ? count : `${to}'den fazla`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Yıldız`,\n        emptyLabelText: 'Boş'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Temizle',\n        closeText: 'Kapat',\n        loadingText: 'Yükleniyor…',\n        noOptionsText: 'Seçenek yok',\n        openText: 'Aç'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Kapat'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Sayfa navigasyonu',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${page}. ${selected ? 'sayfa' : 'sayfaya git'}`;\n          }\n          if (type === 'first') {\n            return 'İlk sayfaya git';\n          }\n          if (type === 'last') {\n            return 'Son sayfaya git';\n          }\n          if (type === 'next') {\n            return 'Sonraki sayfaya git';\n          }\n          // if (type === 'previous') {\n          return 'Önceki sayfaya git';\n        }\n      }\n    }\n  }\n};\n\n// Tagalog-Tagalog\nexport const tlTL = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Ipakita ang landas'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Pumunta sa unang pahina';\n          }\n          if (type === 'last') {\n            return 'Pumunta sa huling pahina';\n          }\n          if (type === 'next') {\n            return 'Pumunta sa susunod na pahina';\n          }\n          // if (type === 'previous') {\n          return 'Pumunta sa nakaraang pahina';\n        },\n        labelRowsPerPage: 'Mga hilera bawat pahina:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} ng ${count !== -1 ? count : `higit sa ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} Bituin${value !== 1 ? 's' : ''}`,\n        emptyLabelText: 'Walang laman'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Maaliwalas',\n        closeText: 'Isara',\n        loadingText: 'Naglo-load…',\n        noOptionsText: 'Walang mga pagpipilian',\n        openText: 'Bukas'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Isara'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Sayfa navigasyonu',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Pumunta sa'}pahina ${page}`;\n          }\n          if (type === 'first') {\n            return 'Pumunta sa unang pahina';\n          }\n          if (type === 'last') {\n            return 'Pumunta sa huling pahina';\n          }\n          if (type === 'next') {\n            return 'Pumunta sa susunod na pahina';\n          }\n          // if (type === 'previous') {\n          return 'Pumunta sa nakaraang pahina';\n        }\n      }\n    }\n  }\n};\nexport const ukUA = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Показати шлях сторінок'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перейти на першу сторінку';\n          }\n          if (type === 'last') {\n            return 'Перейти на останню сторінку';\n          }\n          if (type === 'next') {\n            return 'Перейти на наступну сторінку';\n          }\n          // if (type === 'previous') {\n          return 'Перейти на попередню сторінку';\n        },\n        labelRowsPerPage: 'Рядків на сторінці:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} з ${count !== -1 ? count : `понад ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => {\n          let pluralForm = 'Зірок';\n          const lastDigit = value % 10;\n          if (lastDigit > 1 && lastDigit < 5) {\n            pluralForm = 'Зірки';\n          } else if (lastDigit === 1) {\n            pluralForm = 'Зірка';\n          }\n          return `${value} ${pluralForm}`;\n        },\n        emptyLabelText: 'Рейтинг відсутній'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Очистити',\n        closeText: 'Згорнути',\n        loadingText: 'Завантаження…',\n        noOptionsText: 'Немає варіантів',\n        openText: 'Розгорнути'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Згорнути'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Навігація сторінками',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Перейти на '}сторінку ${page}`;\n          }\n          if (type === 'first') {\n            return 'Перейти на першу сторінку';\n          }\n          if (type === 'last') {\n            return 'Перейти на останню сторінку';\n          }\n          if (type === 'next') {\n            return 'Перейти на наступну сторінку';\n          }\n          // if (type === 'previous') {\n          return 'Перейти на попередню сторінку';\n        }\n      }\n    }\n  }\n};\nexport const urPK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'راستہ دکھائیں'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'پہلے صفحے پر جائیں';\n          }\n          if (type === 'last') {\n            return 'آخری صفحے پر جائیں';\n          }\n          if (type === 'next') {\n            return 'اگلے صفحے پر جائیں';\n          }\n          // if (type === 'previous') {\n          return 'پچھلے صفحے پر جائیں';\n        },\n        labelRowsPerPage: 'ایک صفحے پر قطاریں:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${count !== -1 ? `${count} میں سے` : `${to} سے ذیادہ میں سے`} ${from} سے ${to} قطاریں`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} ستار${value !== 1 ? 'ے' : 'ہ'}`,\n        emptyLabelText: 'خالی'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'کلئیر',\n        closeText: 'بند کریں',\n        loadingText: 'لوڈ ہو رہا ہے۔۔۔',\n        noOptionsText: 'کوئی آپشن نہیں',\n        openText: 'کھولیں'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'بند کریں'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'صفحات کی ترتیب',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `صفحہ نمبر ${page}${selected ? '' : ' پر جائیں'}`;\n          }\n          if (type === 'first') {\n            return 'پہلے صفحے پر جائیں';\n          }\n          if (type === 'last') {\n            return 'آخری صفحے پر جائیں';\n          }\n          if (type === 'next') {\n            return 'اگلے صفحے پر جائیں';\n          }\n          // if (type === 'previous') {\n          return 'پچھلے صفحے پر جائیں';\n        }\n      }\n    }\n  }\n};\nexport const viVN = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: 'Mở ra'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Tới trang đầu tiên';\n          }\n          if (type === 'last') {\n            return 'Tới trang cuối cùng';\n          }\n          if (type === 'next') {\n            return 'Tới trang tiếp theo';\n          }\n          // if (type === 'previous') {\n          return 'Về trang trước đó';\n        },\n        labelRowsPerPage: 'Số hàng mỗi trang:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} trong ${count !== -1 ? count : `nhiều hơn ${to}`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} sao`,\n        emptyLabelText: 'Không có dữ liệu'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: 'Xóa',\n        closeText: 'Đóng',\n        loadingText: 'Đang tải…',\n        noOptionsText: 'Không có lựa chọn nào',\n        openText: 'Mở'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: 'Đóng'\n      }\n    },\n    MuiPagination: {\n      defaultProps: {\n        'aria-label': 'Thanh điều khiển trang',\n        getItemAriaLabel: (type, page, selected) => {\n          if (type === 'page') {\n            return `${selected ? '' : 'Tới '}trang ${page}`;\n          }\n          if (type === 'first') {\n            return 'Tới trang đầu tiên';\n          }\n          if (type === 'last') {\n            return 'Tới trang cuối cùng';\n          }\n          if (type === 'next') {\n            return 'Tới trang tiếp theo';\n          }\n          // if (type === 'previous') {\n          return 'Về trang trước đó';\n        }\n      }\n    }\n  }\n};\nexport const zhCN = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: '展开'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return '第一页';\n          }\n          if (type === 'last') {\n            return '最后一页';\n          }\n          if (type === 'next') {\n            return '下一页';\n          }\n          return '上一页';\n        },\n        labelRowsPerPage: '每页行数:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `第 ${from} 条到第 ${to} 条，${count !== -1 ? `共 ${count} 条` : `至少 ${to} 条`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} 颗星`,\n        emptyLabelText: '无标签'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: '清空',\n        closeText: '关闭',\n        loadingText: '加载中……',\n        noOptionsText: '没有可用选项',\n        openText: '打开'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: '关闭'\n      }\n    }\n  }\n};\nexport const zhHK = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: '展開'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return '第一頁';\n          }\n          if (type === 'last') {\n            return '最後一頁';\n          }\n          if (type === 'next') {\n            return '下一頁';\n          }\n          return '上一頁';\n        },\n        labelRowsPerPage: '每頁行數:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `第 ${from} 項至第 ${to} 項，${count !== -1 ? `共 ${count} 項` : `超過 ${to} 項`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} 粒星`,\n        emptyLabelText: '無標籤'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: '清除',\n        closeText: '關閉',\n        loadingText: '載入中……',\n        noOptionsText: '沒有可用選項',\n        openText: '開啟'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: '關閉'\n      }\n    }\n  }\n};\nexport const zhTW = {\n  components: {\n    MuiBreadcrumbs: {\n      defaultProps: {\n        expandText: '展開'\n      }\n    },\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return '第一頁';\n          }\n          if (type === 'last') {\n            return '最後一頁';\n          }\n          if (type === 'next') {\n            return '下一頁';\n          }\n          return '上一頁';\n        },\n        labelRowsPerPage: '每頁數量:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from} ~ ${to} / ${count !== -1 ? count : `${to} 以上`}`\n      }\n    },\n    MuiRating: {\n      defaultProps: {\n        getLabelText: value => `${value} 顆星`,\n        emptyLabelText: '無標籤'\n      }\n    },\n    MuiAutocomplete: {\n      defaultProps: {\n        clearText: '清空',\n        closeText: '關閉',\n        loadingText: '載入中…',\n        noOptionsText: '沒有可用選項',\n        openText: '打開'\n      }\n    },\n    MuiAlert: {\n      defaultProps: {\n        closeText: '關閉'\n      }\n    }\n  }\n};"], "mappings": ";;;AAAO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,MAAM;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,MAAM,UAAU,IAAI,OAAO,GAAG;AAAA,QAC7D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,KAAK,MAAM,IAAI,GAAG,WAAW,KAAK,KAAK;AAAA,UACnE;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,SAAS,MAAM;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,YAAY,SAAS,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,SAAS,MAAM;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,eAAe,SAAS,IAAI;AAAA,UACxD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,SAAS,MAAM;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,YAAY,SAAS,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,aAAa;AACjB,gBAAM,YAAY,QAAQ;AAC1B,cAAI,YAAY,KAAK,YAAY,GAAG;AAClC,yBAAa;AAAA,UACf;AACA,iBAAO,GAAG,KAAK,IAAI,UAAU;AAAA,QAC/B;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,IAAI,IAAI,WAAW,WAAW,cAAc;AAAA,UACxD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,YAAY;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,MAAM,UAAU,IAAI;AAAA,UAChD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,aAAa;AACjB,gBAAM,YAAY,QAAQ;AAC1B,cAAI,YAAY,KAAK,YAAY,MAAM,QAAQ,MAAM,QAAQ,KAAK;AAChE,yBAAa;AAAA,UACf,WAAW,cAAc,KAAK,QAAQ,QAAQ,IAAI;AAChD,yBAAa;AAAA,UACf;AACA,iBAAO,GAAG,KAAK,IAAI,UAAU;AAAA,QAC/B;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,gBAAI,UAAU;AACZ,qBAAO,GAAG,IAAI;AAAA,YAChB;AACA,mBAAO,eAAe,IAAI;AAAA,UAC5B;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,SAAS,UAAU,IAAI,MAAM,GAAG;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,MAAM,YAAY,IAAI;AAAA,UAClD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMV,oBAAoB;AAAA,MAClB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcZ,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,cAAc,UAAU;AAAA,QACzE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,UAAU,GAAG;AACf,mBAAO,GAAG,KAAK;AAAA,UACjB;AACA,cAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,mBAAO,GAAG,KAAK;AAAA,UACjB;AACA,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,GAAG,IAAI;AAAA,UAC5C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,UAAU,UAAU,IAAI,OAAO,EAAE;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,QAAQ,QAAQ,IAAI;AAAA,UAChD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,WAAW,OAAO;AAAA,QACnE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,SAAS,IAAI;AAAA,UACnD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAGA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,UAAU,UAAU,IAAI,MAAM,EAAE;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,WAAW,UAAU,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAGA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2DpB;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,UAAU,EAAE,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,YAAY,UAAU,IAAI,MAAM,EAAE;AAAA,QACjE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,UAAU,IAAI;AAAA,UACpD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,cAAc,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,QAAQ,UAAU,IAAI,MAAM,EAAE;AAAA,QAC7D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,OAAO,YAAY,IAAI;AAAA,UACnD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,SAAS,IAAI;AAAA,UACnD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,gBAAgB,EAAE,EAAE;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,QAAQ,UAAU,IAAI,OAAO,GAAG;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,SAAS,cAAc,IAAI,IAAI;AAAA,UACtD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,UAAU,UAAU,IAAI,MAAM,EAAE;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,aAAa,QAAQ,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,SAAS,UAAU,KAAK,QAAQ,UAAU,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,QAAQ,UAAU,IAAI,OAAO,EAAE;AAAA,QAC9D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,IAAI,QAAQ,IAAI;AAAA,UAC5C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ,KAAK;AAAA,MACtD;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,OAAO,UAAU,IAAI,MAAM,GAAG;AAAA,QAC7D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,SAAS,IAAI,IAAI,WAAW,KAAK,UAAU;AAAA,UACpD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,gBAAM,gBAAgB,QAAQ;AAC9B,cAAI,CAAC,GAAG,GAAG,CAAC,EAAE,SAAS,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,SAAS,aAAa,GAAG;AAC1E,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,YAAY,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,IAAI,UAAU,WAAW,KAAK,IAAI;AAAA,UAC9C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMV,oBAAoB;AAAA,MAClB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcZ,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMV,oBAAoB;AAAA,MAClB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcZ,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA;AAAA,MAEjC;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,YAAY,UAAU;AAAA,QACvE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,SAAS,aAAa,IAAI,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,UAAU,EAAE,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,SAAS,UAAU,IAAI,MAAM,GAAG;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,WAAW,UAAU,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,IAAI;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,IAAI,KAAK;AAAA,QAChC,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,MAAM,IAAI,GAAG,WAAW,KAAK,GAAG;AAAA,UACzC;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,MAAM,EAAE,OAAO,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,SAAS,UAAU,IAAI,KAAK,EAAE;AAAA,QAC7D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,KAAK,SAAS,IAAI;AAAA,UAC9C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,MAAM;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,IAAI,UAAU,WAAW,KAAK,MAAM;AAAA,UAChD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,QAAQ;AAAA,EACnB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,eAAe,SAAS;AAAA,QACzE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,UAAU,IAAI;AAAA,UACpD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,SAAS;AAAA,EACpB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,cAAc,EAAE,EAAE;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,OAAO,SAAS,IAAI;AAAA,UAChD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,UAAU,KAAK,QAAQ,IAAI,EAAE,EAAE,oBAAoB,IAAI,IAAI,EAAE;AAAA,MACxE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,gBAAI,UAAU;AACZ,qBAAO,GAAG,IAAI;AAAA,YAChB;AACA,mBAAO,GAAG,IAAI;AAAA,UAChB;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,iBAAO,GAAG,KAAK,SAAS,cAAc,IAAI,MAAM,GAAG;AAAA,QACrD;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,YAAY,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,cAAc,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,iBAAO,GAAG,KAAK,aAAa,cAAc,IAAI,MAAM,EAAE;AAAA,QACxD;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,aAAa,IAAI;AAAA,UACvD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,aAAa,UAAU,KAAK,QAAQ,kBAAkB,EAAE,EAAE;AAAA,MAC/E;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,iBAAO,GAAG,KAAK,WAAW,cAAc,IAAI,MAAM,EAAE;AAAA,QACtD;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,WAAW,cAAc,IAAI;AAAA,UACzD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,QAAQ,EAAE,EAAE;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,iBAAO,GAAG,KAAK,QAAQ,cAAc,IAAI,MAAM,EAAE;AAAA,QACnD;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,aAAa,SAAS,IAAI;AAAA,UACtD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,WAAW,UAAU,IAAI,MAAM,EAAE;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,QAAQ,IAAI;AAAA,UACjD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,cAAc,EAAE,EAAE;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,WAAW,UAAU,IAAI,MAAM,EAAE;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,QAAQ,IAAI;AAAA,UACjD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,QAAQ,UAAU,IAAI,QAAQ,EAAE;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,UAAU,IAAI;AAAA,UACpD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,SAAS,EAAE,EAAE;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,aAAa;AACjB,gBAAM,YAAY,QAAQ;AAC1B,eAAK,QAAQ,MAAM,QAAQ,OAAO,YAAY,KAAK,YAAY,GAAG;AAChE,yBAAa;AAAA,UACf,WAAW,UAAU,GAAG;AACtB,yBAAa;AAAA,UACf;AACA,iBAAO,GAAG,KAAK,IAAI,UAAU;AAAA,QAC/B;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,WAAW,GAAG,IAAI,aAAa,cAAc,IAAI;AAAA,UAC1D;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,WAAW,UAAU,IAAI,MAAM,EAAE;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,YAAY,UAAU,IAAI;AAAA,UACtD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,WAAW,UAAU,IAAI,MAAM,EAAE;AAAA,QAChE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,YAAY,UAAU,IAAI;AAAA,UACtD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA;AAAA;AAAA,MAGpB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,MAAM,UAAU,IAAI,QAAQ,IAAI;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,WAAW,UAAU,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,gBAAM,YAAY,QAAQ;AAC1B,gBAAM,gBAAgB,QAAQ;AAC9B,cAAI,CAAC,GAAG,GAAG,CAAC,EAAE,SAAS,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,SAAS,aAAa,GAAG;AAC1E,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,YAAY,IAAI;AAAA,UACrD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,aAAa;AACjB,gBAAM,YAAY,QAAQ;AAC1B,cAAI,YAAY,KAAK,YAAY,GAAG;AAClC,yBAAa;AAAA,UACf,WAAW,cAAc,GAAG;AAC1B,yBAAa;AAAA,UACf;AACA,iBAAO,GAAG,KAAK,IAAI,UAAU;AAAA,QAC/B;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,gBAAI,UAAU;AACZ,qBAAO,GAAG,IAAI;AAAA,YAChB;AACA,mBAAO,cAAc,IAAI;AAAA,UAC3B;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,UAAU,UAAU,KAAK,QAAQ,GAAG,EAAE,sBAAsB;AAAA,MACjF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,OAAO,KAAK;AAAA,QACnC,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,SAAS,IAAI,IAAI,WAAW,KAAK,QAAQ;AAAA,UAClD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,UAAU,GAAG;AACf,mBAAO,GAAG,KAAK;AAAA,UACjB;AACA,cAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,mBAAO,GAAG,KAAK;AAAA,UACjB;AACA,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,SAAS,WAAW,IAAI;AAAA,UACpD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,aAAa,SAAS;AAAA,QACvE,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,UAAU,QAAQ,IAAI;AAAA,UAClD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,WAAW,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,OAAO,QAAQ,IAAI;AAAA,UAC/C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,YAAY;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,IAAI,KAAK,WAAW,UAAU,aAAa;AAAA,UACvD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,YAAY,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,UAAU,UAAU,IAAI,MAAM,EAAE;AAAA,QAC/D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,YAAY,UAAU,IAAI;AAAA,UACtD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,SAAS,EAAE,EAAE;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS;AACrB,cAAI,aAAa;AACjB,gBAAM,YAAY,QAAQ;AAC1B,cAAI,YAAY,KAAK,YAAY,GAAG;AAClC,yBAAa;AAAA,UACf,WAAW,cAAc,GAAG;AAC1B,yBAAa;AAAA,UACf;AACA,iBAAO,GAAG,KAAK,IAAI,UAAU;AAAA,QAC/B;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,aAAa,YAAY,IAAI;AAAA,UACzD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,UAAU,KAAK,GAAG,KAAK,YAAY,GAAG,EAAE,kBAAkB,IAAI,IAAI,OAAO,EAAE;AAAA,MACtF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK,QAAQ,UAAU,IAAI,MAAM,GAAG;AAAA,QAC9D,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,aAAa,IAAI,GAAG,WAAW,KAAK,WAAW;AAAA,UACxD;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,UAAU,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACvE;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,cAAc;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB,CAAC,MAAM,MAAM,aAAa;AAC1C,cAAI,SAAS,QAAQ;AACnB,mBAAO,GAAG,WAAW,KAAK,MAAM,SAAS,IAAI;AAAA,UAC/C;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,KAAK,IAAI,QAAQ,EAAE,MAAM,UAAU,KAAK,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,KAAK,IAAI,QAAQ,EAAE,MAAM,UAAU,KAAK,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,OAAO;AAAA,EAClB,YAAY;AAAA,IACV,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,MAAM,EAAE,MAAM,UAAU,KAAK,QAAQ,GAAG,EAAE,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,QACZ,cAAc,WAAS,GAAG,KAAK;AAAA,QAC/B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,cAAc;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;", "names": []}