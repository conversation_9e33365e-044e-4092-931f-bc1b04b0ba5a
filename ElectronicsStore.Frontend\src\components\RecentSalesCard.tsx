import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  Box,
  useTheme,
} from '@mui/material';
import {
  Receipt as ReceiptIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { RecentSale } from '../types';
// import { formatDistanceToNow } from 'date-fns';
// import { ar } from 'date-fns/locale';

interface RecentSalesCardProps {
  sales: RecentSale[];
  title?: string;
}

const RecentSalesCard: React.FC<RecentSalesCardProps> = ({
  sales,
  title = 'آخر المبيعات',
}) => {
  const theme = useTheme();

  const formatCurrency = (amount: number): string => {
    return `${amount.toLocaleString('ar-SA')} ر.س`;
  };

  const formatTimeAgo = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

      if (diffInHours < 1) {
        return 'منذ قليل';
      } else if (diffInHours < 24) {
        return `منذ ${diffInHours} ساعة`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
      }
    } catch {
      return 'منذ قليل';
    }
  };

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'نقدي':
        return 'success';
      case 'فيزا':
      case 'ماستركارد':
        return 'primary';
      case 'مدى':
        return 'secondary';
      case 'تحويل بنكي':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {title}
          </Typography>
        }
        avatar={
          <Avatar sx={{ bgcolor: theme.palette.info.main }}>
            <ReceiptIcon />
          </Avatar>
        }
      />
      <CardContent sx={{ pt: 0, px: 0 }}>
        <TableContainer sx={{ maxHeight: 400 }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>رقم الفاتورة</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>العميل</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>المبلغ</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>طريقة الدفع</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>الوقت</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sales.map((sale) => (
                <TableRow
                  key={sale.id}
                  sx={{
                    '&:hover': {
                      bgcolor: theme.palette.action.hover,
                    },
                  }}
                >
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          bgcolor: theme.palette.primary.main,
                          mr: 1,
                          fontSize: '0.75rem',
                        }}
                      >
                        <ReceiptIcon fontSize="small" />
                      </Avatar>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {sale.invoiceNumber}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {sale.itemsCount} عنصر
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar
                        sx={{
                          width: 28,
                          height: 28,
                          bgcolor: theme.palette.secondary.main,
                          mr: 1,
                          fontSize: '0.75rem',
                        }}
                      >
                        <PersonIcon fontSize="small" />
                      </Avatar>
                      <Typography variant="body2">
                        {sale.customerName}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        color: theme.palette.success.main,
                        direction: 'ltr',
                        textAlign: 'right',
                      }}
                    >
                      {formatCurrency(sale.totalAmount)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={sale.paymentMethod}
                      size="small"
                      color={getPaymentMethodColor(sale.paymentMethod) as any}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimeAgo(sale.createdAt)}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
};

export default RecentSalesCard;
