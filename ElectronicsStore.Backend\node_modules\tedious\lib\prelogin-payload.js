"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _sprintfJs = require("sprintf-js");
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const optionBufferSize = 20;
const TOKEN = {
  VERSION: 0x00,
  ENCRYPTION: 0x01,
  INSTOPT: 0x02,
  THREADID: 0x03,
  MARS: 0x04,
  FEDAUTHREQUIRED: 0x06,
  TERMINATOR: 0xFF
};
const ENCRYPT = {
  OFF: 0x00,
  ON: 0x01,
  NOT_SUP: 0x02,
  REQ: 0x03
};
const encryptByValue = {};
for (const name in ENCRYPT) {
  const value = ENCRYPT[name];
  encryptByValue[value] = name;
}
const MARS = {
  OFF: 0x00,
  ON: 0x01
};
const marsByValue = {};
for (const name in MARS) {
  const value = MARS[name];
  marsByValue[value] = name;
}
/*
  s2.2.6.4
 */
class PreloginPayload {
  constructor(bufferOrOptions = {
    encrypt: false,
    version: {
      major: 0,
      minor: 0,
      build: 0,
      subbuild: 0
    }
  }) {
    if (bufferOrOptions instanceof Buffer) {
      this.data = bufferOrOptions;
      this.options = {
        encrypt: false,
        version: {
          major: 0,
          minor: 0,
          build: 0,
          subbuild: 0
        }
      };
    } else {
      this.options = bufferOrOptions;
      this.createOptions();
    }
    this.extractOptions();
  }
  createOptions() {
    const options = [this.createVersionOption(), this.createEncryptionOption(), this.createInstanceOption(), this.createThreadIdOption(), this.createMarsOption(), this.createFedAuthOption()];
    let length = 0;
    for (let i = 0, len = options.length; i < len; i++) {
      const option = options[i];
      length += 5 + option.data.length;
    }
    length++; // terminator
    this.data = Buffer.alloc(length, 0);
    let optionOffset = 0;
    let optionDataOffset = 5 * options.length + 1;
    for (let j = 0, len = options.length; j < len; j++) {
      const option = options[j];
      this.data.writeUInt8(option.token, optionOffset + 0);
      this.data.writeUInt16BE(optionDataOffset, optionOffset + 1);
      this.data.writeUInt16BE(option.data.length, optionOffset + 3);
      optionOffset += 5;
      option.data.copy(this.data, optionDataOffset);
      optionDataOffset += option.data.length;
    }
    this.data.writeUInt8(TOKEN.TERMINATOR, optionOffset);
  }
  createVersionOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    buffer.writeUInt8(this.options.version.major);
    buffer.writeUInt8(this.options.version.minor);
    buffer.writeUInt16BE(this.options.version.build);
    buffer.writeUInt16BE(this.options.version.subbuild);
    return {
      token: TOKEN.VERSION,
      data: buffer.data
    };
  }
  createEncryptionOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    if (this.options.encrypt) {
      buffer.writeUInt8(ENCRYPT.ON);
    } else {
      buffer.writeUInt8(ENCRYPT.NOT_SUP);
    }
    return {
      token: TOKEN.ENCRYPTION,
      data: buffer.data
    };
  }
  createInstanceOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    buffer.writeUInt8(0x00);
    return {
      token: TOKEN.INSTOPT,
      data: buffer.data
    };
  }
  createThreadIdOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    buffer.writeUInt32BE(0x00);
    return {
      token: TOKEN.THREADID,
      data: buffer.data
    };
  }
  createMarsOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    buffer.writeUInt8(MARS.OFF);
    return {
      token: TOKEN.MARS,
      data: buffer.data
    };
  }
  createFedAuthOption() {
    const buffer = new _writableTrackingBuffer.default(optionBufferSize);
    buffer.writeUInt8(0x01);
    return {
      token: TOKEN.FEDAUTHREQUIRED,
      data: buffer.data
    };
  }
  extractOptions() {
    let offset = 0;
    while (this.data[offset] !== TOKEN.TERMINATOR) {
      let dataOffset = this.data.readUInt16BE(offset + 1);
      const dataLength = this.data.readUInt16BE(offset + 3);
      switch (this.data[offset]) {
        case TOKEN.VERSION:
          this.extractVersion(dataOffset);
          break;
        case TOKEN.ENCRYPTION:
          this.extractEncryption(dataOffset);
          break;
        case TOKEN.INSTOPT:
          this.extractInstance(dataOffset);
          break;
        case TOKEN.THREADID:
          if (dataLength > 0) {
            this.extractThreadId(dataOffset);
          }
          break;
        case TOKEN.MARS:
          this.extractMars(dataOffset);
          break;
        case TOKEN.FEDAUTHREQUIRED:
          this.extractFedAuth(dataOffset);
          break;
      }
      offset += 5;
      dataOffset += dataLength;
    }
  }
  extractVersion(offset) {
    this.version = {
      major: this.data.readUInt8(offset + 0),
      minor: this.data.readUInt8(offset + 1),
      build: this.data.readUInt16BE(offset + 2),
      subbuild: this.data.readUInt16BE(offset + 4)
    };
  }
  extractEncryption(offset) {
    this.encryption = this.data.readUInt8(offset);
    this.encryptionString = encryptByValue[this.encryption];
  }
  extractInstance(offset) {
    this.instance = this.data.readUInt8(offset);
  }
  extractThreadId(offset) {
    this.threadId = this.data.readUInt32BE(offset);
  }
  extractMars(offset) {
    this.mars = this.data.readUInt8(offset);
    this.marsString = marsByValue[this.mars];
  }
  extractFedAuth(offset) {
    this.fedAuthRequired = this.data.readUInt8(offset);
  }
  toString(indent = '') {
    return indent + 'PreLogin - ' + (0, _sprintfJs.sprintf)('version:%d.%d.%d.%d, encryption:0x%02X(%s), instopt:0x%02X, threadId:0x%08X, mars:0x%02X(%s)', this.version.major, this.version.minor, this.version.build, this.version.subbuild, this.encryption ? this.encryption : 0, this.encryptionString ? this.encryptionString : '', this.instance ? this.instance : 0, this.threadId ? this.threadId : 0, this.mars ? this.mars : 0, this.marsString ? this.marsString : '');
  }
}
var _default = PreloginPayload;
exports.default = _default;
module.exports = PreloginPayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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