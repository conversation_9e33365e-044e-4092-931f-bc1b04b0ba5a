using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class ProductService : IProductService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ProductDto?> GetProductByIdAsync(int id)
        {
            var product = await _unitOfWork.Products.GetByIdWithIncludeAsync(id, 
                p => p.Category, p => p.Supplier);
            
            if (product == null)
                return null;

            var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(id);
            var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(id);

            return MapToProductDto(product, currentStock, inventoryValue);
        }

        public async Task<ProductDto?> GetProductByBarcodeAsync(string barcode)
        {
            var product = await _unitOfWork.Products.GetByBarcodeAsync(barcode);
            if (product == null)
                return null;

            var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
            var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);

            return MapToProductDto(product, currentStock, inventoryValue);
        }

        public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
        {
            var products = await _unitOfWork.Products.GetProductsWithDetailsAsync();
            var productDtos = new List<ProductDto>();

            foreach (var product in products)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return productDtos;
        }

        public async Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto, int userId)
        {
            // Validation
            if (await _unitOfWork.Categories.GetByIdAsync(createProductDto.CategoryId) == null)
                throw new InvalidOperationException($"Category with ID {createProductDto.CategoryId} not found.");

            if (createProductDto.SupplierId.HasValue && 
                await _unitOfWork.Suppliers.GetByIdAsync(createProductDto.SupplierId.Value) == null)
                throw new InvalidOperationException($"Supplier with ID {createProductDto.SupplierId} not found.");

            if (!string.IsNullOrEmpty(createProductDto.Barcode) && 
                await _unitOfWork.Products.BarcodeExistsAsync(createProductDto.Barcode))
                throw new InvalidOperationException($"Barcode '{createProductDto.Barcode}' already exists.");

            if (createProductDto.DefaultSellingPrice < createProductDto.MinSellingPrice)
                throw new InvalidOperationException("Default selling price cannot be less than minimum selling price.");

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                var product = new Product
                {
                    Name = createProductDto.Name,
                    Barcode = createProductDto.Barcode,
                    CategoryId = createProductDto.CategoryId,
                    SupplierId = createProductDto.SupplierId,
                    DefaultCostPrice = createProductDto.DefaultCostPrice,
                    DefaultSellingPrice = createProductDto.DefaultSellingPrice,
                    MinSellingPrice = createProductDto.MinSellingPrice,
                    Description = createProductDto.Description,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.Products.AddAsync(product);
                await _unitOfWork.SaveChangesAsync();

                // Add initial stock if specified
                if (createProductDto.InitialQuantity > 0)
                {
                    var inventoryLog = new InventoryLog
                    {
                        ProductId = product.Id,
                        MovementType = "adjust",
                        Quantity = createProductDto.InitialQuantity,
                        UnitCost = createProductDto.DefaultCostPrice,
                        ReferenceTable = "initial_stock",
                        ReferenceId = product.Id,
                        Note = "Initial stock entry",
                        UserId = userId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Inventory.AddAsync(inventoryLog);
                    await _unitOfWork.SaveChangesAsync();
                }

                await _unitOfWork.CommitTransactionAsync();

                // Get the created product with details
                var createdProduct = await _unitOfWork.Products.GetByIdWithIncludeAsync(product.Id, 
                    p => p.Category, p => p.Supplier);
                
                return MapToProductDto(createdProduct!, createProductDto.InitialQuantity, 
                    createProductDto.InitialQuantity * createProductDto.DefaultCostPrice);
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<ProductDto> UpdateProductAsync(UpdateProductDto updateProductDto)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(updateProductDto.Id);
            if (product == null)
                throw new InvalidOperationException($"Product with ID {updateProductDto.Id} not found.");

            // Validation
            if (await _unitOfWork.Categories.GetByIdAsync(updateProductDto.CategoryId) == null)
                throw new InvalidOperationException($"Category with ID {updateProductDto.CategoryId} not found.");

            if (updateProductDto.SupplierId.HasValue && 
                await _unitOfWork.Suppliers.GetByIdAsync(updateProductDto.SupplierId.Value) == null)
                throw new InvalidOperationException($"Supplier with ID {updateProductDto.SupplierId} not found.");

            if (!string.IsNullOrEmpty(updateProductDto.Barcode) && 
                await _unitOfWork.Products.BarcodeExistsAsync(updateProductDto.Barcode, updateProductDto.Id))
                throw new InvalidOperationException($"Barcode '{updateProductDto.Barcode}' already exists.");

            if (updateProductDto.DefaultSellingPrice < updateProductDto.MinSellingPrice)
                throw new InvalidOperationException("Default selling price cannot be less than minimum selling price.");

            // Update product
            product.Name = updateProductDto.Name;
            product.Barcode = updateProductDto.Barcode;
            product.CategoryId = updateProductDto.CategoryId;
            product.SupplierId = updateProductDto.SupplierId;
            product.DefaultCostPrice = updateProductDto.DefaultCostPrice;
            product.DefaultSellingPrice = updateProductDto.DefaultSellingPrice;
            product.MinSellingPrice = updateProductDto.MinSellingPrice;
            product.Description = updateProductDto.Description;

            _unitOfWork.Products.Update(product);
            await _unitOfWork.SaveChangesAsync();

            // Get updated product with details
            var updatedProduct = await _unitOfWork.Products.GetByIdWithIncludeAsync(product.Id, 
                p => p.Category, p => p.Supplier);
            
            var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
            var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);

            return MapToProductDto(updatedProduct!, currentStock, inventoryValue);
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(id);
            if (product == null)
                return false;

            if (!await CanDeleteProductAsync(id))
                throw new InvalidOperationException("Cannot delete product that has related transactions or stock movements.");

            _unitOfWork.Products.Remove(product);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<ProductDto>> SearchProductsAsync(ProductSearchDto searchDto)
        {
            var products = await _unitOfWork.Products.GetProductsWithDetailsAsync();
            var filteredProducts = products.AsQueryable();

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                filteredProducts = filteredProducts.Where(p => 
                    p.Name.Contains(searchDto.SearchTerm) || 
                    (p.Barcode != null && p.Barcode.Contains(searchDto.SearchTerm)));
            }

            if (searchDto.CategoryId.HasValue)
                filteredProducts = filteredProducts.Where(p => p.CategoryId == searchDto.CategoryId.Value);

            if (searchDto.SupplierId.HasValue)
                filteredProducts = filteredProducts.Where(p => p.SupplierId == searchDto.SupplierId.Value);

            if (searchDto.MinPrice.HasValue)
                filteredProducts = filteredProducts.Where(p => p.DefaultSellingPrice >= searchDto.MinPrice.Value);

            if (searchDto.MaxPrice.HasValue)
                filteredProducts = filteredProducts.Where(p => p.DefaultSellingPrice <= searchDto.MaxPrice.Value);

            var productDtos = new List<ProductDto>();
            foreach (var product in filteredProducts)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                
                var productDto = MapToProductDto(product, currentStock, inventoryValue);

                // Apply stock filters
                if (searchDto.InStock.HasValue && searchDto.InStock.Value && currentStock <= 0)
                    continue;

                if (searchDto.LowStock.HasValue && searchDto.LowStock.Value && 
                    currentStock > searchDto.LowStockThreshold)
                    continue;

                productDtos.Add(productDto);
            }

            return productDtos;
        }

        public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId)
        {
            var products = await _unitOfWork.Products.GetByCategoryAsync(categoryId);
            var productDtos = new List<ProductDto>();

            foreach (var product in products)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return productDtos;
        }

        public async Task<IEnumerable<ProductDto>> GetProductsBySupplierAsync(int supplierId)
        {
            var products = await _unitOfWork.Products.GetBySupplierAsync(supplierId);
            var productDtos = new List<ProductDto>();

            foreach (var product in products)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return productDtos;
        }

        public async Task<IEnumerable<ProductDto>> GetProductsInPriceRangeAsync(decimal minPrice, decimal maxPrice)
        {
            var products = await _unitOfWork.Products.GetProductsInPriceRangeAsync(minPrice, maxPrice);
            var productDtos = new List<ProductDto>();

            foreach (var product in products)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return productDtos;
        }

        private ProductDto MapToProductDto(Product product, int currentStock, decimal inventoryValue)
        {
            return new ProductDto
            {
                Id = product.Id,
                Name = product.Name,
                Barcode = product.Barcode,
                CategoryId = product.CategoryId,
                CategoryName = product.Category?.Name ?? "",
                SupplierId = product.SupplierId,
                SupplierName = product.Supplier?.Name,
                DefaultCostPrice = product.DefaultCostPrice,
                DefaultSellingPrice = product.DefaultSellingPrice,
                MinSellingPrice = product.MinSellingPrice,
                Description = product.Description,
                CreatedAt = product.CreatedAt,
                CurrentQuantity = currentStock,
                InventoryValue = inventoryValue
            };
        }

        public async Task<IEnumerable<ProductStockDto>> GetStockSummaryAsync()
        {
            var stockSummary = await _unitOfWork.Inventory.GetStockSummaryAsync();
            return stockSummary.Select(item =>
            {
                var dynamicItem = (dynamic)item;
                return new ProductStockDto
                {
                    ProductId = (int)dynamicItem.ProductId,
                    ProductName = (string)dynamicItem.ProductName,
                    CurrentQuantity = (int)dynamicItem.CurrentStock,
                    AverageCost = 0, // Will be calculated separately if needed
                    TotalValue = 0, // Will be calculated separately if needed
                    LastMovementDate = (DateTime?)dynamicItem.LastMovement,
                    Status = GetStockStatus((int)dynamicItem.CurrentStock)
                };
            });
        }

        public async Task<IEnumerable<ProductStockDto>> GetLowStockProductsAsync(int threshold = 10)
        {
            var lowStockProducts = await _unitOfWork.Inventory.GetLowStockAlertsAsync(threshold);
            return lowStockProducts.Cast<dynamic>().Select(item => new ProductStockDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CurrentQuantity = item.CurrentStock,
                LastMovementDate = item.LastMovement,
                Status = "LowStock"
            });
        }

        public async Task<IEnumerable<ProductStockDto>> GetOutOfStockProductsAsync()
        {
            var outOfStockProducts = await _unitOfWork.Inventory.GetOutOfStockProductsAsync();
            return outOfStockProducts.Cast<dynamic>().Select(item => new ProductStockDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CurrentQuantity = item.CurrentStock,
                LastMovementDate = item.LastMovement,
                Status = "OutOfStock"
            });
        }

        public async Task<ProductStockDto?> GetProductStockAsync(int productId)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            if (product == null)
                return null;

            var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(productId);
            var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(productId);

            return new ProductStockDto
            {
                ProductId = productId,
                ProductName = product.Name,
                CurrentQuantity = currentStock,
                TotalValue = inventoryValue,
                Status = GetStockStatus(currentStock)
            };
        }

        public async Task<bool> AdjustStockAsync(int productId, int quantity, string reason, int userId)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            if (product == null)
                return false;

            var inventoryLog = new InventoryLog
            {
                ProductId = productId,
                MovementType = "adjust",
                Quantity = quantity,
                UnitCost = product.DefaultCostPrice,
                ReferenceTable = "manual_adjustment",
                ReferenceId = 0,
                Note = reason,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Inventory.AddAsync(inventoryLog);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<ProductDto>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var topProducts = await _unitOfWork.Products.GetTopSellingProductsAsync(count, fromDate, toDate);
            var productDtos = new List<ProductDto>();

            foreach (var product in topProducts)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return productDtos;
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _unitOfWork.Products.GetTotalInventoryValueAsync();
        }

        public async Task<decimal> GetAverageProductPriceAsync()
        {
            return await _unitOfWork.Products.GetAveragePriceAsync();
        }

        public async Task<object> GetProductStatisticsAsync()
        {
            var totalProducts = await _unitOfWork.Products.CountAsync();
            var totalValue = await GetTotalInventoryValueAsync();
            var averagePrice = await GetAverageProductPriceAsync();
            var lowStockCount = (await GetLowStockProductsAsync()).Count();
            var outOfStockCount = (await GetOutOfStockProductsAsync()).Count();

            return new
            {
                TotalProducts = totalProducts,
                TotalInventoryValue = totalValue,
                AveragePrice = averagePrice,
                LowStockProducts = lowStockCount,
                OutOfStockProducts = outOfStockCount,
                InStockProducts = totalProducts - outOfStockCount
            };
        }

        public async Task<bool> ProductExistsAsync(int id)
        {
            return await _unitOfWork.Products.AnyAsync(p => p.Id == id);
        }

        public async Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null)
        {
            return await _unitOfWork.Products.BarcodeExistsAsync(barcode, excludeId);
        }

        public async Task<bool> CanDeleteProductAsync(int id)
        {
            // Check if product has sales
            var hasSales = await _unitOfWork.SalesInvoiceDetails.AnyAsync(d => d.ProductId == id);
            if (hasSales)
                return false;

            // Check if product has purchases
            var hasPurchases = await _unitOfWork.PurchaseInvoiceDetails.AnyAsync(d => d.ProductId == id);
            if (hasPurchases)
                return false;

            // Check if product has inventory movements
            var hasMovements = await _unitOfWork.Inventory.AnyAsync(il => il.ProductId == id);
            if (hasMovements)
                return false;

            return true;
        }

        public async Task<(IEnumerable<ProductDto> Products, int TotalCount)> GetPagedProductsAsync(
            int pageNumber, int pageSize, ProductSearchDto? searchDto = null)
        {
            var (products, totalCount) = await _unitOfWork.Products.GetPagedWithStockAsync(
                pageNumber, pageSize, searchDto?.SearchTerm, searchDto?.CategoryId);

            var productDtos = new List<ProductDto>();
            foreach (var product in products)
            {
                var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(product.Id);
                var inventoryValue = await _unitOfWork.Inventory.GetInventoryValueAsync(product.Id);
                productDtos.Add(MapToProductDto(product, currentStock, inventoryValue));
            }

            return (productDtos, totalCount);
        }

        private string GetStockStatus(int currentStock)
        {
            if (currentStock <= 0)
                return "OutOfStock";
            else if (currentStock <= 10)
                return "LowStock";
            else
                return "InStock";
        }
    }
}
