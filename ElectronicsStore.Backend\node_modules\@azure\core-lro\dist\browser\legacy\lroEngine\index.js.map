{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/legacy/lroEngine/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { LroEngine } from \"./lroEngine.js\";\nexport { LroEngineOptions } from \"./models.js\";\n"]}