import React from 'react';
import { Box, Typography, Card, CardContent, Grid } from '@mui/material';

function App() {
  return (
    <Box sx={{ p: 3, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      <Typography variant="h3" sx={{ textAlign: 'center', mb: 4, color: '#1976d2' }}>
        🏪 متجر الإلكترونيات - Dashboard
      </Typography>

      <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
        <Typography variant="h4" sx={{ mb: 3, color: '#2e7d32' }}>
          ✅ Dashboard يعمل مع Material-UI!
        </Typography>

        <Typography variant="h5" sx={{ mb: 2 }}>
          📊 الإحصائيات:
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e3f2fd' }}>
              <CardContent>
                <Typography variant="h6" sx={{ color: '#1976d2', mb: 1 }}>
                  إجمالي المبيعات
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  1,250,000 ر.س
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e8f5e8' }}>
              <CardContent>
                <Typography variant="h6" sx={{ color: '#2e7d32', mb: 1 }}>
                  إجمالي المنتجات
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  450 منتج
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#fff3e0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ color: '#f57c00', mb: 1 }}>
                  مخزون منخفض
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  23 منتج
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#fce4ec' }}>
              <CardContent>
                <Typography variant="h6" sx={{ color: '#c2185b', mb: 1 }}>
                  مبيعات اليوم
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  45,000 ر.س
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default App;
