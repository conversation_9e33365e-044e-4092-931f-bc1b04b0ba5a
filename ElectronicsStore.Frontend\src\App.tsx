import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Inventory,
  ShoppingCart,
  AttachMoney,
  People,
  Warning,
  CheckCircle
} from '@mui/icons-material';

function App() {
  return (
    <Box sx={{ p: 3, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      <Typography variant="h3" sx={{ textAlign: 'center', mb: 4, color: '#1976d2' }}>
        🏪 متجر الإلكترونيات - Dashboard
      </Typography>

      <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
        <Typography variant="h4" sx={{ mb: 3, color: '#2e7d32' }}>
          ✅ Dashboard يعمل مع Material-UI!
        </Typography>

        <Typography variant="h5" sx={{ mb: 2 }}>
          📊 الإحصائيات:
        </Typography>

        <Grid container spacing={3}>
          {/* إجمالي المبيعات */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e3f2fd', '&:hover': { transform: 'translateY(-2px)', transition: '0.3s' } }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AttachMoney sx={{ color: '#1976d2', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#1976d2' }}>
                    إجمالي المبيعات
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  1,250,000 ر.س
                </Typography>
                <Chip
                  icon={<TrendingUp />}
                  label="+12.5%"
                  color="success"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>

          {/* إجمالي المنتجات */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e8f5e8', '&:hover': { transform: 'translateY(-2px)', transition: '0.3s' } }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Inventory sx={{ color: '#2e7d32', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#2e7d32' }}>
                    إجمالي المنتجات
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  450 منتج
                </Typography>
                <Chip
                  icon={<CheckCircle />}
                  label="متوفر"
                  color="success"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>

          {/* مخزون منخفض */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#fff3e0', '&:hover': { transform: 'translateY(-2px)', transition: '0.3s' } }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Warning sx={{ color: '#f57c00', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#f57c00' }}>
                    مخزون منخفض
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  23 منتج
                </Typography>
                <Chip
                  icon={<Warning />}
                  label="يحتاج تموين"
                  color="warning"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>

          {/* مبيعات اليوم */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#fce4ec', '&:hover': { transform: 'translateY(-2px)', transition: '0.3s' } }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ShoppingCart sx={{ color: '#c2185b', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#c2185b' }}>
                    مبيعات اليوم
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  45,000 ر.س
                </Typography>
                <Chip
                  icon={<TrendingUp />}
                  label="+8.2%"
                  color="success"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default App;
