import React from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material/styles';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Theme
import { theme } from './theme/theme';

// Components
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <QueryClientProvider client={queryClient}>
        <Layout>
          <Dashboard />
        </Layout>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;
