import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

function App() {
  return (
    <div>
      <CssBaseline />
      <h1 style={{ textAlign: 'center', color: '#1976d2', padding: '20px' }}>
        🏪 متجر الإلكترونيات - Dashboard
      </h1>
      <p style={{ textAlign: 'center', fontSize: '18px' }}>
        ✅ التطبيق يعمل! المشكلة تم حلها.
      </p>
    </div>
  );
}

export default App;
