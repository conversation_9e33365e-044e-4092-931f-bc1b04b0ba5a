{"version": 3, "file": "errors.js", "names": ["ConnectionError", "Error", "constructor", "message", "code", "exports", "RequestError"], "sources": ["../src/errors.ts"], "sourcesContent": ["export class ConnectionError extends <PERSON>rror {\n  declare code: string | undefined;\n\n  declare isTransient: boolean | undefined;\n\n  constructor(message: string, code?: string) {\n    super(message);\n\n    this.code = code;\n  }\n}\n\nexport class RequestError extends Error {\n  declare code: string | undefined;\n\n  declare number: number | undefined;\n  declare state: number | undefined;\n  declare class: number | undefined;\n  declare serverName: string | undefined;\n  declare procName: string | undefined;\n  declare lineNumber: number | undefined;\n\n  constructor(message: string, code?: string) {\n    super(message);\n\n    this.code = code;\n  }\n}\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,SAASC,KAAK,CAAC;EAKzCC,WAAWA,CAACC,OAAe,EAAEC,IAAa,EAAE;IAC1C,KAAK,CAACD,OAAO,CAAC;IAEd,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;AACF;AAACC,OAAA,CAAAL,eAAA,GAAAA,eAAA;AAEM,MAAMM,YAAY,SAASL,KAAK,CAAC;EAUtCC,WAAWA,CAACC,OAAe,EAAEC,IAAa,EAAE;IAC1C,KAAK,CAACD,OAAO,CAAC;IAEd,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;AACF;AAACC,OAAA,CAAAC,YAAA,GAAAA,YAAA"}