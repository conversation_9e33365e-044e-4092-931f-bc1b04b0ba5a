{"version": 3, "file": "operationOptionHelpers.js", "sourceRoot": "", "sources": ["../../src/operationOptionHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,mCAAmC,IAAI,sCAAsC,GAE9E,MAAM,2BAA2B,CAAC;AAEnC;;;;GAIG;AACH,MAAM,UAAU,mCAAmC,CAAC,OAAyB;IAC3E,OAAO,sCAAsC,CAC3C,OAA8B,CACV,CAAC;AACzB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions, RequestParameters } from \"./common.js\";\n\nimport {\n  operationOptionsToRequestParameters as tspOperationOptionsToRequestParameters,\n  type OperationOptions as TspOperationOptions,\n} from \"@typespec/ts-http-runtime\";\n\n/**\n * Helper function to convert OperationOptions to RequestParameters\n * @param options - the options that are used by Modular layer to send the request\n * @returns the result of the conversion in RequestParameters of RLC layer\n */\nexport function operationOptionsToRequestParameters(options: OperationOptions): RequestParameters {\n  return tspOperationOptionsToRequestParameters(\n    options as TspOperationOptions,\n  ) as RequestParameters;\n}\n"]}