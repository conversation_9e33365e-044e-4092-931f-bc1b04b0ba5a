# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.14](https://github.com/es-shims/AggregateError/compare/v1.0.13...v1.0.14) - 2025-06-01

### Commits

- [Deps] update `es-abstract`, `globalthis` [`ddcdc9d`](https://github.com/es-shims/AggregateError/commit/ddcdc9dbf94196870db314cb2acbe80098035ecf)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `for-each`, `object-inspect`, `tape` [`f760355`](https://github.com/es-shims/AggregateError/commit/f760355314b4a80b8135a04ac51d557683faa441)
- [Tests] replace `aud` with `npm audit` [`8497f42`](https://github.com/es-shims/AggregateError/commit/8497f425c13a343c84b7be39f1ae1b7bce99c497)
- [Dev Deps] add missing peer dep [`657a7ad`](https://github.com/es-shims/AggregateError/commit/657a7ade36e5e47af7c2ccdb685505cb54e0e45f)

## [v1.0.13](https://github.com/es-shims/AggregateError/compare/v1.0.12...v1.0.13) - 2024-03-22

### Commits

- [Deps] update `es-abstract`, `set-function-name` [`8a923d8`](https://github.com/es-shims/AggregateError/commit/8a923d8d6c52f49c6a59ac0cf87c028d5fc79d5a)
- [actions] remove redundant finisher [`7b4a423`](https://github.com/es-shims/AggregateError/commit/7b4a4232024ac8658f39d34d7b18c1c4adbd1751)
- [Deps] update `define-data-property`, `es-abstract`, `es-errors`, `has-property-descriptors` [`dac80ec`](https://github.com/es-shims/AggregateError/commit/dac80ecaaa2debcf09caded760b3aa18a189a226)
- [Dev Deps] update `tape` [`aa3ff65`](https://github.com/es-shims/AggregateError/commit/aa3ff65b715b45ef77286c6307df987118451635)

## [v1.0.12](https://github.com/es-shims/AggregateError/compare/v1.0.11...v1.0.12) - 2024-02-04

### Commits

- [Deps] update `define-data-property`, `es-abstract`, `function-bind`, `get-intrinsic`, `has-property-descriptors` [`e58ffc2`](https://github.com/es-shims/AggregateError/commit/e58ffc2aeafd00632046a66d20f5f26a349821cf)
- [Dev Deps] update `aud`, `npmignore`, `object-inspect`, `tape` [`78fe590`](https://github.com/es-shims/AggregateError/commit/78fe590e8aab85afcad7d634433e98cc932e31b4)
- [Refactor] use `es-errors` where possible, so things that only need those do not need `get-intrinsic` [`9c69ddd`](https://github.com/es-shims/AggregateError/commit/9c69ddd0bb2848c28d72239c45be793d2a8a7694)

## [v1.0.11](https://github.com/es-shims/AggregateError/compare/v1.0.10...v1.0.11) - 2023-09-13

### Commits

- [Refactor] use `define-data-property`, `set-function-name` [`68b182f`](https://github.com/es-shims/AggregateError/commit/68b182f399b2522fedcdbc441f7efb4cd205bf5a)
- [Deps] update `define-properties`, `set-function-name` [`8c0491e`](https://github.com/es-shims/AggregateError/commit/8c0491e73bd6adcd83e2833784b54ec1225f5e31)

## [v1.0.10](https://github.com/es-shims/AggregateError/compare/v1.0.9...v1.0.10) - 2023-08-26

### Commits

- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`567696f`](https://github.com/es-shims/AggregateError/commit/567696f56631c565c0af7937f9dde93d96e2914d)
- [Tests] remove invalid receiver tests; add "as a function" tests [`97e050d`](https://github.com/es-shims/AggregateError/commit/97e050d7b387cc57f8fd47c4768033c62f26535f)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `es6-shim`, `object-inspect`, `tape` [`c6fbf1d`](https://github.com/es-shims/AggregateError/commit/c6fbf1dbafb8c5a6f96cd082079c113343451d71)
- [Deps] update `es-abstract` [`138527c`](https://github.com/es-shims/AggregateError/commit/138527c9d0b10beaf1732f4c3cc603ca095c29d8)
- [Dev Deps] update `aud` [`e98e027`](https://github.com/es-shims/AggregateError/commit/e98e0274a5e370eba5659fb6a6f1c8a3eb114bd9)

## [v1.0.9](https://github.com/es-shims/AggregateError/compare/v1.0.8...v1.0.9) - 2022-11-02

### Commits

- [Deps] update `es-abstract`, `get-intrinsic`, `globalthis` [`8fe0dd4`](https://github.com/es-shims/AggregateError/commit/8fe0dd490d9ce21b6a0ab67518d1b2320fe3c74b)
- [actions] update rebase action to use reusable workflow [`12fbd34`](https://github.com/es-shims/AggregateError/commit/12fbd341e3908b76294b1799b92c4c12fb876647)
- [Dev Deps] update `aud`, `object-inspect`, `tape` [`035dade`](https://github.com/es-shims/AggregateError/commit/035dade12d1a5fc62fb40b891b308b9c94f840cb)

## [v1.0.8](https://github.com/es-shims/AggregateError/compare/v1.0.7...v1.0.8) - 2022-05-04

### Commits

- [actions] reuse common workflows [`551e610`](https://github.com/es-shims/AggregateError/commit/551e6109bf7782949290c05f42d67ebea5de2bd8)
- [meta] use `npmignore` to autogenerate an npmignore file [`2b9c9f6`](https://github.com/es-shims/AggregateError/commit/2b9c9f6c214d176ec018279e651538c7709cb550)
- [Fix] ensure `AggregateError.prototype` is nonwritable [`53c59b4`](https://github.com/es-shims/AggregateError/commit/53c59b4f03480a2bfcae67530b15b857b06323f5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`db50863`](https://github.com/es-shims/AggregateError/commit/db50863f715ae15f7fb00a3e745f018d719382b5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`ca73cf7`](https://github.com/es-shims/AggregateError/commit/ca73cf76f9795288be5f07d71afde77416cc6974)
- [actions] update codecov uploader [`e5689ca`](https://github.com/es-shims/AggregateError/commit/e5689ca9b5a4b6a452de8c1a2ac4392ca154b313)
- [Deps] update `define-properties`, `es-abstract`, `functions-have-names` [`e0b0863`](https://github.com/es-shims/AggregateError/commit/e0b08630cd432882b1689738a0ff684b8b574634)
- [Deps] update `es-abstract` [`d41de34`](https://github.com/es-shims/AggregateError/commit/d41de34a832d2905f89aada97066e23365b6d223)

## [v1.0.7](https://github.com/es-shims/AggregateError/compare/v1.0.6...v1.0.7) - 2021-09-30

### Commits

- [meta] do not publish Github Actions workflows [`303ffe3`](https://github.com/es-shims/AggregateError/commit/303ffe3b606e2c770f602c5ab1ea5692935a4ca8)

## [v1.0.6](https://github.com/es-shims/AggregateError/compare/v1.0.5...v1.0.6) - 2021-09-30

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`5a9b28c`](https://github.com/es-shims/AggregateError/commit/5a9b28ca0faadd38c069a2c929b8f10ed7b105c5)
- [Deps] update `es-abstract` [`9d62677`](https://github.com/es-shims/AggregateError/commit/9d626773706fbff8ca7708f9a29601963140222c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`2c14b62`](https://github.com/es-shims/AggregateError/commit/2c14b62eacaae76f619955fdfa37aaed91fccbac)
- [readme] fix repo URLs; remove travis badge [`5097d6a`](https://github.com/es-shims/AggregateError/commit/5097d6a3d326a654ab838edf0672c3e89c6abf32)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`cf7500b`](https://github.com/es-shims/AggregateError/commit/cf7500b762e0abce580f9b290d209d21ff0b9f00)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-strict-mode`, `object-inspect`, `tape` [`41f8949`](https://github.com/es-shims/AggregateError/commit/41f8949f96872a694d04fe7e9a9e2e28cace1367)
- [actions] update workflows [`04d88f9`](https://github.com/es-shims/AggregateError/commit/04d88f91142d5a0e22318a976927255677ed7f1f)
- [readme] add github actions/codecov badges [`3e84f08`](https://github.com/es-shims/AggregateError/commit/3e84f085283e718339768cecf789aa670805d14c)
- [Deps] update `es-abstract`, `get-intrinsic`, `globalthis` [`70e9947`](https://github.com/es-shims/AggregateError/commit/70e9947169a976680a9db0cfdd68b57088412bb2)
- [Deps] update `es-abstract`, `functions-have-names`, `get-iintrinsic` [`e68ae0b`](https://github.com/es-shims/AggregateError/commit/e68ae0b63a6afec14eda250df3b719e3dcdf4b6b)
- [meta] use `prepublishOnly`, for npm 7+ [`2858f55`](https://github.com/es-shims/AggregateError/commit/2858f55c31fa713f38249e8cd4e7ee5c75eac458)
- [Tests] improve coverage [`ab5858a`](https://github.com/es-shims/AggregateError/commit/ab5858ac4c1191bb68b987e7a31221ea1c517cf1)

## [v1.0.5](https://github.com/es-shims/AggregateError/compare/v1.0.4...v1.0.5) - 2020-11-22

### Commits

- [Tests] migrate tests to Github Actions [`1b83d28`](https://github.com/es-shims/AggregateError/commit/1b83d2853482d8aaa7529c663c34e476ed052bcd)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`952f9a2`](https://github.com/es-shims/AggregateError/commit/952f9a25698df829c356b8586c934f65d7afb19d)
- [Tests] run `nyc` on all tests [`ce5728a`](https://github.com/es-shims/AggregateError/commit/ce5728a4842374fb6349e3dcb11e56991d19fcb3)
- [Deps] update `es-abstract`; use `get-intrinsic` where applicable [`b4999f9`](https://github.com/es-shims/AggregateError/commit/b4999f935e98e3292c582d6ae538c7e5d326021a)
- [actions] add "Allow Edits" workflow [`493b21d`](https://github.com/es-shims/AggregateError/commit/493b21d1d103d38e2f017dcc86870373c60635ac)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es6-shim` [`43cc5ca`](https://github.com/es-shims/AggregateError/commit/43cc5cae65010f5699cd650174f01550ea43fe28)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`95c0a09`](https://github.com/es-shims/AggregateError/commit/95c0a09d1100eb5bd3a1d49c381df0e6ea052ab3)

## [v1.0.4](https://github.com/es-shims/AggregateError/compare/v1.0.3...v1.0.4) - 2020-08-08

### Fixed

- [Fix] use `Object.defineProperty` instead of assignment to set the entry point `.prototype` [`#12`](https://github.com/es-shims/AggregateError/issues/12)

### Commits

- [Dev Deps] update `auto-changelog`, `eslint`, `object-inspect` [`a87fbed`](https://github.com/es-shims/AggregateError/commit/a87fbede91105713a43f583f335d50888b2ac5e9)
- [meta] update `auto-changelog` settings [`5cb7179`](https://github.com/es-shims/AggregateError/commit/5cb7179de5eb8cecc750d6172838a05b4938a642)
- [Deps] update `es-abstract` [`252bc1a`](https://github.com/es-shims/AggregateError/commit/252bc1a75f273b03df953499fcfae0ff84371b4f)
- [Tests] remove confusing comment [`ee9ea50`](https://github.com/es-shims/AggregateError/commit/ee9ea50d8174240853ad092d7390e90aae55285c)
- [Tests] add missing `covert` dep [`f848aa2`](https://github.com/es-shims/AggregateError/commit/f848aa290ef98d907443e5d9e32dd9207ccf77a6)

## [v1.0.3](https://github.com/es-shims/AggregateError/compare/v1.0.2...v1.0.3) - 2020-06-04

### Commits

- [patch] spec change; `errors` is now an own data instead of a prototype accessor [`9d8750c`](https://github.com/es-shims/AggregateError/commit/9d8750c6c3028bfd8aa11efb130448d3fc7b22a5)
- [patch] spec change; `errors` is validated/processed *after* stringifying and setting `message` [`890d344`](https://github.com/es-shims/AggregateError/commit/890d3445748f94b184522467b2285295bb9c2292)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`488dfd3`](https://github.com/es-shims/AggregateError/commit/488dfd381d93ccea6c06fe08db1d1b5a65d2f6f6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`83bc687`](https://github.com/es-shims/AggregateError/commit/83bc6872f10755a88bbb2b0761f38e9f0354f46e)
- [Deps] update `es-abstract`, `functions-have-names`, `globalthis`, `internal-slot` [`4a86017`](https://github.com/es-shims/AggregateError/commit/4a8601721996b24f3ab41fdac691b5d77ce1343b)
- [Docs] fix Travis CI link in README.md [`aa712e3`](https://github.com/es-shims/AggregateError/commit/aa712e3d18f12ec7b9f6b5f5dd087d13fd8158bd)
- [Dev Deps] update `auto-changelog`, `tape` [`27d054d`](https://github.com/es-shims/AggregateError/commit/27d054d2f585dff8f41bc104295ba104d3d6cdef)
- [Dev Deps] update `auto-changelog`; add `aud` [`08edc48`](https://github.com/es-shims/AggregateError/commit/08edc48a4445701c974df5e8ed7d33dcd538c8d0)
- [Deps] update `es-abstract` [`8e33737`](https://github.com/es-shims/AggregateError/commit/8e3373707b3f30e9847ca8d96623cdba170c67d3)
- [Tests] only audit prod deps [`56599f2`](https://github.com/es-shims/AggregateError/commit/56599f226b062fb6e0df1ccf96ade0b03060423e)

## [v1.0.2](https://github.com/es-shims/AggregateError/compare/v1.0.1...v1.0.2) - 2019-12-13

### Commits

- [Refactor] use split-up `es-abstract` (67% bundle size decrease) [`fcca181`](https://github.com/es-shims/AggregateError/commit/fcca18198dc96c7a9bff4350a059aef1abd75455)
- [meta] switch to `keepachangelog` template [`61c9af8`](https://github.com/es-shims/AggregateError/commit/61c9af83bc6520df306ed84fcb3030ba8c95ae25)
- [actions] add automatic rebasing / merge commit blocking [`d9840cd`](https://github.com/es-shims/AggregateError/commit/d9840cda4e863c64fe0419449f6c78200fd8d0e0)
- [Deps] update `es-abstract`, `internal-slot` [`71aaa2e`](https://github.com/es-shims/AggregateError/commit/71aaa2e139c92f2d61c13c40322140c9b792f29d)
- [Tests] use `evalmd` in `postlint` [`5081797`](https://github.com/es-shims/AggregateError/commit/5081797a51e3f4206abe00e28c7238623cd07aec)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`7a8486c`](https://github.com/es-shims/AggregateError/commit/7a8486ccdb5fcfff866206bf5125fd21bbbb113a)
- [meta] fix missing version npmrc settings [`10c271e`](https://github.com/es-shims/AggregateError/commit/10c271e739e58b39725c46100ef03930d8c28140)

## [v1.0.1](https://github.com/es-shims/AggregateError/compare/v1.0.0...v1.0.1) - 2019-11-11

### Commits

- [Tests] use shared travis-ci configs [`4acd21b`](https://github.com/es-shims/AggregateError/commit/4acd21bd73c36b1d88b5fc4b01597917765ba0a0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `object-inspect`, `auto-changelog` [`668dd27`](https://github.com/es-shims/AggregateError/commit/668dd27d0c21ba635dadc6209c927add75e391f6)
- [Fix] remove own `AggregateError.prototype.toString` [`86da74a`](https://github.com/es-shims/AggregateError/commit/86da74aaa38d003bbb04ffb52305cf8093229dcf)
- [meta] add `funding` field [`3673ad9`](https://github.com/es-shims/AggregateError/commit/3673ad97a2d5edf12a980f958cb1d292dc9c3b3c)

## v1.0.0 - 2019-10-21

### Commits

- [Tests] add `travis.yml` [`71ad01b`](https://github.com/es-shims/AggregateError/commit/71ad01b0ff6b7fa43ae0b8bc8dd3215761ae4eee)
- Implementation [`f67c101`](https://github.com/es-shims/AggregateError/commit/f67c101ae888925375d421ffabff856e333f322d)
- Tests [`5cda887`](https://github.com/es-shims/AggregateError/commit/5cda88785e6676f7bf7e152da4ae1ccf3d3a717e)
- Initial commit [`370054f`](https://github.com/es-shims/AggregateError/commit/370054f028af45fb43c763993772ebe270a49f80)
- [Refactor] use `internal-slot` [`972cb6b`](https://github.com/es-shims/AggregateError/commit/972cb6b558ad0246ac6ba837cfd6f407c4b1138d)
- readme [`562715e`](https://github.com/es-shims/AggregateError/commit/562715e80a2af2a35d8768149d699e752f123224)
- npm init [`cf3b5b3`](https://github.com/es-shims/AggregateError/commit/cf3b5b3aedaa1c3eaa530c4e81895df2344369cd)
- [tests] add `npm run lint` [`bc6cca9`](https://github.com/es-shims/AggregateError/commit/bc6cca9d43507fa4da7b44680e27924dd2798eec)
- [meta] add `safe-publish-latest`, `auto-changelog` [`b415df1`](https://github.com/es-shims/AggregateError/commit/b415df102b94d59f46b2131ce20a95d2ec8e6355)
- [Tests] skip name test when bound functions do not have names [`693e161`](https://github.com/es-shims/AggregateError/commit/693e161fd7d9f6667dee3ab32933910c21126b83)
- Only apps should have lockfiles [`315a27e`](https://github.com/es-shims/AggregateError/commit/315a27e68bbbfdb4dd0435305f3e772f185f22d7)
- [Refactor] use `functions-have-names` [`c42a982`](https://github.com/es-shims/AggregateError/commit/c42a982cb873dd4bb796aee9337e7cb8986e2f69)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`a7afeea`](https://github.com/es-shims/AggregateError/commit/a7afeead419514b9c6f9fd816a09d95c22b5a502)
