"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _metadataParser = require("../metadata-parser");
var _token = require("./token");
var _helpers = require("./helpers");
function readTableName(buf, offset, metadata, options) {
  if (!metadata.type.hasTableName) {
    return new _helpers.Result(undefined, offset);
  }
  if (options.tdsVersion < '7_2') {
    return (0, _helpers.readUsVarChar)(buf, offset);
  }
  let numberOfTableNameParts;
  ({
    offset,
    value: numberOfTableNameParts
  } = (0, _helpers.readUInt8)(buf, offset));
  const tableName = [];
  for (let i = 0; i < numberOfTableNameParts; i++) {
    let tableNamePart;
    ({
      offset,
      value: tableNamePart
    } = (0, _helpers.readUsVarChar)(buf, offset));
    tableName.push(tableNamePart);
  }
  return new _helpers.Result(tableName, offset);
}
function readColumnName(buf, offset, index, metadata, options) {
  let colName;
  ({
    offset,
    value: colName
  } = (0, _helpers.readBVarChar)(buf, offset));
  if (options.columnNameReplacer) {
    return new _helpers.Result(options.columnNameReplacer(colName, index, metadata), offset);
  } else if (options.camelCaseColumns) {
    return new _helpers.Result(colName.replace(/^[A-Z]/, function (s) {
      return s.toLowerCase();
    }), offset);
  } else {
    return new _helpers.Result(colName, offset);
  }
}
function readColumn(buf, offset, options, index) {
  let metadata;
  ({
    offset,
    value: metadata
  } = (0, _metadataParser.readMetadata)(buf, offset, options));
  let tableName;
  ({
    offset,
    value: tableName
  } = readTableName(buf, offset, metadata, options));
  let colName;
  ({
    offset,
    value: colName
  } = readColumnName(buf, offset, index, metadata, options));
  return new _helpers.Result({
    userType: metadata.userType,
    flags: metadata.flags,
    type: metadata.type,
    collation: metadata.collation,
    precision: metadata.precision,
    scale: metadata.scale,
    udtInfo: metadata.udtInfo,
    dataLength: metadata.dataLength,
    schema: metadata.schema,
    colName: colName,
    tableName: tableName
  }, offset);
}
async function colMetadataParser(parser) {
  let columnCount;
  while (true) {
    let offset;
    try {
      ({
        offset,
        value: columnCount
      } = (0, _helpers.readUInt16LE)(parser.buffer, parser.position));
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        await parser.waitForChunk();
        continue;
      }
      throw err;
    }
    parser.position = offset;
    break;
  }
  const columns = [];
  for (let i = 0; i < columnCount; i++) {
    while (true) {
      let column;
      let offset;
      try {
        ({
          offset,
          value: column
        } = readColumn(parser.buffer, parser.position, parser.options, i));
      } catch (err) {
        if (err instanceof _helpers.NotEnoughDataError) {
          await parser.waitForChunk();
          continue;
        }
        throw err;
      }
      parser.position = offset;
      columns.push(column);
      break;
    }
  }
  return new _token.ColMetadataToken(columns);
}
var _default = colMetadataParser;
exports.default = _default;
module.exports = colMetadataParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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