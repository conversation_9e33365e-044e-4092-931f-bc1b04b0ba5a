{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../src/restError.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EACL,eAAe,IAAI,kBAAkB,GAEtC,MAAM,2BAA2B,CAAC;AAUnC,MAAM,UAAU,eAAe,CAC7B,iBAAiD,EACjD,QAAgC;IAEhC,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;QAC1C,OAAO,kBAAkB,CAAC,iBAAiB,EAAE,QAAqC,CAAC,CAAC;IACtF,CAAC;SAAM,CAAC;QACN,OAAO,kBAAkB,CAAC,iBAA6C,CAAC,CAAC;IAC3E,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { RestError } from \"@azure/core-rest-pipeline\";\nimport type { PathUncheckedResponse } from \"./common.js\";\n\nimport {\n  createRestError as tspCreateRestError,\n  type PathUncheckedResponse as TspPathUncheckedResponse,\n} from \"@typespec/ts-http-runtime\";\n\n/**\n * Creates a rest error from a PathUnchecked response\n */\nexport function createRestError(response: PathUncheckedResponse): RestError;\n/**\n * Creates a rest error from an error message and a PathUnchecked response\n */\nexport function createRestError(message: string, response: PathUncheckedResponse): RestError;\nexport function createRestError(\n  messageOrResponse: string | PathUncheckedResponse,\n  response?: PathUncheckedResponse,\n): RestError {\n  if (typeof messageOrResponse === \"string\") {\n    return tspCreateRestError(messageOrResponse, response! as TspPathUncheckedResponse);\n  } else {\n    return tspCreateRestError(messageOrResponse as TspPathUncheckedResponse);\n  }\n}\n"]}