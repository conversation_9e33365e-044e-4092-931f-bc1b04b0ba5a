{"version": 3, "file": "transaction.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "_allHeaders", "obj", "__esModule", "default", "OPERATION_TYPE", "TM_GET_DTC_ADDRESS", "TM_PROPAGATE_XACT", "TM_BEGIN_XACT", "TM_PROMOTE_XACT", "TM_COMMIT_XACT", "TM_ROLLBACK_XACT", "TM_SAVE_XACT", "exports", "ISOLATION_LEVEL", "NO_CHANGE", "READ_UNCOMMITTED", "READ_COMMITTED", "REPEATABLE_READ", "SERIALIZABLE", "SNAPSHOT", "isolationLevelByValue", "name", "value", "assertValidIsolationLevel", "isolationLevel", "TypeError", "includes", "Number", "isInteger", "RangeError", "Transaction", "constructor", "outstandingRequestCount", "beginPayload", "txnDescriptor", "buffer", "WritableTrackingBuffer", "writeToTrackingBuffer", "writeUShort", "writeUInt8", "length", "writeString", "Symbol", "iterator", "data", "toString", "commitPayload", "rollbackPayload", "savePayload", "isolationLevelToTSQL"], "sources": ["../src/transaction.ts"], "sourcesContent": ["import WritableTrackingBuffer from './tracking-buffer/writable-tracking-buffer';\nimport { writeToTrackingBuffer } from './all-headers';\n\n/*\n  s2.2.6.8\n */\n\nexport const OPERATION_TYPE = {\n  TM_GET_DTC_ADDRESS: 0x00,\n  TM_PROPAGATE_XACT: 0x01,\n  TM_BEGIN_XACT: 0x05,\n  TM_PROMOTE_XACT: 0x06,\n  TM_COMMIT_XACT: 0x07,\n  TM_ROLLBACK_XACT: 0x08,\n  TM_SAVE_XACT: 0x09\n};\n\nexport const ISOLATION_LEVEL: { [key: string]: number } = {\n  NO_CHANGE: 0x00,\n  READ_UNCOMMITTED: 0x01,\n  READ_COMMITTED: 0x02,\n  REPEATABLE_READ: 0x03,\n  SERIALIZABLE: 0x04,\n  SNAPSHOT: 0x05\n};\n\nexport const isolationLevelByValue: { [key: number]: string } = {};\nfor (const name in ISOLATION_LEVEL) {\n  const value = ISOLATION_LEVEL[name];\n  isolationLevelByValue[value] = name;\n}\n\nexport function assertValidIsolationLevel(isolationLevel: any, name: string): asserts isolationLevel is 0 | 1 | 2 | 3 | 4 | 5 {\n  if (typeof isolationLevel !== 'number') {\n    throw new TypeError(`The \"${name}\" ${name.includes('.') ? 'property' : 'argument'} must be of type number. Received type ${typeof isolationLevel} (${isolationLevel})`);\n  }\n\n  if (!Number.isInteger(isolationLevel)) {\n    throw new RangeError(`The value of \"${name}\" is out of range. It must be an integer. Received: ${isolationLevel}`);\n  }\n\n  if (!(isolationLevel >= 0 && isolationLevel <= 5)) {\n    throw new RangeError(`The value of \"${name}\" is out of range. It must be >= 0 && <= 5. Received: ${isolationLevel}`);\n  }\n}\n\nexport class Transaction {\n  declare name: string;\n  declare isolationLevel: number;\n  declare outstandingRequestCount: number;\n\n  constructor(name: string, isolationLevel = ISOLATION_LEVEL.NO_CHANGE) {\n    this.name = name;\n    this.isolationLevel = isolationLevel;\n    this.outstandingRequestCount = 1;\n  }\n\n  beginPayload(txnDescriptor: Buffer) {\n    const buffer = new WritableTrackingBuffer(100, 'ucs2');\n    writeToTrackingBuffer(buffer, txnDescriptor, this.outstandingRequestCount);\n    buffer.writeUShort(OPERATION_TYPE.TM_BEGIN_XACT);\n    buffer.writeUInt8(this.isolationLevel);\n    buffer.writeUInt8(this.name.length * 2);\n    buffer.writeString(this.name, 'ucs2');\n\n    return {\n      *[Symbol.iterator]() {\n        yield buffer.data;\n      },\n      toString: () => {\n        return 'Begin Transaction: name=' + this.name + ', isolationLevel=' + isolationLevelByValue[this.isolationLevel];\n      }\n    };\n  }\n\n  commitPayload(txnDescriptor: Buffer) {\n    const buffer = new WritableTrackingBuffer(100, 'ascii');\n    writeToTrackingBuffer(buffer, txnDescriptor, this.outstandingRequestCount);\n    buffer.writeUShort(OPERATION_TYPE.TM_COMMIT_XACT);\n    buffer.writeUInt8(this.name.length * 2);\n    buffer.writeString(this.name, 'ucs2');\n    // No fBeginXact flag, so no new transaction is started.\n    buffer.writeUInt8(0);\n\n    return {\n      *[Symbol.iterator]() {\n        yield buffer.data;\n      },\n      toString: () => {\n        return 'Commit Transaction: name=' + this.name;\n      }\n    };\n  }\n\n  rollbackPayload(txnDescriptor: Buffer) {\n    const buffer = new WritableTrackingBuffer(100, 'ascii');\n    writeToTrackingBuffer(buffer, txnDescriptor, this.outstandingRequestCount);\n    buffer.writeUShort(OPERATION_TYPE.TM_ROLLBACK_XACT);\n    buffer.writeUInt8(this.name.length * 2);\n    buffer.writeString(this.name, 'ucs2');\n    // No fBeginXact flag, so no new transaction is started.\n    buffer.writeUInt8(0);\n\n    return {\n      *[Symbol.iterator]() {\n        yield buffer.data;\n      },\n      toString: () => {\n        return 'Rollback Transaction: name=' + this.name;\n      }\n    };\n  }\n\n  savePayload(txnDescriptor: Buffer) {\n    const buffer = new WritableTrackingBuffer(100, 'ascii');\n    writeToTrackingBuffer(buffer, txnDescriptor, this.outstandingRequestCount);\n    buffer.writeUShort(OPERATION_TYPE.TM_SAVE_XACT);\n    buffer.writeUInt8(this.name.length * 2);\n    buffer.writeString(this.name, 'ucs2');\n\n    return {\n      *[Symbol.iterator]() {\n        yield buffer.data;\n      },\n      toString: () => {\n        return 'Save Transaction: name=' + this.name;\n      }\n    };\n  }\n\n  isolationLevelToTSQL() {\n    switch (this.isolationLevel) {\n      case ISOLATION_LEVEL.READ_UNCOMMITTED:\n        return 'READ UNCOMMITTED';\n      case ISOLATION_LEVEL.READ_COMMITTED:\n        return 'READ COMMITTED';\n      case ISOLATION_LEVEL.REPEATABLE_READ:\n        return 'REPEATABLE READ';\n      case ISOLATION_LEVEL.SERIALIZABLE:\n        return 'SERIALIZABLE';\n      case ISOLATION_LEVEL.SNAPSHOT:\n        return 'SNAPSHOT';\n    }\n    return '';\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAAsD,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEtD;AACA;AACA;;AAEO,MAAMG,cAAc,GAAG;EAC5BC,kBAAkB,EAAE,IAAI;EACxBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE;AAChB,CAAC;AAACC,OAAA,CAAAR,cAAA,GAAAA,cAAA;AAEK,MAAMS,eAA0C,GAAG;EACxDC,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE;AACZ,CAAC;AAACP,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAEK,MAAMO,qBAAgD,GAAG,CAAC,CAAC;AAACR,OAAA,CAAAQ,qBAAA,GAAAA,qBAAA;AACnE,KAAK,MAAMC,IAAI,IAAIR,eAAe,EAAE;EAClC,MAAMS,KAAK,GAAGT,eAAe,CAACQ,IAAI,CAAC;EACnCD,qBAAqB,CAACE,KAAK,CAAC,GAAGD,IAAI;AACrC;AAEO,SAASE,yBAAyBA,CAACC,cAAmB,EAAEH,IAAY,EAAmD;EAC5H,IAAI,OAAOG,cAAc,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAE,QAAOJ,IAAK,KAAIA,IAAI,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAW,0CAAyC,OAAOF,cAAe,KAAIA,cAAe,GAAE,CAAC;EACzK;EAEA,IAAI,CAACG,MAAM,CAACC,SAAS,CAACJ,cAAc,CAAC,EAAE;IACrC,MAAM,IAAIK,UAAU,CAAE,iBAAgBR,IAAK,uDAAsDG,cAAe,EAAC,CAAC;EACpH;EAEA,IAAI,EAAEA,cAAc,IAAI,CAAC,IAAIA,cAAc,IAAI,CAAC,CAAC,EAAE;IACjD,MAAM,IAAIK,UAAU,CAAE,iBAAgBR,IAAK,yDAAwDG,cAAe,EAAC,CAAC;EACtH;AACF;AAEO,MAAMM,WAAW,CAAC;EAKvBC,WAAWA,CAACV,IAAY,EAAEG,cAAc,GAAGX,eAAe,CAACC,SAAS,EAAE;IACpE,IAAI,CAACO,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACQ,uBAAuB,GAAG,CAAC;EAClC;EAEAC,YAAYA,CAACC,aAAqB,EAAE;IAClC,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,GAAG,EAAE,MAAM,CAAC;IACtD,IAAAC,iCAAqB,EAACF,MAAM,EAAED,aAAa,EAAE,IAAI,CAACF,uBAAuB,CAAC;IAC1EG,MAAM,CAACG,WAAW,CAAClC,cAAc,CAACG,aAAa,CAAC;IAChD4B,MAAM,CAACI,UAAU,CAAC,IAAI,CAACf,cAAc,CAAC;IACtCW,MAAM,CAACI,UAAU,CAAC,IAAI,CAAClB,IAAI,CAACmB,MAAM,GAAG,CAAC,CAAC;IACvCL,MAAM,CAACM,WAAW,CAAC,IAAI,CAACpB,IAAI,EAAE,MAAM,CAAC;IAErC,OAAO;MACL,EAAEqB,MAAM,CAACC,QAAQ,IAAI;QACnB,MAAMR,MAAM,CAACS,IAAI;MACnB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACd,OAAO,0BAA0B,GAAG,IAAI,CAACxB,IAAI,GAAG,mBAAmB,GAAGD,qBAAqB,CAAC,IAAI,CAACI,cAAc,CAAC;MAClH;IACF,CAAC;EACH;EAEAsB,aAAaA,CAACZ,aAAqB,EAAE;IACnC,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,GAAG,EAAE,OAAO,CAAC;IACvD,IAAAC,iCAAqB,EAACF,MAAM,EAAED,aAAa,EAAE,IAAI,CAACF,uBAAuB,CAAC;IAC1EG,MAAM,CAACG,WAAW,CAAClC,cAAc,CAACK,cAAc,CAAC;IACjD0B,MAAM,CAACI,UAAU,CAAC,IAAI,CAAClB,IAAI,CAACmB,MAAM,GAAG,CAAC,CAAC;IACvCL,MAAM,CAACM,WAAW,CAAC,IAAI,CAACpB,IAAI,EAAE,MAAM,CAAC;IACrC;IACAc,MAAM,CAACI,UAAU,CAAC,CAAC,CAAC;IAEpB,OAAO;MACL,EAAEG,MAAM,CAACC,QAAQ,IAAI;QACnB,MAAMR,MAAM,CAACS,IAAI;MACnB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACd,OAAO,2BAA2B,GAAG,IAAI,CAACxB,IAAI;MAChD;IACF,CAAC;EACH;EAEA0B,eAAeA,CAACb,aAAqB,EAAE;IACrC,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,GAAG,EAAE,OAAO,CAAC;IACvD,IAAAC,iCAAqB,EAACF,MAAM,EAAED,aAAa,EAAE,IAAI,CAACF,uBAAuB,CAAC;IAC1EG,MAAM,CAACG,WAAW,CAAClC,cAAc,CAACM,gBAAgB,CAAC;IACnDyB,MAAM,CAACI,UAAU,CAAC,IAAI,CAAClB,IAAI,CAACmB,MAAM,GAAG,CAAC,CAAC;IACvCL,MAAM,CAACM,WAAW,CAAC,IAAI,CAACpB,IAAI,EAAE,MAAM,CAAC;IACrC;IACAc,MAAM,CAACI,UAAU,CAAC,CAAC,CAAC;IAEpB,OAAO;MACL,EAAEG,MAAM,CAACC,QAAQ,IAAI;QACnB,MAAMR,MAAM,CAACS,IAAI;MACnB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACd,OAAO,6BAA6B,GAAG,IAAI,CAACxB,IAAI;MAClD;IACF,CAAC;EACH;EAEA2B,WAAWA,CAACd,aAAqB,EAAE;IACjC,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,GAAG,EAAE,OAAO,CAAC;IACvD,IAAAC,iCAAqB,EAACF,MAAM,EAAED,aAAa,EAAE,IAAI,CAACF,uBAAuB,CAAC;IAC1EG,MAAM,CAACG,WAAW,CAAClC,cAAc,CAACO,YAAY,CAAC;IAC/CwB,MAAM,CAACI,UAAU,CAAC,IAAI,CAAClB,IAAI,CAACmB,MAAM,GAAG,CAAC,CAAC;IACvCL,MAAM,CAACM,WAAW,CAAC,IAAI,CAACpB,IAAI,EAAE,MAAM,CAAC;IAErC,OAAO;MACL,EAAEqB,MAAM,CAACC,QAAQ,IAAI;QACnB,MAAMR,MAAM,CAACS,IAAI;MACnB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACd,OAAO,yBAAyB,GAAG,IAAI,CAACxB,IAAI;MAC9C;IACF,CAAC;EACH;EAEA4B,oBAAoBA,CAAA,EAAG;IACrB,QAAQ,IAAI,CAACzB,cAAc;MACzB,KAAKX,eAAe,CAACE,gBAAgB;QACnC,OAAO,kBAAkB;MAC3B,KAAKF,eAAe,CAACG,cAAc;QACjC,OAAO,gBAAgB;MACzB,KAAKH,eAAe,CAACI,eAAe;QAClC,OAAO,iBAAiB;MAC1B,KAAKJ,eAAe,CAACK,YAAY;QAC/B,OAAO,cAAc;MACvB,KAAKL,eAAe,CAACM,QAAQ;QAC3B,OAAO,UAAU;IACrB;IACA,OAAO,EAAE;EACX;AACF;AAACP,OAAA,CAAAkB,WAAA,GAAAA,WAAA"}