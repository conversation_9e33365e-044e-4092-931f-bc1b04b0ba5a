{"version": 3, "file": "debug.js", "names": ["_events", "require", "util", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "Debug", "EventEmitter", "constructor", "data", "payload", "packet", "token", "options", "indent", "direction", "haveListeners", "log", "headerToString", "dataToString", "generatePayloadText", "inspect", "showHidden", "depth", "colors", "listeners", "length", "text", "emit", "_default", "exports", "module"], "sources": ["../src/debug.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport * as util from 'util';\nimport { Packet } from './packet';\n\nclass Debug extends EventEmitter {\n  declare options: {\n    data: boolean;\n    payload: boolean;\n    packet: boolean;\n    token: boolean;\n  };\n\n  declare indent: string;\n\n  /*\n    @options    Which debug details should be sent.\n                data    - dump of packet data\n                payload - details of decoded payload\n  */\n  constructor({ data = false, payload = false, packet = false, token = false } = {}) {\n    super();\n\n    this.options = { data, payload, packet, token };\n    this.indent = '  ';\n  }\n\n  packet(direction: 'Received' | 'Sent', packet: Packet) {\n    if (this.haveListeners() && this.options.packet) {\n      this.log('');\n      this.log(direction);\n      this.log(packet.headerToString(this.indent));\n    }\n  }\n\n  data(packet: Packet) {\n    if (this.haveListeners() && this.options.data) {\n      this.log(packet.dataToString(this.indent));\n    }\n  }\n\n  payload(generatePayloadText: () => string) {\n    if (this.haveListeners() && this.options.payload) {\n      this.log(generatePayloadText());\n    }\n  }\n\n  token(token: any) {\n    if (this.haveListeners() && this.options.token) {\n      this.log(util.inspect(token, { showHidden: false, depth: 5, colors: true }));\n    }\n  }\n\n  haveListeners() {\n    return this.listeners('debug').length > 0;\n  }\n\n  log(text: string) {\n    this.emit('debug', text);\n  }\n}\n\nexport default Debug;\nmodule.exports = Debug;\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA6B,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAG7B,MAAMW,KAAK,SAASC,oBAAY,CAAC;EAU/B;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IAAEC,IAAI,GAAG,KAAK;IAAEC,OAAO,GAAG,KAAK;IAAEC,MAAM,GAAG,KAAK;IAAEC,KAAK,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACjF,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,OAAO,GAAG;MAAEJ,IAAI;MAAEC,OAAO;MAAEC,MAAM;MAAEC;IAAM,CAAC;IAC/C,IAAI,CAACE,MAAM,GAAG,IAAI;EACpB;EAEAH,MAAMA,CAACI,SAA8B,EAAEJ,MAAc,EAAE;IACrD,IAAI,IAAI,CAACK,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACF,MAAM,EAAE;MAC/C,IAAI,CAACM,GAAG,CAAC,EAAE,CAAC;MACZ,IAAI,CAACA,GAAG,CAACF,SAAS,CAAC;MACnB,IAAI,CAACE,GAAG,CAACN,MAAM,CAACO,cAAc,CAAC,IAAI,CAACJ,MAAM,CAAC,CAAC;IAC9C;EACF;EAEAL,IAAIA,CAACE,MAAc,EAAE;IACnB,IAAI,IAAI,CAACK,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACJ,IAAI,EAAE;MAC7C,IAAI,CAACQ,GAAG,CAACN,MAAM,CAACQ,YAAY,CAAC,IAAI,CAACL,MAAM,CAAC,CAAC;IAC5C;EACF;EAEAJ,OAAOA,CAACU,mBAAiC,EAAE;IACzC,IAAI,IAAI,CAACJ,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACH,OAAO,EAAE;MAChD,IAAI,CAACO,GAAG,CAACG,mBAAmB,CAAC,CAAC,CAAC;IACjC;EACF;EAEAR,KAAKA,CAACA,KAAU,EAAE;IAChB,IAAI,IAAI,CAACI,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACD,KAAK,EAAE;MAC9C,IAAI,CAACK,GAAG,CAACnC,IAAI,CAACuC,OAAO,CAACT,KAAK,EAAE;QAAEU,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;IAC9E;EACF;EAEAR,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACS,SAAS,CAAC,OAAO,CAAC,CAACC,MAAM,GAAG,CAAC;EAC3C;EAEAT,GAAGA,CAACU,IAAY,EAAE;IAChB,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,IAAI,CAAC;EAC1B;AACF;AAAC,IAAAE,QAAA,GAEcvB,KAAK;AAAAwB,OAAA,CAAAvC,OAAA,GAAAsC,QAAA;AACpBE,MAAM,CAACD,OAAO,GAAGxB,KAAK"}