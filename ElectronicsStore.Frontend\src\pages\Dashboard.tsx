import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Container,
  Paper,
  CircularProgress,
  Alert,
  Fade,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Assessment as AssessmentIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import DashboardHeader from '../components/DashboardHeader';
import PageContainer from '../components/PageContainer';

// Components
import StatsCard from '../components/StatsCard';
import SalesChart from '../components/SalesChart';
import TopProductsCard from '../components/TopProductsCard';
import RecentSalesCard from '../components/RecentSalesCard';
import LowStockAlertsCard from '../components/LowStockAlertsCard';

// Services & Types
import { dashboardService } from '../services/api';
import {
  DashboardStats,
  SalesData,
  TopProduct,
  RecentSale,
  LowStockAlert,
} from '../types';

const Dashboard: React.FC = () => {
  // State Management
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [recentSales, setRecentSales] = useState<RecentSale[]>([]);
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load Dashboard Data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع البيانات بشكل متوازي
        const [
          statsData,
          salesChartData,
          topProductsData,
          recentSalesData,
          alertsData,
        ] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getSalesData(30),
          dashboardService.getTopProducts(5),
          dashboardService.getRecentSales(8),
          dashboardService.getLowStockAlerts(),
        ]);

        setStats(statsData);
        setSalesData(salesChartData);
        setTopProducts(topProductsData);
        setRecentSales(recentSalesData);
        setLowStockAlerts(alertsData);
      } catch (err) {
        console.error('Error loading dashboard data:', err);
        setError('فشل في تحميل بيانات لوحة التحكم');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Loading State
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="60vh"
        flexDirection="column"
      >
        <CircularProgress size={60} thickness={4} />
        <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
          جاري تحميل لوحة التحكم...
        </Typography>
      </Box>
    );
  }

  // Error State
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <PageContainer maxWidth="xl">
      <Box>
          {/* Header */}
          <DashboardHeader
            title="لوحة التحكم"
            subtitle="نظرة عامة على أداء متجر الإلكترونيات"
            showBreadcrumbs={true}
            showLastUpdate={true}
          />

          {/* Primary Stats Cards */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
              الإحصائيات الرئيسية
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="إجمالي المبيعات"
                  value={stats?.totalSales || 0}
                  icon={<TrendingUpIcon />}
                  color="primary"
                  trend={{ value: 12.5, isPositive: true }}
                  subtitle="هذا الشهر"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="إجمالي المنتجات"
                  value={stats?.totalProducts || 0}
                  icon={<InventoryIcon />}
                  color="info"
                  subtitle="في المخزون"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="مخزون منخفض"
                  value={stats?.lowStockProducts || 0}
                  icon={<AssessmentIcon />}
                  color="warning"
                  subtitle="يحتاج إعادة تموين"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="مبيعات اليوم"
                  value={stats?.todaySales || 0}
                  icon={<ShoppingCartIcon />}
                  color="success"
                  trend={{ value: 8.2, isPositive: true }}
                  subtitle="حتى الآن"
                />
              </Grid>
            </Grid>
          </Box>

          {/* Financial Stats */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
              الإحصائيات المالية
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="الإيرادات الشهرية"
                  value={stats?.monthlyRevenue || 0}
                  icon={<MoneyIcon />}
                  color="success"
                  trend={{ value: 15.3, isPositive: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="إجمالي الأرباح"
                  value={stats?.totalProfit || 0}
                  icon={<AccountBalanceIcon />}
                  color="primary"
                  trend={{ value: 9.7, isPositive: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="إجمالي المصروفات"
                  value={stats?.totalExpenses || 0}
                  icon={<ReceiptIcon />}
                  color="error"
                  trend={{ value: -3.2, isPositive: false }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="إجمالي العملاء"
                  value={stats?.totalCustomers || 0}
                  icon={<PeopleIcon />}
                  color="secondary"
                  trend={{ value: 5.8, isPositive: true }}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Charts and Analytics */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
              التحليلات والرسوم البيانية
            </Typography>
            <Grid container spacing={3}>
              {/* Sales Chart */}
              <Grid item xs={12} lg={8}>
                <SalesChart data={salesData} height={350} />
              </Grid>

              {/* Top Products */}
              <Grid item xs={12} lg={4}>
                <TopProductsCard products={topProducts} />
              </Grid>
            </Grid>
          </Box>

          {/* Recent Activity */}
          <Box>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
              النشاط الحديث والتنبيهات
            </Typography>
            <Grid container spacing={3}>
              {/* Recent Sales */}
              <Grid item xs={12} lg={8}>
                <RecentSalesCard sales={recentSales} />
              </Grid>

              {/* Low Stock Alerts */}
              <Grid item xs={12} lg={4}>
                <LowStockAlertsCard alerts={lowStockAlerts} />
              </Grid>
            </Grid>
          </Box>
        </Box>
    </PageContainer>
  );
};

export default Dashboard;
