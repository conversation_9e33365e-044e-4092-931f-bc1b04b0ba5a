const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));
app.use(express.json());

// Mock data
const mockProducts = [
  {
    id: 1,
    name: 'iPhone 15 Pro',
    description: 'أحدث هاتف من آبل بمعالج A17 Pro',
    price: 4500,
    cost: 3800,
    stock: 25,
    min_stock: 5,
    category: 'هواتف ذكية',
    brand: 'Apple',
    barcode: '1234567890123',
    image: '/api/placeholder/150/150',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Samsung Galaxy S24',
    description: 'هاتف سامسونج الرائد بكاميرا متطورة',
    price: 3800,
    cost: 3200,
    stock: 15,
    min_stock: 5,
    category: 'هواتف ذكية',
    brand: 'Samsung',
    barcode: '1234567890124',
    image: '/api/placeholder/150/150',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    name: 'MacBook Pro M3',
    description: 'لابتوب آبل بمعالج M3 للمحترفين',
    price: 8500,
    cost: 7200,
    stock: 8,
    min_stock: 3,
    category: 'لابتوب',
    brand: 'Apple',
    barcode: '1234567890125',
    image: '/api/placeholder/150/150',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    name: 'AirPods Pro',
    description: 'سماعات آبل اللاسلكية مع إلغاء الضوضاء',
    price: 950,
    cost: 750,
    stock: 50,
    min_stock: 10,
    category: 'سماعات',
    brand: 'Apple',
    barcode: '1234567890126',
    image: '/api/placeholder/150/150',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 5,
    name: 'Dell XPS 13',
    description: 'لابتوب ديل خفيف الوزن للأعمال',
    price: 4200,
    cost: 3500,
    stock: 12,
    min_stock: 5,
    category: 'لابتوب',
    brand: 'Dell',
    barcode: '1234567890127',
    image: '/api/placeholder/150/150',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

const mockCustomers = [
  {
    id: 1,
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'شارع الملك فهد، الرياض',
    city: 'الرياض',
    postal_code: '12345',
    customer_type: 'individual',
    status: 'active',
    credit_limit: 5000,
    total_orders: 5,
    total_spent: 12500,
    join_date: '2024-01-15',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    name: 'فاطمة علي',
    email: '<EMAIL>',
    phone: '+966502345678',
    address: 'شارع التحلية، جدة',
    city: 'جدة',
    postal_code: '23456',
    customer_type: 'individual',
    status: 'vip',
    credit_limit: 10000,
    total_orders: 12,
    total_spent: 25000,
    join_date: '2023-12-10',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Products API
app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    data: mockProducts,
    pagination: {
      page: 1,
      limit: 50,
      total: mockProducts.length,
      pages: 1
    }
  });
});

app.get('/api/products/:id', (req, res) => {
  const product = mockProducts.find(p => p.id === parseInt(req.params.id));
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'المنتج غير موجود'
    });
  }
  res.json({
    success: true,
    data: product
  });
});

app.post('/api/products', (req, res) => {
  const newProduct = {
    id: mockProducts.length + 1,
    ...req.body,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  mockProducts.push(newProduct);
  res.status(201).json({
    success: true,
    message: 'تم إضافة المنتج بنجاح',
    data: newProduct
  });
});

app.put('/api/products/:id', (req, res) => {
  const index = mockProducts.findIndex(p => p.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'المنتج غير موجود'
    });
  }
  
  mockProducts[index] = {
    ...mockProducts[index],
    ...req.body,
    updated_at: new Date().toISOString()
  };
  
  res.json({
    success: true,
    message: 'تم تحديث المنتج بنجاح',
    data: mockProducts[index]
  });
});

app.delete('/api/products/:id', (req, res) => {
  const index = mockProducts.findIndex(p => p.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'المنتج غير موجود'
    });
  }
  
  mockProducts.splice(index, 1);
  res.json({
    success: true,
    message: 'تم حذف المنتج بنجاح'
  });
});

// Customers API
app.get('/api/customers', (req, res) => {
  res.json({
    success: true,
    data: mockCustomers,
    pagination: {
      page: 1,
      limit: 50,
      total: mockCustomers.length,
      pages: 1
    }
  });
});

app.get('/api/customers/:id', (req, res) => {
  const customer = mockCustomers.find(c => c.id === parseInt(req.params.id));
  if (!customer) {
    return res.status(404).json({
      success: false,
      message: 'العميل غير موجود'
    });
  }
  res.json({
    success: true,
    data: customer
  });
});

app.post('/api/customers', (req, res) => {
  const newCustomer = {
    id: mockCustomers.length + 1,
    ...req.body,
    total_orders: 0,
    total_spent: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  mockCustomers.push(newCustomer);
  res.status(201).json({
    success: true,
    message: 'تم إضافة العميل بنجاح',
    data: newCustomer
  });
});

app.put('/api/customers/:id', (req, res) => {
  const index = mockCustomers.findIndex(c => c.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'العميل غير موجود'
    });
  }
  
  mockCustomers[index] = {
    ...mockCustomers[index],
    ...req.body,
    updated_at: new Date().toISOString()
  };
  
  res.json({
    success: true,
    message: 'تم تحديث العميل بنجاح',
    data: mockCustomers[index]
  });
});

app.delete('/api/customers/:id', (req, res) => {
  const index = mockCustomers.findIndex(c => c.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'العميل غير موجود'
    });
  }
  
  mockCustomers.splice(index, 1);
  res.json({
    success: true,
    message: 'تم حذف العميل بنجاح'
  });
});

// Sales API
app.post('/api/sales', (req, res) => {
  const newSale = {
    id: Date.now(),
    order_number: `ORD-${Date.now()}`,
    ...req.body,
    status: 'completed',
    sale_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  // Update product stock
  if (req.body.items) {
    req.body.items.forEach(item => {
      const product = mockProducts.find(p => p.id === item.product_id);
      if (product) {
        product.stock -= item.quantity;
      }
    });
  }
  
  res.status(201).json({
    success: true,
    message: 'تم إنشاء البيع بنجاح',
    data: newSale
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'خادم API يعمل بشكل طبيعي'
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
  console.log(`📦 Products: http://localhost:${PORT}/api/products`);
  console.log(`👥 Customers: http://localhost:${PORT}/api/customers`);
});

module.exports = app;
