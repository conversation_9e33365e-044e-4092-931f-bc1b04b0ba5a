{"version": 3, "file": "get-parameter-encryption-metadata.js", "names": ["_types", "require", "_cekEntry", "_keyCrypto", "_dataType", "_request", "_interopRequireDefault", "_rpcrequestPayload", "_packet", "obj", "__esModule", "default", "getParameterEncryptionMetadata", "connection", "request", "callback", "cryptoMetadataLoaded", "metadataRequest", "Request", "error", "decryptSymmetricKeyPromises", "cekList", "paramCount", "columns", "resultRows", "isFirstRecordSet", "some", "col", "metadata", "colName", "currentOrdinal", "DescribeParameterEncryptionResultSet1", "KeyOrdinal", "value", "cekEntry", "CEKEntry", "ordinal", "add", "EncryptedKey", "DbId", "KeyId", "KeyVersion", "KeyMdVersion", "Key<PERSON><PERSON>", "ProviderName", "KeyEncryptionAlgorithm", "paramName", "DescribeParameterEncryptionResultSet2", "ParameterName", "paramIndex", "parameters", "findIndex", "param", "name", "cekOrdinal", "ColumnEncryptionKeyOrdinal", "length", "Error", "encType", "ColumnEncrytionType", "SQLServerEncryptionType", "PlainText", "cryptoMetadata", "cipherAlgorithmId", "ColumnEncryptionAlgorithm", "encryptionType", "normalizationRuleVersion", "<PERSON><PERSON><PERSON>", "from", "NormalizationRuleVersion", "push", "decryptSymmetricKey", "config", "options", "forceEncrypt", "sqlTextOrProcedure", "Promise", "all", "then", "process", "nextTick", "addParameter", "TYPES", "NVarChar", "makeParamsParameter", "on", "makeRequest", "TYPE", "RPC_REQUEST", "RpcRequestPayload", "currentTransactionDescriptor", "databaseCollation", "exports"], "sources": ["../../src/always-encrypted/get-parameter-encryption-metadata.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { SQLServerEncryptionType, type CryptoMetadata, DescribeParameterEncryptionResultSet1, DescribeParameterEncryptionResultSet2 } from './types';\nimport { CEKEntry } from './cek-entry';\nimport { decryptSymmetricKey } from './key-crypto';\nimport { typeByName as TYPES, type Parameter } from '../data-type';\nimport Request from '../request';\nimport Connection from '../connection';\nimport RpcRequestPayload from '../rpcrequest-payload';\nimport { TYPE } from '../packet';\n\nexport const getParameterEncryptionMetadata = (connection: Connection, request: Request, callback: (error?: Error) => void) => {\n  if (request.cryptoMetadataLoaded === true) {\n    return callback();\n  }\n\n  const metadataRequest = new Request('sp_describe_parameter_encryption', (error) => {\n    if (error) {\n      return callback(error);\n    }\n\n    const decryptSymmetricKeyPromises: Promise<void>[] = [];\n    const cekList: CEKEntry[] = [];\n    let paramCount = 0;\n\n    for (const columns of resultRows) {\n      try {\n        const isFirstRecordSet = columns.some((col: any) => (col && col.metadata && col.metadata.colName) === 'database_id');\n        if (isFirstRecordSet === true) {\n          const currentOrdinal = columns[DescribeParameterEncryptionResultSet1.KeyOrdinal].value;\n          let cekEntry: CEKEntry;\n          if (!cekList[currentOrdinal]) {\n            cekEntry = new CEKEntry(currentOrdinal);\n            cekList[cekEntry.ordinal] = cekEntry;\n          } else {\n            cekEntry = cekList[currentOrdinal];\n          }\n          cekEntry.add(columns[DescribeParameterEncryptionResultSet1.EncryptedKey].value,\n                       columns[DescribeParameterEncryptionResultSet1.DbId].value,\n                       columns[DescribeParameterEncryptionResultSet1.KeyId].value,\n                       columns[DescribeParameterEncryptionResultSet1.KeyVersion].value,\n                       columns[DescribeParameterEncryptionResultSet1.KeyMdVersion].value,\n                       columns[DescribeParameterEncryptionResultSet1.KeyPath].value,\n                       columns[DescribeParameterEncryptionResultSet1.ProviderName].value,\n                       columns[DescribeParameterEncryptionResultSet1.KeyEncryptionAlgorithm].value);\n        } else {\n          paramCount++;\n          const paramName: string = columns[DescribeParameterEncryptionResultSet2.ParameterName].value;\n          const paramIndex: number = request.parameters.findIndex((param: Parameter) => paramName === `@${param.name}`);\n          const cekOrdinal: number = columns[DescribeParameterEncryptionResultSet2.ColumnEncryptionKeyOrdinal].value;\n          const cekEntry: CEKEntry = cekList[cekOrdinal];\n\n          if (cekEntry && cekList.length < cekOrdinal) {\n            return callback(new Error(`Internal error. The referenced column encryption key ordinal \"${cekOrdinal}\" is missing in the encryption metadata returned by sp_describe_parameter_encryption. Max ordinal is \"${cekList.length}\".`));\n          }\n\n          const encType = columns[DescribeParameterEncryptionResultSet2.ColumnEncrytionType].value;\n          if (SQLServerEncryptionType.PlainText !== encType) {\n            request.parameters[paramIndex].cryptoMetadata = {\n              cekEntry: cekEntry,\n              ordinal: cekOrdinal,\n              cipherAlgorithmId: columns[DescribeParameterEncryptionResultSet2.ColumnEncryptionAlgorithm].value,\n              encryptionType: encType,\n              normalizationRuleVersion: Buffer.from([columns[DescribeParameterEncryptionResultSet2.NormalizationRuleVersion].value]),\n            };\n            decryptSymmetricKeyPromises.push(decryptSymmetricKey(request.parameters[paramIndex].cryptoMetadata as CryptoMetadata, connection.config.options));\n          } else if (request.parameters[paramIndex].forceEncrypt === true) {\n            return callback(new Error(`Cannot execute statement or procedure ${request.sqlTextOrProcedure} because Force Encryption was set as true for parameter ${paramIndex + 1} and the database expects this parameter to be sent as plaintext. This may be due to a configuration error.`));\n          }\n        }\n      } catch {\n        return callback(new Error(`Internal error. Unable to parse parameter encryption metadata in statement or procedure \"${request.sqlTextOrProcedure}\"`));\n      }\n    }\n\n    if (paramCount !== request.parameters.length) {\n      return callback(new Error(`Internal error. Metadata for some parameters in statement or procedure \"${request.sqlTextOrProcedure}\" is missing in the resultset returned by sp_describe_parameter_encryption.`));\n    }\n\n    return Promise.all(decryptSymmetricKeyPromises).then(() => {\n      request.cryptoMetadataLoaded = true;\n      process.nextTick(callback);\n    }, (error) => {\n      process.nextTick(callback, error);\n    });\n  });\n\n  metadataRequest.addParameter('tsql', TYPES.NVarChar, request.sqlTextOrProcedure);\n  if (request.parameters.length) {\n    metadataRequest.addParameter('params', TYPES.NVarChar, metadataRequest.makeParamsParameter(request.parameters));\n  }\n\n  const resultRows: any[] = [];\n\n  metadataRequest.on('row', (columns: any) => {\n    resultRows.push(columns);\n  });\n\n  connection.makeRequest(metadataRequest, TYPE.RPC_REQUEST, new RpcRequestPayload(metadataRequest.sqlTextOrProcedure!, metadataRequest.parameters, connection.currentTransactionDescriptor(), connection.config.options, connection.databaseCollation));\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAC,sBAAA,CAAAL,OAAA;AAEA,IAAAM,kBAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AAAiC,SAAAK,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAVjC;AACA;;AAWO,MAAMG,8BAA8B,GAAGA,CAACC,UAAsB,EAAEC,OAAgB,EAAEC,QAAiC,KAAK;EAC7H,IAAID,OAAO,CAACE,oBAAoB,KAAK,IAAI,EAAE;IACzC,OAAOD,QAAQ,CAAC,CAAC;EACnB;EAEA,MAAME,eAAe,GAAG,IAAIC,gBAAO,CAAC,kCAAkC,EAAGC,KAAK,IAAK;IACjF,IAAIA,KAAK,EAAE;MACT,OAAOJ,QAAQ,CAACI,KAAK,CAAC;IACxB;IAEA,MAAMC,2BAA4C,GAAG,EAAE;IACvD,MAAMC,OAAmB,GAAG,EAAE;IAC9B,IAAIC,UAAU,GAAG,CAAC;IAElB,KAAK,MAAMC,OAAO,IAAIC,UAAU,EAAE;MAChC,IAAI;QACF,MAAMC,gBAAgB,GAAGF,OAAO,CAACG,IAAI,CAAEC,GAAQ,IAAK,CAACA,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACC,QAAQ,CAACC,OAAO,MAAM,aAAa,CAAC;QACpH,IAAIJ,gBAAgB,KAAK,IAAI,EAAE;UAC7B,MAAMK,cAAc,GAAGP,OAAO,CAACQ,4CAAqC,CAACC,UAAU,CAAC,CAACC,KAAK;UACtF,IAAIC,QAAkB;UACtB,IAAI,CAACb,OAAO,CAACS,cAAc,CAAC,EAAE;YAC5BI,QAAQ,GAAG,IAAIC,kBAAQ,CAACL,cAAc,CAAC;YACvCT,OAAO,CAACa,QAAQ,CAACE,OAAO,CAAC,GAAGF,QAAQ;UACtC,CAAC,MAAM;YACLA,QAAQ,GAAGb,OAAO,CAACS,cAAc,CAAC;UACpC;UACAI,QAAQ,CAACG,GAAG,CAACd,OAAO,CAACQ,4CAAqC,CAACO,YAAY,CAAC,CAACL,KAAK,EACjEV,OAAO,CAACQ,4CAAqC,CAACQ,IAAI,CAAC,CAACN,KAAK,EACzDV,OAAO,CAACQ,4CAAqC,CAACS,KAAK,CAAC,CAACP,KAAK,EAC1DV,OAAO,CAACQ,4CAAqC,CAACU,UAAU,CAAC,CAACR,KAAK,EAC/DV,OAAO,CAACQ,4CAAqC,CAACW,YAAY,CAAC,CAACT,KAAK,EACjEV,OAAO,CAACQ,4CAAqC,CAACY,OAAO,CAAC,CAACV,KAAK,EAC5DV,OAAO,CAACQ,4CAAqC,CAACa,YAAY,CAAC,CAACX,KAAK,EACjEV,OAAO,CAACQ,4CAAqC,CAACc,sBAAsB,CAAC,CAACZ,KAAK,CAAC;QAC3F,CAAC,MAAM;UACLX,UAAU,EAAE;UACZ,MAAMwB,SAAiB,GAAGvB,OAAO,CAACwB,4CAAqC,CAACC,aAAa,CAAC,CAACf,KAAK;UAC5F,MAAMgB,UAAkB,GAAGnC,OAAO,CAACoC,UAAU,CAACC,SAAS,CAAEC,KAAgB,IAAKN,SAAS,KAAM,IAAGM,KAAK,CAACC,IAAK,EAAC,CAAC;UAC7G,MAAMC,UAAkB,GAAG/B,OAAO,CAACwB,4CAAqC,CAACQ,0BAA0B,CAAC,CAACtB,KAAK;UAC1G,MAAMC,QAAkB,GAAGb,OAAO,CAACiC,UAAU,CAAC;UAE9C,IAAIpB,QAAQ,IAAIb,OAAO,CAACmC,MAAM,GAAGF,UAAU,EAAE;YAC3C,OAAOvC,QAAQ,CAAC,IAAI0C,KAAK,CAAE,iEAAgEH,UAAW,yGAAwGjC,OAAO,CAACmC,MAAO,IAAG,CAAC,CAAC;UACpO;UAEA,MAAME,OAAO,GAAGnC,OAAO,CAACwB,4CAAqC,CAACY,mBAAmB,CAAC,CAAC1B,KAAK;UACxF,IAAI2B,8BAAuB,CAACC,SAAS,KAAKH,OAAO,EAAE;YACjD5C,OAAO,CAACoC,UAAU,CAACD,UAAU,CAAC,CAACa,cAAc,GAAG;cAC9C5B,QAAQ,EAAEA,QAAQ;cAClBE,OAAO,EAAEkB,UAAU;cACnBS,iBAAiB,EAAExC,OAAO,CAACwB,4CAAqC,CAACiB,yBAAyB,CAAC,CAAC/B,KAAK;cACjGgC,cAAc,EAAEP,OAAO;cACvBQ,wBAAwB,EAAEC,MAAM,CAACC,IAAI,CAAC,CAAC7C,OAAO,CAACwB,4CAAqC,CAACsB,wBAAwB,CAAC,CAACpC,KAAK,CAAC;YACvH,CAAC;YACDb,2BAA2B,CAACkD,IAAI,CAAC,IAAAC,8BAAmB,EAACzD,OAAO,CAACoC,UAAU,CAACD,UAAU,CAAC,CAACa,cAAc,EAAoBjD,UAAU,CAAC2D,MAAM,CAACC,OAAO,CAAC,CAAC;UACnJ,CAAC,MAAM,IAAI3D,OAAO,CAACoC,UAAU,CAACD,UAAU,CAAC,CAACyB,YAAY,KAAK,IAAI,EAAE;YAC/D,OAAO3D,QAAQ,CAAC,IAAI0C,KAAK,CAAE,yCAAwC3C,OAAO,CAAC6D,kBAAmB,2DAA0D1B,UAAU,GAAG,CAAE,6GAA4G,CAAC,CAAC;UACvR;QACF;MACF,CAAC,CAAC,MAAM;QACN,OAAOlC,QAAQ,CAAC,IAAI0C,KAAK,CAAE,4FAA2F3C,OAAO,CAAC6D,kBAAmB,GAAE,CAAC,CAAC;MACvJ;IACF;IAEA,IAAIrD,UAAU,KAAKR,OAAO,CAACoC,UAAU,CAACM,MAAM,EAAE;MAC5C,OAAOzC,QAAQ,CAAC,IAAI0C,KAAK,CAAE,2EAA0E3C,OAAO,CAAC6D,kBAAmB,6EAA4E,CAAC,CAAC;IAChN;IAEA,OAAOC,OAAO,CAACC,GAAG,CAACzD,2BAA2B,CAAC,CAAC0D,IAAI,CAAC,MAAM;MACzDhE,OAAO,CAACE,oBAAoB,GAAG,IAAI;MACnC+D,OAAO,CAACC,QAAQ,CAACjE,QAAQ,CAAC;IAC5B,CAAC,EAAGI,KAAK,IAAK;MACZ4D,OAAO,CAACC,QAAQ,CAACjE,QAAQ,EAAEI,KAAK,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFF,eAAe,CAACgE,YAAY,CAAC,MAAM,EAAEC,oBAAK,CAACC,QAAQ,EAAErE,OAAO,CAAC6D,kBAAkB,CAAC;EAChF,IAAI7D,OAAO,CAACoC,UAAU,CAACM,MAAM,EAAE;IAC7BvC,eAAe,CAACgE,YAAY,CAAC,QAAQ,EAAEC,oBAAK,CAACC,QAAQ,EAAElE,eAAe,CAACmE,mBAAmB,CAACtE,OAAO,CAACoC,UAAU,CAAC,CAAC;EACjH;EAEA,MAAM1B,UAAiB,GAAG,EAAE;EAE5BP,eAAe,CAACoE,EAAE,CAAC,KAAK,EAAG9D,OAAY,IAAK;IAC1CC,UAAU,CAAC8C,IAAI,CAAC/C,OAAO,CAAC;EAC1B,CAAC,CAAC;EAEFV,UAAU,CAACyE,WAAW,CAACrE,eAAe,EAAEsE,YAAI,CAACC,WAAW,EAAE,IAAIC,0BAAiB,CAACxE,eAAe,CAAC0D,kBAAkB,EAAG1D,eAAe,CAACiC,UAAU,EAAErC,UAAU,CAAC6E,4BAA4B,CAAC,CAAC,EAAE7E,UAAU,CAAC2D,MAAM,CAACC,OAAO,EAAE5D,UAAU,CAAC8E,iBAAiB,CAAC,CAAC;AACvP,CAAC;AAACC,OAAA,CAAAhF,8BAAA,GAAAA,8BAAA"}