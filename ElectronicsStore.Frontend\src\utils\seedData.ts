import { categoryService } from '../services/categoryService';
import { productService } from '../services/productService';

// Sample categories (simplified)
const sampleCategories = [
  { name: 'Electronics' },
  { name: 'Phones' },
  { name: 'Laptops' },
];

// Sample products (simplified)
const sampleProducts = [
  {
    name: 'iPhone 15',
    barcode: '123456789001',
    description: 'Latest iPhone model',
    categoryId: 2, // Phones
    defaultCostPrice: 800,
    defaultSellingPrice: 1000,
    minSellingPrice: 900,
  },
  {
    name: 'MacBook Pro',
    barcode: '123456789002',
    description: 'Apple laptop with M3 chip',
    categoryId: 3, // Laptops
    defaultCostPrice: 1500,
    defaultSellingPrice: 2000,
    minSellingPrice: 1800,
  },
];

// Seed functions
export const seedCategories = async () => {
  try {
    console.log('🌱 إضافة الفئات التجريبية...');
    
    for (const category of sampleCategories) {
      try {
        await categoryService.createCategory(category);
        console.log(`✅ تم إضافة فئة: ${category.name}`);
      } catch (error: any) {
        if (error.message?.includes('already exists') || error.status === 409) {
          console.log(`⚠️ الفئة موجودة بالفعل: ${category.name}`);
        } else {
          console.error(`❌ خطأ في إضافة فئة ${category.name}:`, error.message);
        }
      }
    }
    
    console.log('✅ تم الانتهاء من إضافة الفئات');
  } catch (error) {
    console.error('❌ خطأ عام في إضافة الفئات:', error);
  }
};

export const seedProducts = async () => {
  try {
    console.log('🌱 إضافة المنتجات التجريبية...');
    
    for (const product of sampleProducts) {
      try {
        await productService.createProduct(product);
        console.log(`✅ تم إضافة منتج: ${product.name}`);
      } catch (error: any) {
        if (error.message?.includes('already exists') || error.status === 409) {
          console.log(`⚠️ المنتج موجود بالفعل: ${product.name}`);
        } else {
          console.error(`❌ خطأ في إضافة منتج ${product.name}:`, error.message);
        }
      }
    }
    
    console.log('✅ تم الانتهاء من إضافة المنتجات');
  } catch (error) {
    console.error('❌ خطأ عام في إضافة المنتجات:', error);
  }
};

export const seedAllData = async () => {
  console.log('🚀 بدء إضافة البيانات التجريبية...');
  
  // إضافة الفئات أولاً
  await seedCategories();
  
  // انتظار قليل ثم إضافة المنتجات
  setTimeout(async () => {
    await seedProducts();
    console.log('🎉 تم الانتهاء من إضافة جميع البيانات التجريبية!');
  }, 2000);
};
