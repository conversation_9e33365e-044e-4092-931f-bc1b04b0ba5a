import { categoryService } from '../services/categoryService';
import { productService } from '../services/productService';

// Sample categories
const sampleCategories = [
  { name: 'هواتف ذكية' },
  { name: 'ل<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'إكسسوارات' },
  { name: 'أجهزة لوحية' },
  { name: 'سماعات' },
  { name: 'شواحن' },
  { name: 'كاميرات' },
  { name: 'ساعات ذكية' },
];

// Sample products
const sampleProducts = [
  {
    name: 'iPhone 15 Pro',
    barcode: '123456789001',
    description: 'أحدث هاتف من آبل مع كاميرا متطورة وشاشة Super Retina XDR',
    categoryId: 1, // هواتف ذكية
    defaultCostPrice: 3200,
    defaultSellingPrice: 4500,
    minSellingPrice: 4000,
  },
  {
    name: 'Samsung Galaxy S24',
    barcode: '123456789002',
    description: 'هاتف سامسونج الرائد مع شاشة AMOLED ومعالج Snapdragon',
    categoryId: 1, // هواتف ذكية
    defaultCostPrice: 2800,
    defaultSellingPrice: 3800,
    minSellingPrice: 3500,
  },
  {
    name: 'MacBook Pro M3',
    barcode: '123456789003',
    description: 'لابتوب آبل بمعالج M3 الجديد وذاكرة 16GB',
    categoryId: 2, // لابتوب
    defaultCostPrice: 6200,
    defaultSellingPrice: 8500,
    minSellingPrice: 7800,
  },
  {
    name: 'Dell XPS 13',
    barcode: '123456789004',
    description: 'لابتوب ديل بتصميم أنيق وأداء قوي مع معالج Intel i7',
    categoryId: 2, // لابتوب
    defaultCostPrice: 3100,
    defaultSellingPrice: 4200,
    minSellingPrice: 3800,
  },
  {
    name: 'AirPods Pro',
    barcode: '123456789005',
    description: 'سماعات لاسلكية مع إلغاء الضوضاء النشط',
    categoryId: 5, // سماعات
    defaultCostPrice: 650,
    defaultSellingPrice: 950,
    minSellingPrice: 850,
  },
  {
    name: 'iPad Air',
    barcode: '123456789006',
    description: 'جهاز لوحي من آبل بشاشة 10.9 بوصة ومعالج M1',
    categoryId: 4, // أجهزة لوحية
    defaultCostPrice: 2200,
    defaultSellingPrice: 3000,
    minSellingPrice: 2700,
  },
];

// Seed functions
export const seedCategories = async () => {
  try {
    console.log('🌱 إضافة الفئات التجريبية...');
    
    for (const category of sampleCategories) {
      try {
        await categoryService.createCategory(category);
        console.log(`✅ تم إضافة فئة: ${category.name}`);
      } catch (error: any) {
        if (error.message?.includes('already exists') || error.status === 409) {
          console.log(`⚠️ الفئة موجودة بالفعل: ${category.name}`);
        } else {
          console.error(`❌ خطأ في إضافة فئة ${category.name}:`, error.message);
        }
      }
    }
    
    console.log('✅ تم الانتهاء من إضافة الفئات');
  } catch (error) {
    console.error('❌ خطأ عام في إضافة الفئات:', error);
  }
};

export const seedProducts = async () => {
  try {
    console.log('🌱 إضافة المنتجات التجريبية...');
    
    for (const product of sampleProducts) {
      try {
        await productService.createProduct(product);
        console.log(`✅ تم إضافة منتج: ${product.name}`);
      } catch (error: any) {
        if (error.message?.includes('already exists') || error.status === 409) {
          console.log(`⚠️ المنتج موجود بالفعل: ${product.name}`);
        } else {
          console.error(`❌ خطأ في إضافة منتج ${product.name}:`, error.message);
        }
      }
    }
    
    console.log('✅ تم الانتهاء من إضافة المنتجات');
  } catch (error) {
    console.error('❌ خطأ عام في إضافة المنتجات:', error);
  }
};

export const seedAllData = async () => {
  console.log('🚀 بدء إضافة البيانات التجريبية...');
  
  // إضافة الفئات أولاً
  await seedCategories();
  
  // انتظار قليل ثم إضافة المنتجات
  setTimeout(async () => {
    await seedProducts();
    console.log('🎉 تم الانتهاء من إضافة جميع البيانات التجريبية!');
  }, 2000);
};
