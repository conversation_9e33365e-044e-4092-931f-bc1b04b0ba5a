{"name": "bcryptjs", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "version": "2.4.3", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/shaneGirish)", "<PERSON> <> (https://github.com/alexmurray)", "<PERSON> <> (https://github.com/<PERSON>)", "<PERSON> <> (https://github.com/geekymole)", "<PERSON> <<EMAIL>> (https://github.com/nisaacson)"], "repository": {"type": "url", "url": "https://github.com/dcodeIO/bcrypt.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "index.js", "browser": "dist/bcrypt.js", "dependencies": {}, "devDependencies": {"testjs": "~1", "closurecompiler": "~1", "metascript": "~0.18", "bcrypt": "latest", "utfx": "~1"}, "license": "MIT", "scripts": {"test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=SIMPLE_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz", "make": "npm run build && npm run compile && npm run compress && npm test"}}