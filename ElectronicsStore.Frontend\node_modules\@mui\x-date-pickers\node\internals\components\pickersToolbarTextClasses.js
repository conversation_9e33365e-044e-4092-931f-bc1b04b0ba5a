"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getPickersToolbarTextUtilityClass = getPickersToolbarTextUtilityClass;
exports.pickersToolbarTextClasses = void 0;
var _utils = require("@mui/utils");
function getPickersToolbarTextUtilityClass(slot) {
  return (0, _utils.unstable_generateUtilityClass)('MuiPickersToolbarText', slot);
}
const pickersToolbarTextClasses = exports.pickersToolbarTextClasses = (0, _utils.unstable_generateUtilityClasses)('MuiPickersToolbarText', ['root', 'selected']);