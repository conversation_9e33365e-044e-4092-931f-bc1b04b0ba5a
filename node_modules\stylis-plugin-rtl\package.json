{"name": "stylis-plugin-rtl", "version": "2.1.1", "description": "Fork of stylis-rtl, uses cssjanus under the hood to flip style orientations for RTL", "module": "dist/stylis-rtl.js", "main": "dist/cjs/stylis-rtl.js", "types": "dist/stylis-rtl.d.ts", "scripts": {"build": "tsc && tsc --module commonjs --outDir dist/cjs", "prepublishOnly": "yarn build", "test": "jest"}, "files": ["dist", "types"], "repository": {"type": "git", "url": "git+https://github.com/styled-components/stylis-plugin-rtl.git"}, "keywords": ["rtl", "bidi", "stylis", "emotion", "emotion-js", "css", "preprocessor", "styled-components"], "author": "<PERSON><PERSON>", "contributors": ["<PERSON> <<EMAIL>> (https://probablyup.com)"], "license": "MIT", "bugs": {"url": "https://github.com/styled-components/stylis-plugin-rtl/issues"}, "homepage": "https://github.com/styled-components/stylis-plugin-rtl#readme", "peerDependencies": {"stylis": "4.x"}, "devDependencies": {"@types/jest": "^27.0.2", "@types/stylis": "^4.0.2", "jest": "^27.3.1", "prettier": "^2.4.1", "stylis": "^4.0.2", "ts-jest": "^27.0.7", "typescript": "^4.4.4"}, "dependencies": {"cssjanus": "^2.0.1"}}