"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getMultiSectionDigitalClockUtilityClass = getMultiSectionDigitalClockUtilityClass;
exports.multiSectionDigitalClockClasses = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
function getMultiSectionDigitalClockUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiMultiSectionDigitalClock', slot);
}
const multiSectionDigitalClockClasses = exports.multiSectionDigitalClockClasses = (0, _generateUtilityClasses.default)('MuiMultiSectionDigitalClock', ['root']);