"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _useSlider = require("./useSlider");
Object.keys(_useSlider).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _useSlider[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _useSlider[key];
    }
  });
});
var _useSlider2 = require("./useSlider.types");
Object.keys(_useSlider2).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _useSlider2[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _useSlider2[key];
    }
  });
});