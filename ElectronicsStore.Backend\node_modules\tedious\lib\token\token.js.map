{"version": 3, "file": "token.js", "names": ["TYPE", "ALTMETADATA", "ALTROW", "COLMETADATA", "COLINFO", "DONE", "DONEPROC", "DONEINPROC", "ENVCHANGE", "ERROR", "FEATUREEXTACK", "FEDAUTHINFO", "INFO", "LOGINACK", "NBCROW", "OFFSET", "ORDER", "RETURNSTATUS", "RETURNVALUE", "ROW", "SSPI", "TABNAME", "exports", "Token", "constructor", "name", "handler<PERSON>ame", "ColMetadataToken", "columns", "DoneToken", "more", "sqlError", "attention", "serverError", "rowCount", "curCmd", "DoneInProcToken", "DoneProcToken", "DatabaseEnvChangeToken", "newValue", "oldValue", "type", "LanguageEnvChangeToken", "CharsetEnvChangeToken", "PacketSizeEnvChangeToken", "BeginTransactionEnvChangeToken", "CommitTransactionEnvChangeToken", "RollbackTransactionEnvChangeToken", "DatabaseMirroringPartnerEnvChangeToken", "ResetConnectionEnvChangeToken", "CollationChangeToken", "RoutingEnvChangeToken", "FeatureExtAckToken", "fedAuth", "utf8Support", "FedAuthInfoToken", "spn", "<PERSON>url", "InfoMessageToken", "number", "state", "class", "clazz", "message", "serverName", "procName", "lineNumber", "ErrorMessageToken", "LoginAckToken", "interface", "interfaze", "tdsVersion", "progName", "progVersion", "NBCRowToken", "OrderToken", "orderColumns", "ReturnStatusToken", "value", "ReturnValueToken", "paramOrdinal", "paramName", "metadata", "RowToken", "SSPIToken", "ntlmpacket", "ntlmpacketBuffer"], "sources": ["../../src/token/token.ts"], "sourcesContent": ["import { Collation } from '../collation';\nimport { type Metadata } from '../metadata-parser';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\nimport { TokenHandler } from './handler';\n\nexport const TYPE = {\n  ALTMETADATA: 0x88,\n  ALTROW: 0xD3,\n  COLMETADATA: 0x81,\n  COLINFO: 0xA5,\n  DONE: 0xFD,\n  DONEPROC: 0xFE,\n  DONEINPROC: 0xFF,\n  ENVCHANGE: 0xE3,\n  ERROR: 0xAA,\n  FEATUREEXTACK: 0xAE,\n  FEDAUTHINFO: 0xEE,\n  INFO: 0xAB,\n  LOGINACK: 0xAD,\n  NBCROW: 0xD2,\n  OFFSET: 0x78,\n  ORDER: 0xA9,\n  RETURNSTATUS: 0x79,\n  RETURNVALUE: 0xAC,\n  ROW: 0xD1,\n  SSPI: 0xED,\n  TABNAME: 0xA4\n};\n\ntype HandlerName = keyof TokenHandler;\n\nexport abstract class Token {\n  declare name: string;\n  declare handlerName: keyof TokenHandler;\n\n  constructor(name: string, handlerName: HandlerName) {\n    this.name = name;\n    this.handlerName = handlerName;\n  }\n}\n\nexport class ColMetadataToken extends Token {\n  declare name: 'COLMETADATA';\n  declare handlerName: 'onColMetadata';\n\n  declare columns: ColumnMetadata[];\n\n  constructor(columns: ColumnMetadata[]) {\n    super('COLMETADATA', 'onColMetadata');\n\n    this.columns = columns;\n  }\n}\n\nexport class DoneToken extends Token {\n  declare name: 'DONE';\n  declare handlerName: 'onDone';\n\n  declare more: boolean;\n  declare sqlError: boolean;\n  declare attention: boolean;\n  declare serverError: boolean;\n  declare rowCount: number | undefined;\n  declare curCmd: number;\n\n  constructor({ more, sqlError, attention, serverError, rowCount, curCmd }: { more: boolean, sqlError: boolean, attention: boolean, serverError: boolean, rowCount: number | undefined, curCmd: number }) {\n    super('DONE', 'onDone');\n\n    this.more = more;\n    this.sqlError = sqlError;\n    this.attention = attention;\n    this.serverError = serverError;\n    this.rowCount = rowCount;\n    this.curCmd = curCmd;\n  }\n}\n\nexport class DoneInProcToken extends Token {\n  declare name: 'DONEINPROC';\n  declare handlerName: 'onDoneInProc';\n\n  declare more: boolean;\n  declare sqlError: boolean;\n  declare attention: boolean;\n  declare serverError: boolean;\n  declare rowCount: number | undefined;\n  declare curCmd: number;\n\n  constructor({ more, sqlError, attention, serverError, rowCount, curCmd }: { more: boolean, sqlError: boolean, attention: boolean, serverError: boolean, rowCount: number | undefined, curCmd: number }) {\n    super('DONEINPROC', 'onDoneInProc');\n\n    this.more = more;\n    this.sqlError = sqlError;\n    this.attention = attention;\n    this.serverError = serverError;\n    this.rowCount = rowCount;\n    this.curCmd = curCmd;\n  }\n}\n\nexport class DoneProcToken extends Token {\n  declare name: 'DONEPROC';\n  declare handlerName: 'onDoneProc';\n\n  declare more: boolean;\n  declare sqlError: boolean;\n  declare attention: boolean;\n  declare serverError: boolean;\n  declare rowCount: number | undefined;\n  declare curCmd: number;\n\n  constructor({ more, sqlError, attention, serverError, rowCount, curCmd }: { more: boolean, sqlError: boolean, attention: boolean, serverError: boolean, rowCount: number | undefined, curCmd: number }) {\n    super('DONEPROC', 'onDoneProc');\n\n    this.more = more;\n    this.sqlError = sqlError;\n    this.attention = attention;\n    this.serverError = serverError;\n    this.rowCount = rowCount;\n    this.curCmd = curCmd;\n  }\n}\n\nexport class DatabaseEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onDatabaseChange';\n\n  declare type: 'DATABASE';\n  declare newValue: string;\n  declare oldValue: string;\n\n  constructor(newValue: string, oldValue: string) {\n    super('ENVCHANGE', 'onDatabaseChange');\n\n    this.type = 'DATABASE';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class LanguageEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onLanguageChange';\n\n  declare type: 'LANGUAGE';\n  declare newValue: string;\n  declare oldValue: string;\n\n  constructor(newValue: string, oldValue: string) {\n    super('ENVCHANGE', 'onLanguageChange');\n\n    this.type = 'LANGUAGE';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class CharsetEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onCharsetChange';\n\n  declare type: 'CHARSET';\n  declare newValue: string;\n  declare oldValue: string;\n\n  constructor(newValue: string, oldValue: string) {\n    super('ENVCHANGE', 'onCharsetChange');\n\n    this.type = 'CHARSET';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class PacketSizeEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onPacketSizeChange';\n\n  declare type: 'PACKET_SIZE';\n  declare newValue: number;\n  declare oldValue: number;\n\n  constructor(newValue: number, oldValue: number) {\n    super('ENVCHANGE', 'onPacketSizeChange');\n\n    this.type = 'PACKET_SIZE';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class BeginTransactionEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onBeginTransaction';\n\n  declare type: 'BEGIN_TXN';\n  declare newValue: Buffer;\n  declare oldValue: Buffer;\n\n  constructor(newValue: Buffer, oldValue: Buffer) {\n    super('ENVCHANGE', 'onBeginTransaction');\n\n    this.type = 'BEGIN_TXN';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class CommitTransactionEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onCommitTransaction';\n\n  declare type: 'COMMIT_TXN';\n  declare newValue: Buffer;\n  declare oldValue: Buffer;\n\n  constructor(newValue: Buffer, oldValue: Buffer) {\n    super('ENVCHANGE', 'onCommitTransaction');\n\n    this.type = 'COMMIT_TXN';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class RollbackTransactionEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onRollbackTransaction';\n\n  declare type: 'ROLLBACK_TXN';\n  declare oldValue: Buffer;\n  declare newValue: Buffer;\n\n  constructor(newValue: Buffer, oldValue: Buffer) {\n    super('ENVCHANGE', 'onRollbackTransaction');\n\n    this.type = 'ROLLBACK_TXN';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class DatabaseMirroringPartnerEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onDatabaseMirroringPartner';\n\n  declare type: 'DATABASE_MIRRORING_PARTNER';\n  declare oldValue: string;\n  declare newValue: string;\n\n  constructor(newValue: string, oldValue: string) {\n    super('ENVCHANGE', 'onDatabaseMirroringPartner');\n\n    this.type = 'DATABASE_MIRRORING_PARTNER';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class ResetConnectionEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onResetConnection';\n\n  declare type: 'RESET_CONNECTION';\n  declare oldValue: Buffer;\n  declare newValue: Buffer;\n\n  constructor(newValue: Buffer, oldValue: Buffer) {\n    super('ENVCHANGE', 'onResetConnection');\n\n    this.type = 'RESET_CONNECTION';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport type EnvChangeToken =\n  DatabaseEnvChangeToken |\n  LanguageEnvChangeToken |\n  CharsetEnvChangeToken |\n  PacketSizeEnvChangeToken |\n  BeginTransactionEnvChangeToken |\n  CommitTransactionEnvChangeToken |\n  RollbackTransactionEnvChangeToken |\n  DatabaseMirroringPartnerEnvChangeToken |\n  ResetConnectionEnvChangeToken |\n  RoutingEnvChangeToken |\n  CollationChangeToken;\n\nexport class CollationChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onSqlCollationChange';\n\n  declare type: 'SQL_COLLATION';\n  declare oldValue: Collation | undefined;\n  declare newValue: Collation | undefined;\n\n  constructor(newValue: Collation | undefined, oldValue: Collation | undefined) {\n    super('ENVCHANGE', 'onSqlCollationChange');\n\n    this.type = 'SQL_COLLATION';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class RoutingEnvChangeToken extends Token {\n  declare name: 'ENVCHANGE';\n  declare handlerName: 'onRoutingChange';\n\n  declare type: 'ROUTING_CHANGE';\n  declare newValue: { protocol: number, port: number, server: string };\n  declare oldValue: Buffer;\n\n  constructor(newValue: { protocol: number, port: number, server: string }, oldValue: Buffer) {\n    super('ENVCHANGE', 'onRoutingChange');\n\n    this.type = 'ROUTING_CHANGE';\n    this.newValue = newValue;\n    this.oldValue = oldValue;\n  }\n}\n\nexport class FeatureExtAckToken extends Token {\n  declare name: 'FEATUREEXTACK';\n  declare handlerName: 'onFeatureExtAck';\n\n  declare fedAuth: Buffer | undefined;\n\n  /** Value of UTF8_SUPPORT acknowledgement.\n   *\n   * undefined when UTF8_SUPPORT not included in token. */\n  declare utf8Support: boolean | undefined;\n\n  constructor(fedAuth: Buffer | undefined, utf8Support: boolean | undefined) {\n    super('FEATUREEXTACK', 'onFeatureExtAck');\n\n    this.fedAuth = fedAuth;\n    this.utf8Support = utf8Support;\n  }\n}\n\nexport class FedAuthInfoToken extends Token {\n  declare name: 'FEDAUTHINFO';\n  declare handlerName: 'onFedAuthInfo';\n\n  declare spn: string | undefined;\n  declare stsurl: string | undefined;\n\n  constructor(spn: string | undefined, stsurl: string | undefined) {\n    super('FEDAUTHINFO', 'onFedAuthInfo');\n\n    this.spn = spn;\n    this.stsurl = stsurl;\n  }\n}\n\nexport class InfoMessageToken extends Token {\n  declare name: 'INFO';\n  declare handlerName: 'onInfoMessage';\n\n  declare number: number;\n  declare state: number;\n  declare class: number;\n  declare message: string;\n  declare serverName: string;\n  declare procName: string;\n  declare lineNumber: number;\n\n  constructor({ number, state, class: clazz, message, serverName, procName, lineNumber }: { number: number, state: number, class: number, message: string, serverName: string, procName: string, lineNumber: number }) {\n    super('INFO', 'onInfoMessage');\n\n    this.number = number;\n    this.state = state;\n    this.class = clazz;\n    this.message = message;\n    this.serverName = serverName;\n    this.procName = procName;\n    this.lineNumber = lineNumber;\n  }\n}\n\nexport class ErrorMessageToken extends Token {\n  declare name: 'ERROR';\n  declare handlerName: 'onErrorMessage';\n\n  declare number: number;\n  declare state: number;\n  declare class: number;\n  declare message: string;\n  declare serverName: string;\n  declare procName: string;\n  declare lineNumber: number;\n\n  constructor({ number, state, class: clazz, message, serverName, procName, lineNumber }: { number: number, state: number, class: number, message: string, serverName: string, procName: string, lineNumber: number }) {\n    super('ERROR', 'onErrorMessage');\n\n    this.number = number;\n    this.state = state;\n    this.class = clazz;\n    this.message = message;\n    this.serverName = serverName;\n    this.procName = procName;\n    this.lineNumber = lineNumber;\n  }\n}\n\nexport class LoginAckToken extends Token {\n  declare name: 'LOGINACK';\n  declare handlerName: 'onLoginAck';\n\n  declare interface: string;\n  declare tdsVersion: string;\n  declare progName: string;\n  declare progVersion: { major: number, minor: number, buildNumHi: number, buildNumLow: number };\n\n  constructor({ interface: interfaze, tdsVersion, progName, progVersion }: { interface: LoginAckToken['interface'], tdsVersion: LoginAckToken['tdsVersion'], progName: LoginAckToken['progName'], progVersion: LoginAckToken['progVersion'] }) {\n    super('LOGINACK', 'onLoginAck');\n\n    this.interface = interfaze;\n    this.tdsVersion = tdsVersion;\n    this.progName = progName;\n    this.progVersion = progVersion;\n  }\n}\n\nexport class NBCRowToken extends Token {\n  declare name: 'NBCROW';\n  declare handlerName: 'onRow';\n\n  declare columns: any;\n\n  constructor(columns: any) {\n    super('NBCROW', 'onRow');\n\n    this.columns = columns;\n  }\n}\n\nexport class OrderToken extends Token {\n  declare name: 'ORDER';\n  declare handlerName: 'onOrder';\n\n  declare orderColumns: number[];\n\n  constructor(orderColumns: number[]) {\n    super('ORDER', 'onOrder');\n\n    this.orderColumns = orderColumns;\n  }\n}\n\nexport class ReturnStatusToken extends Token {\n  declare name: 'RETURNSTATUS';\n  declare handlerName: 'onReturnStatus';\n\n  declare value: number;\n\n  constructor(value: number) {\n    super('RETURNSTATUS', 'onReturnStatus');\n\n    this.value = value;\n  }\n}\n\nexport class ReturnValueToken extends Token {\n  declare name: 'RETURNVALUE';\n  declare handlerName: 'onReturnValue';\n\n  declare paramOrdinal: number;\n  declare paramName: string;\n  declare metadata: Metadata;\n  declare value: unknown;\n\n  constructor({ paramOrdinal, paramName, metadata, value }: { paramOrdinal: number, paramName: string, metadata: Metadata, value: unknown }) {\n    super('RETURNVALUE', 'onReturnValue');\n\n    this.paramOrdinal = paramOrdinal;\n    this.paramName = paramName;\n    this.metadata = metadata;\n    this.value = value;\n  }\n}\n\nexport class RowToken extends Token {\n  declare name: 'ROW';\n  declare handlerName: 'onRow';\n\n  declare columns: any;\n\n  constructor(columns: any) {\n    super('ROW', 'onRow');\n\n    this.columns = columns;\n  }\n}\n\nexport class SSPIToken extends Token {\n  declare name: 'SSPICHALLENGE';\n  declare handlerName: 'onSSPI';\n\n  declare ntlmpacket: any;\n  declare ntlmpacketBuffer: Buffer;\n\n  constructor(ntlmpacket: any, ntlmpacketBuffer: Buffer) {\n    super('SSPICHALLENGE', 'onSSPI');\n\n    this.ntlmpacket = ntlmpacket;\n    this.ntlmpacketBuffer = ntlmpacketBuffer;\n  }\n}\n"], "mappings": ";;;;;;AAKO,MAAMA,IAAI,GAAG;EAClBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,IAAI;EACZC,WAAW,EAAE,IAAI;EACjBC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE;AACX,CAAC;AAACC,OAAA,CAAAtB,IAAA,GAAAA,IAAA;AAIK,MAAeuB,KAAK,CAAC;EAI1BC,WAAWA,CAACC,IAAY,EAAEC,WAAwB,EAAE;IAClD,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAChC;AACF;AAACJ,OAAA,CAAAC,KAAA,GAAAA,KAAA;AAEM,MAAMI,gBAAgB,SAASJ,KAAK,CAAC;EAM1CC,WAAWA,CAACI,OAAyB,EAAE;IACrC,KAAK,CAAC,aAAa,EAAE,eAAe,CAAC;IAErC,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAACN,OAAA,CAAAK,gBAAA,GAAAA,gBAAA;AAEM,MAAME,SAAS,SAASN,KAAK,CAAC;EAWnCC,WAAWA,CAAC;IAAEM,IAAI;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,WAAW;IAAEC,QAAQ;IAAEC;EAAqI,CAAC,EAAE;IACtM,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC;IAEvB,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AAACb,OAAA,CAAAO,SAAA,GAAAA,SAAA;AAEM,MAAMO,eAAe,SAASb,KAAK,CAAC;EAWzCC,WAAWA,CAAC;IAAEM,IAAI;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,WAAW;IAAEC,QAAQ;IAAEC;EAAqI,CAAC,EAAE;IACtM,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC;IAEnC,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AAACb,OAAA,CAAAc,eAAA,GAAAA,eAAA;AAEM,MAAMC,aAAa,SAASd,KAAK,CAAC;EAWvCC,WAAWA,CAAC;IAAEM,IAAI;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,WAAW;IAAEC,QAAQ;IAAEC;EAAqI,CAAC,EAAE;IACtM,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC;IAE/B,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AAACb,OAAA,CAAAe,aAAA,GAAAA,aAAA;AAEM,MAAMC,sBAAsB,SAASf,KAAK,CAAC;EAQhDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,kBAAkB,CAAC;IAEtC,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAgB,sBAAA,GAAAA,sBAAA;AAEM,MAAMI,sBAAsB,SAASnB,KAAK,CAAC;EAQhDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,kBAAkB,CAAC;IAEtC,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAoB,sBAAA,GAAAA,sBAAA;AAEM,MAAMC,qBAAqB,SAASpB,KAAK,CAAC;EAQ/CC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,iBAAiB,CAAC;IAErC,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAqB,qBAAA,GAAAA,qBAAA;AAEM,MAAMC,wBAAwB,SAASrB,KAAK,CAAC;EAQlDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,oBAAoB,CAAC;IAExC,IAAI,CAACC,IAAI,GAAG,aAAa;IACzB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAsB,wBAAA,GAAAA,wBAAA;AAEM,MAAMC,8BAA8B,SAAStB,KAAK,CAAC;EAQxDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,oBAAoB,CAAC;IAExC,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAuB,8BAAA,GAAAA,8BAAA;AAEM,MAAMC,+BAA+B,SAASvB,KAAK,CAAC;EAQzDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,qBAAqB,CAAC;IAEzC,IAAI,CAACC,IAAI,GAAG,YAAY;IACxB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAwB,+BAAA,GAAAA,+BAAA;AAEM,MAAMC,iCAAiC,SAASxB,KAAK,CAAC;EAQ3DC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC;IAE3C,IAAI,CAACC,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAyB,iCAAA,GAAAA,iCAAA;AAEM,MAAMC,sCAAsC,SAASzB,KAAK,CAAC;EAQhEC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,4BAA4B,CAAC;IAEhD,IAAI,CAACC,IAAI,GAAG,4BAA4B;IACxC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAA0B,sCAAA,GAAAA,sCAAA;AAEM,MAAMC,6BAA6B,SAAS1B,KAAK,CAAC;EAQvDC,WAAWA,CAACe,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,KAAK,CAAC,WAAW,EAAE,mBAAmB,CAAC;IAEvC,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAA2B,6BAAA,GAAAA,6BAAA;AAeM,MAAMC,oBAAoB,SAAS3B,KAAK,CAAC;EAQ9CC,WAAWA,CAACe,QAA+B,EAAEC,QAA+B,EAAE;IAC5E,KAAK,CAAC,WAAW,EAAE,sBAAsB,CAAC;IAE1C,IAAI,CAACC,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAA4B,oBAAA,GAAAA,oBAAA;AAEM,MAAMC,qBAAqB,SAAS5B,KAAK,CAAC;EAQ/CC,WAAWA,CAACe,QAA4D,EAAEC,QAAgB,EAAE;IAC1F,KAAK,CAAC,WAAW,EAAE,iBAAiB,CAAC;IAErC,IAAI,CAACC,IAAI,GAAG,gBAAgB;IAC5B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAA6B,qBAAA,GAAAA,qBAAA;AAEM,MAAMC,kBAAkB,SAAS7B,KAAK,CAAC;EAM5C;AACF;AACA;;EAGEC,WAAWA,CAAC6B,OAA2B,EAAEC,WAAgC,EAAE;IACzE,KAAK,CAAC,eAAe,EAAE,iBAAiB,CAAC;IAEzC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAChC;AACF;AAAChC,OAAA,CAAA8B,kBAAA,GAAAA,kBAAA;AAEM,MAAMG,gBAAgB,SAAShC,KAAK,CAAC;EAO1CC,WAAWA,CAACgC,GAAuB,EAAEC,MAA0B,EAAE;IAC/D,KAAK,CAAC,aAAa,EAAE,eAAe,CAAC;IAErC,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AAACnC,OAAA,CAAAiC,gBAAA,GAAAA,gBAAA;AAEM,MAAMG,gBAAgB,SAASnC,KAAK,CAAC;EAY1CC,WAAWA,CAAC;IAAEmC,MAAM;IAAEC,KAAK;IAAEC,KAAK,EAAEC,KAAK;IAAEC,OAAO;IAAEC,UAAU;IAAEC,QAAQ;IAAEC;EAAwI,CAAC,EAAE;IACnN,KAAK,CAAC,MAAM,EAAE,eAAe,CAAC;IAE9B,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGC,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;AACF;AAAC5C,OAAA,CAAAoC,gBAAA,GAAAA,gBAAA;AAEM,MAAMS,iBAAiB,SAAS5C,KAAK,CAAC;EAY3CC,WAAWA,CAAC;IAAEmC,MAAM;IAAEC,KAAK;IAAEC,KAAK,EAAEC,KAAK;IAAEC,OAAO;IAAEC,UAAU;IAAEC,QAAQ;IAAEC;EAAwI,CAAC,EAAE;IACnN,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC;IAEhC,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGC,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;AACF;AAAC5C,OAAA,CAAA6C,iBAAA,GAAAA,iBAAA;AAEM,MAAMC,aAAa,SAAS7C,KAAK,CAAC;EASvCC,WAAWA,CAAC;IAAE6C,SAAS,EAAEC,SAAS;IAAEC,UAAU;IAAEC,QAAQ;IAAEC;EAAgL,CAAC,EAAE;IAC3O,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC;IAE/B,IAAI,CAACJ,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAChC;AACF;AAACnD,OAAA,CAAA8C,aAAA,GAAAA,aAAA;AAEM,MAAMM,WAAW,SAASnD,KAAK,CAAC;EAMrCC,WAAWA,CAACI,OAAY,EAAE;IACxB,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC;IAExB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAACN,OAAA,CAAAoD,WAAA,GAAAA,WAAA;AAEM,MAAMC,UAAU,SAASpD,KAAK,CAAC;EAMpCC,WAAWA,CAACoD,YAAsB,EAAE;IAClC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC;IAEzB,IAAI,CAACA,YAAY,GAAGA,YAAY;EAClC;AACF;AAACtD,OAAA,CAAAqD,UAAA,GAAAA,UAAA;AAEM,MAAME,iBAAiB,SAAStD,KAAK,CAAC;EAM3CC,WAAWA,CAACsD,KAAa,EAAE;IACzB,KAAK,CAAC,cAAc,EAAE,gBAAgB,CAAC;IAEvC,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;AACF;AAACxD,OAAA,CAAAuD,iBAAA,GAAAA,iBAAA;AAEM,MAAME,gBAAgB,SAASxD,KAAK,CAAC;EAS1CC,WAAWA,CAAC;IAAEwD,YAAY;IAAEC,SAAS;IAAEC,QAAQ;IAAEJ;EAAuF,CAAC,EAAE;IACzI,KAAK,CAAC,aAAa,EAAE,eAAe,CAAC;IAErC,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACJ,KAAK,GAAGA,KAAK;EACpB;AACF;AAACxD,OAAA,CAAAyD,gBAAA,GAAAA,gBAAA;AAEM,MAAMI,QAAQ,SAAS5D,KAAK,CAAC;EAMlCC,WAAWA,CAACI,OAAY,EAAE;IACxB,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;IAErB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAACN,OAAA,CAAA6D,QAAA,GAAAA,QAAA;AAEM,MAAMC,SAAS,SAAS7D,KAAK,CAAC;EAOnCC,WAAWA,CAAC6D,UAAe,EAAEC,gBAAwB,EAAE;IACrD,KAAK,CAAC,eAAe,EAAE,QAAQ,CAAC;IAEhC,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;AACF;AAAChE,OAAA,CAAA8D,SAAA,GAAAA,SAAA"}