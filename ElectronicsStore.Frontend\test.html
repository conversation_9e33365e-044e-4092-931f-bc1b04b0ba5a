<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976d2;
            text-align: center;
            margin-bottom: 30px;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .status {
            font-weight: bold;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 متجر الإلكترونيات - اختبار النظام</h1>
        
        <div class="success">
            <h2>✅ HTML يعمل بشكل صحيح!</h2>
            <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن:</p>
            <ul>
                <li>✅ الخادم المحلي يعمل</li>
                <li>✅ المتصفح يعرض المحتوى</li>
                <li>✅ CSS يعمل بشكل صحيح</li>
                <li>✅ الخطوط العربية تظهر</li>
            </ul>
        </div>

        <div class="error">
            <h3>❌ المشكلة المحتملة:</h3>
            <p>المشكلة في <strong>TypeScript/React</strong> وليس في الخادم</p>
            <ul>
                <li>مشكلة في تعريفات الأنواع (@types/react)</li>
                <li>مشكلة في إعدادات TypeScript</li>
                <li>مشكلة في تشغيل Vite</li>
            </ul>
        </div>

        <div class="status">
            <h3>🔧 الحلول المقترحة:</h3>
            <ol>
                <li>إعادة تثبيت node_modules</li>
                <li>تبسيط tsconfig.json</li>
                <li>استخدام JavaScript بدلاً من TypeScript مؤقتاً</li>
                <li>إنشاء مشروع جديد من الصفر</li>
            </ol>
        </div>
    </div>
</body>
</html>
