// Dashboard Statistics Types
export interface DashboardStats {
  totalSales: number;
  totalProducts: number;
  lowStockProducts: number;
  totalCustomers: number;
  todaySales: number;
  monthlyRevenue: number;
  totalProfit: number;
  totalExpenses: number;
}

// Sales Data Types
export interface SalesData {
  date: string;
  sales: number;
  profit: number;
  orders: number;
}

// Product Types (Updated to match C# API)
export interface Product {
  id: number;
  name: string;
  barcode: string;
  description?: string;
  categoryId: number;
  categoryName: string;
  supplierId?: number;
  supplierName?: string;
  defaultCostPrice: number;
  defaultSellingPrice: number;
  minSellingPrice: number;
  currentStock: number;
  inventoryValue: number;
  createdAt: string;
  updatedAt?: string;
}

// Product Form Data
export interface ProductFormData {
  name: string;
  barcode: string;
  description?: string;
  categoryId: number;
  supplierId?: number;
  defaultCostPrice: number;
  defaultSellingPrice: number;
  minSellingPrice: number;
}

// Category Types
export interface Category {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  productsCount: number;
}

// Top Products Types
export interface TopProduct {
  id: number;
  name: string;
  totalSold: number;
  revenue: number;
  profit: number;
}

// Recent Sales Types
export interface RecentSale {
  id: number;
  invoiceNumber: string;
  customerName: string;
  totalAmount: number;
  paymentMethod: string;
  createdAt: string;
  itemsCount: number;
}

// Low Stock Alert Types
export interface LowStockAlert {
  id: number;
  productName: string;
  currentStock: number;
  minimumStock: number;
  categoryName: string;
  urgencyLevel: 'critical' | 'warning' | 'low';
}

// Monthly Revenue Types
export interface MonthlyRevenue {
  month: string;
  revenue: number;
  profit: number;
  expenses: number;
}

// User Types
export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  role: string;
  isActive: boolean;
  lastLogin: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

// Chart Data Types
export interface ChartDataPoint {
  name: string;
  value: number;
  color?: string;
}

// Dashboard Card Props
export interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  subtitle?: string;
}

// Navigation Types
export interface NavItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  path: string;
  children?: NavItem[];
}

// Theme Types
export interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
}
