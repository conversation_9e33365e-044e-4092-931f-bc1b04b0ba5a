"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getPickersArrowSwitcherUtilityClass = getPickersArrowSwitcherUtilityClass;
exports.pickersArrowSwitcherClasses = void 0;
var _utils = require("@mui/utils");
function getPickersArrowSwitcherUtilityClass(slot) {
  return (0, _utils.unstable_generateUtilityClass)('MuiPickersArrowSwitcher', slot);
}
const pickersArrowSwitcherClasses = exports.pickersArrowSwitcherClasses = (0, _utils.unstable_generateUtilityClasses)('MuiPickersArrowSwitcher', ['root', 'spacer', 'button']);