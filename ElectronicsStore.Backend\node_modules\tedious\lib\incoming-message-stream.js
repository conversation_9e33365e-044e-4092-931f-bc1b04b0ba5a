"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _bl = _interopRequireDefault(require("bl"));
var _stream = require("stream");
var _message = _interopRequireDefault(require("./message"));
var _packet = require("./packet");
var _errors = require("./errors");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
  IncomingMessageStream
  Transform received TDS data into individual IncomingMessage streams.
*/
class IncomingMessageStream extends _stream.Transform {
  constructor(debug) {
    super({
      readableObjectMode: true
    });
    this.debug = debug;
    this.currentMessage = undefined;
    this.bl = new _bl.default();
  }
  pause() {
    super.pause();
    if (this.currentMessage) {
      this.currentMessage.pause();
    }
    return this;
  }
  resume() {
    super.resume();
    if (this.currentMessage) {
      this.currentMessage.resume();
    }
    return this;
  }
  processBufferedData(callback) {
    // The packet header is always 8 bytes of length.
    while (this.bl.length >= _packet.HEADER_LENGTH) {
      // Get the full packet length
      const length = this.bl.readUInt16BE(2);
      if (length < _packet.HEADER_LENGTH) {
        return callback(new _errors.ConnectionError('Unable to process incoming packet'));
      }
      if (this.bl.length >= length) {
        const data = this.bl.slice(0, length);
        this.bl.consume(length);

        // TODO: Get rid of creating `Packet` instances here.
        const packet = new _packet.Packet(data);
        this.debug.packet('Received', packet);
        this.debug.data(packet);
        let message = this.currentMessage;
        if (message === undefined) {
          this.currentMessage = message = new _message.default({
            type: packet.type(),
            resetConnection: false
          });
          this.push(message);
        }
        if (packet.isLast()) {
          // Wait until the current message was fully processed before we
          // continue processing any remaining messages.
          message.once('end', () => {
            this.currentMessage = undefined;
            this.processBufferedData(callback);
          });
          message.end(packet.data());
          return;
        } else if (!message.write(packet.data())) {
          // If too much data is buffering up in the
          // current message, wait for it to drain.
          message.once('drain', () => {
            this.processBufferedData(callback);
          });
          return;
        }
      } else {
        break;
      }
    }

    // Not enough data to read the next packet. Stop here and wait for
    // the next call to `_transform`.
    callback();
  }
  _transform(chunk, _encoding, callback) {
    this.bl.append(chunk);
    this.processBufferedData(callback);
  }
}
var _default = IncomingMessageStream;
exports.default = _default;
module.exports = IncomingMessageStream;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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