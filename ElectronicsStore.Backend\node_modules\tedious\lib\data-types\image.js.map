{"version": 3, "file": "image.js", "names": ["NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Image", "id", "type", "name", "hasTableName", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "parameter", "value", "length", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeInt32LE", "generateParameterLength", "options", "generateParameterData", "validate", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/image.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst Image: DataType = {\n  id: 0x22,\n  type: 'IMAGE',\n  name: 'Image',\n  hasTableName: true,\n\n  declaration: function() {\n    return 'image';\n  },\n\n  resolveLength: function(parameter) {\n    if (parameter.value != null) {\n      const value = parameter.value as any; // TODO: Temporary solution. Replace 'any' more with specific type;\n      return value.length;\n    } else {\n      return -1;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    const buffer = Buffer.alloc(5);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeInt32LE(parameter.length!, 1);\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeInt32LE(parameter.value.length!, 0);\n    return buffer;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield parameter.value;\n  },\n\n  validate: function(value): null | Buffer {\n    if (value == null) {\n      return null;\n    }\n    if (!Buffer.isBuffer(value)) {\n      throw new TypeError('Invalid buffer.');\n    }\n    return value;\n  }\n};\n\nexport default Image;\nmodule.exports = Image;\n"], "mappings": ";;;;;;AAEA,MAAMA,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAEzD,MAAMC,KAAe,GAAG;EACtBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,YAAY,EAAE,IAAI;EAElBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,OAAO;EAChB,CAAC;EAEDC,aAAa,EAAE,SAAAA,CAASC,SAAS,EAAE;IACjC,IAAIA,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,MAAMA,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;MACtC,OAAOA,KAAK,CAACC,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAEDC,gBAAgBA,CAACH,SAAS,EAAE;IAC1B,MAAMI,MAAM,GAAGb,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACZ,EAAE,EAAE,CAAC,CAAC;IAC7BU,MAAM,CAACG,YAAY,CAACP,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IACzC,OAAOE,MAAM;EACf,CAAC;EAEDI,uBAAuBA,CAACR,SAAS,EAAES,OAAO,EAAE;IAC1C,IAAIT,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,MAAMc,MAAM,GAAGb,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACG,YAAY,CAACP,SAAS,CAACC,KAAK,CAACC,MAAM,EAAG,CAAC,CAAC;IAC/C,OAAOE,MAAM;EACf,CAAC;EAED,CAAEM,qBAAqBA,CAACV,SAAS,EAAES,OAAO,EAAE;IAC1C,IAAIT,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMD,SAAS,CAACC,KAAK;EACvB,CAAC;EAEDU,QAAQ,EAAE,SAAAA,CAASV,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAI,CAACV,MAAM,CAACqB,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIY,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOZ,KAAK;EACd;AACF,CAAC;AAAC,IAAAa,QAAA,GAEarB,KAAK;AAAAsB,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACpBG,MAAM,CAACF,OAAO,GAAGtB,KAAK"}