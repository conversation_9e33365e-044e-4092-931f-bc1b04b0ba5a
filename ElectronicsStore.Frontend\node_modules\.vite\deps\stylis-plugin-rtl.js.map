{"version": 3, "sources": ["../../cssjanus/src/cssjanus.js", "../../stylis-plugin-rtl/src/stylis-rtl.ts", "../../stylis/src/Enum.js", "../../stylis/src/Utility.js", "../../stylis/src/Tokenizer.js", "../../stylis/src/Parser.js", "../../stylis/src/Serializer.js"], "sourcesContent": ["/*!\n * CSSJanus. https://www.mediawiki.org/wiki/CSSJanus\n *\n * Copyright 2014 <PERSON>\n * Copyright 2010 <PERSON><PERSON>\n * Copyright 2008 Google Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar cssjanus;\n\n/**\n * Create a tokenizer object.\n *\n * This utility class is used by CSSJanus to protect strings by replacing them temporarily with\n * tokens and later transforming them back.\n *\n * @class\n * @constructor\n * @param {RegExp} regex Regular expression whose matches to replace by a token\n * @param {string} token Placeholder text\n */\nfunction Tokenizer( regex, token ) {\n\n\tvar matches = [],\n\t\tindex = 0;\n\n\t/**\n\t * Add a match.\n\t *\n\t * @private\n\t * @param {string} match Matched string\n\t * @return {string} Token to leave in the matched string's place\n\t */\n\tfunction tokenizeCallback( match ) {\n\t\tmatches.push( match );\n\t\treturn token;\n\t}\n\n\t/**\n\t * Get a match.\n\t *\n\t * @private\n\t * @return {string} Original matched string to restore\n\t */\n\tfunction detokenizeCallback() {\n\t\treturn matches[ index++ ];\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Replace matching strings with tokens.\n\t\t *\n\t\t * @param {string} str String to tokenize\n\t\t * @return {string} Tokenized string\n\t\t */\n\t\ttokenize: function ( str ) {\n\t\t\treturn str.replace( regex, tokenizeCallback );\n\t\t},\n\n\t\t/**\n\t\t * Restores tokens to their original values.\n\t\t *\n\t\t * @param {string} str String previously run through tokenize()\n\t\t * @return {string} Original string\n\t\t */\n\t\tdetokenize: function ( str ) {\n\t\t\treturn str.replace( new RegExp( '(' + token + ')', 'g' ), detokenizeCallback );\n\t\t}\n\t};\n}\n\n/**\n * Create a CSSJanus object.\n *\n * CSSJanus transforms CSS rules with horizontal relevance so that a left-to-right stylesheet can\n * become a right-to-left stylesheet automatically. Processing can be bypassed for an entire rule\n * or a single property by adding a / * @noflip * / comment above the rule or property.\n *\n * @class\n * @constructor\n */\nfunction CSSJanus() {\n\n\tvar\n\t\t// Tokens\n\t\ttemporaryToken = '`TMP`',\n\t\ttemporaryLtrToken = '`TMPLTR`',\n\t\ttemporaryRtlToken = '`TMPRTL`',\n\t\tnoFlipSingleToken = '`NOFLIP_SINGLE`',\n\t\tnoFlipClassToken = '`NOFLIP_CLASS`',\n\t\tcommentToken = '`COMMENT`',\n\t\t// Patterns\n\t\tnonAsciiPattern = '[^\\\\u0020-\\\\u007e]',\n\t\tunicodePattern = '(?:(?:\\\\\\\\[0-9a-f]{1,6})(?:\\\\r\\\\n|\\\\s)?)',\n\t\tnumPattern = '(?:[0-9]*\\\\.[0-9]+|[0-9]+)',\n\t\tunitPattern = '(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)',\n\t\tdirectionPattern = 'direction\\\\s*:\\\\s*',\n\t\turlSpecialCharsPattern = '[!#$%&*-~]',\n\t\tvalidAfterUriCharsPattern = '[\\'\"]?\\\\s*',\n\t\tnonLetterPattern = '(^|[^a-zA-Z])',\n\t\tcharsWithinSelectorPattern = '[^\\\\}]*?',\n\t\tnoFlipPattern = '\\\\/\\\\*\\\\!?\\\\s*@noflip\\\\s*\\\\*\\\\/',\n\t\tcommentPattern = '\\\\/\\\\*[^*]*\\\\*+([^\\\\/*][^*]*\\\\*+)*\\\\/',\n\t\tescapePattern = '(?:' + unicodePattern + '|\\\\\\\\[^\\\\r\\\\n\\\\f0-9a-f])',\n\t\tnmstartPattern = '(?:[_a-z]|' + nonAsciiPattern + '|' + escapePattern + ')',\n\t\tnmcharPattern = '(?:[_a-z0-9-]|' + nonAsciiPattern + '|' + escapePattern + ')',\n\t\tidentPattern = '-?' + nmstartPattern + nmcharPattern + '*',\n\t\tquantPattern = numPattern + '(?:\\\\s*' + unitPattern + '|' + identPattern + ')?',\n\t\tsignedQuantPattern = '((?:-?' + quantPattern + ')|(?:inherit|auto))',\n\t\tsignedQuantSimplePattern = '(?:-?' + numPattern + '(?:\\\\s*' + unitPattern + ')?)',\n\t\tmathOperatorsPattern = '(?:\\\\+|\\\\-|\\\\*|\\\\/)',\n\t\tallowedCharsPattern = '(?:\\\\(|\\\\)|\\\\t| )',\n\t\tcalcEquationPattern = '(?:' + allowedCharsPattern + '|' + signedQuantSimplePattern + '|' + mathOperatorsPattern + '){3,}',\n\t\tcalcPattern = '(?:calc\\\\((?:' + calcEquationPattern + ')\\\\))',\n\t\tsignedQuantCalcPattern = '((?:-?' + quantPattern + ')|(?:inherit|auto)|' + calcPattern + ')',\n\t\tfourNotationQuantPropsPattern = '((?:margin|padding|border-width)\\\\s*:\\\\s*)',\n\t\tfourNotationColorPropsPattern = '((?:-color|border-style)\\\\s*:\\\\s*)',\n\t\tcolorPattern = '(#?' + nmcharPattern + '+|(?:rgba?|hsla?)\\\\([ \\\\d.,%-]+\\\\))',\n\t\t// The use of a lazy match (\"*?\") may cause a backtrack limit to be exceeded before finding\n\t\t// the intended match. This affects 'urlCharsPattern' and 'lookAheadNotOpenBracePattern'.\n\t\t// We have not yet found this problem on Node.js, but we have on PHP 7, where it was\n\t\t// mitigated by using a possessive quantifier (\"*+\"), which are not supported in JS.\n\t\t// See <https://phabricator.wikimedia.org/T215746#4944830>.\n\t\turlCharsPattern = '(?:' + urlSpecialCharsPattern + '|' + nonAsciiPattern + '|' + escapePattern + ')*?',\n\t\tlookAheadNotLetterPattern = '(?![a-zA-Z])',\n\t\tlookAheadNotOpenBracePattern = '(?!(' + nmcharPattern + '|\\\\r?\\\\n|\\\\s|#|\\\\:|\\\\.|\\\\,|\\\\+|>|~|\\\\(|\\\\)|\\\\[|\\\\]|=|\\\\*=|~=|\\\\^=|\\'[^\\']*\\'|\"[^\"]*\"|' + commentToken + ')*?{)',\n\t\tlookAheadNotClosingParenPattern = '(?!' + urlCharsPattern + validAfterUriCharsPattern + '\\\\))',\n\t\tlookAheadForClosingParenPattern = '(?=' + urlCharsPattern + validAfterUriCharsPattern + '\\\\))',\n\t\tsuffixPattern = '(\\\\s*(?:!important\\\\s*)?[;}])',\n\t\t// Regular expressions\n\t\ttemporaryTokenRegExp = /`TMP`/g,\n\t\ttemporaryLtrTokenRegExp = /`TMPLTR`/g,\n\t\ttemporaryRtlTokenRegExp = /`TMPRTL`/g,\n\t\tcommentRegExp = new RegExp( commentPattern, 'gi' ),\n\t\tnoFlipSingleRegExp = new RegExp( '(' + noFlipPattern + lookAheadNotOpenBracePattern + '[^;}]+;?)', 'gi' ),\n\t\tnoFlipClassRegExp = new RegExp( '(' + noFlipPattern + charsWithinSelectorPattern + '})', 'gi' ),\n\t\tdirectionLtrRegExp = new RegExp( '(' + directionPattern + ')ltr', 'gi' ),\n\t\tdirectionRtlRegExp = new RegExp( '(' + directionPattern + ')rtl', 'gi' ),\n\t\tleftRegExp = new RegExp( nonLetterPattern + '(left)' + lookAheadNotLetterPattern + lookAheadNotClosingParenPattern + lookAheadNotOpenBracePattern, 'gi' ),\n\t\trightRegExp = new RegExp( nonLetterPattern + '(right)' + lookAheadNotLetterPattern + lookAheadNotClosingParenPattern + lookAheadNotOpenBracePattern, 'gi' ),\n\t\tleftInUrlRegExp = new RegExp( nonLetterPattern + '(left)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\trightInUrlRegExp = new RegExp( nonLetterPattern + '(right)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\tltrDirSelector = /(:dir\\( *)ltr( *\\))/g,\n\t\trtlDirSelector = /(:dir\\( *)rtl( *\\))/g,\n\t\tltrInUrlRegExp = new RegExp( nonLetterPattern + '(ltr)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\trtlInUrlRegExp = new RegExp( nonLetterPattern + '(rtl)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\tcursorEastRegExp = new RegExp( nonLetterPattern + '([ns]?)e-resize', 'gi' ),\n\t\tcursorWestRegExp = new RegExp( nonLetterPattern + '([ns]?)w-resize', 'gi' ),\n\t\tfourNotationQuantRegExp = new RegExp( fourNotationQuantPropsPattern + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + suffixPattern, 'gi' ),\n\t\tfourNotationColorRegExp = new RegExp( fourNotationColorPropsPattern + colorPattern + '(\\\\s+)' + colorPattern + '(\\\\s+)' + colorPattern + '(\\\\s+)' + colorPattern + suffixPattern, 'gi' ),\n\t\tbgHorizontalPercentageRegExp = new RegExp( '(background(?:-position)?\\\\s*:\\\\s*(?:[^:;}\\\\s]+\\\\s+)*?)(' + quantPattern + ')', 'gi' ),\n\t\tbgHorizontalPercentageXRegExp = new RegExp( '(background-position-x\\\\s*:\\\\s*)(-?' + numPattern + '%)', 'gi' ),\n\t\t// border-radius: <length or percentage>{1,4} [optional: / <length or percentage>{1,4} ]\n\t\tborderRadiusRegExp = new RegExp( '(border-radius\\\\s*:\\\\s*)' + signedQuantPattern + '(?:(?:\\\\s+' + signedQuantPattern + ')(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?)?' +\n\t\t\t'(?:(?:(?:\\\\s*\\\\/\\\\s*)' + signedQuantPattern + ')(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?)?' + suffixPattern, 'gi' ),\n\t\tboxShadowRegExp = new RegExp( '(box-shadow\\\\s*:\\\\s*(?:inset\\\\s*)?)' + signedQuantPattern, 'gi' ),\n\t\ttextShadow1RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + signedQuantPattern + '(\\\\s*)' + colorPattern, 'gi' ),\n\t\ttextShadow2RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + colorPattern + '(\\\\s*)' + signedQuantPattern, 'gi' ),\n\t\ttextShadow3RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + signedQuantPattern, 'gi' ),\n\t\ttranslateXRegExp = new RegExp( '(transform\\\\s*:[^;}]*)(translateX\\\\s*\\\\(\\\\s*)' + signedQuantPattern + '(\\\\s*\\\\))', 'gi' ),\n\t\ttranslateRegExp = new RegExp( '(transform\\\\s*:[^;}]*)(translate\\\\s*\\\\(\\\\s*)' + signedQuantPattern + '((?:\\\\s*,\\\\s*' + signedQuantPattern + '){0,2}\\\\s*\\\\))', 'gi' );\n\n\t/**\n\t * Invert the horizontal value of a background position property.\n\t *\n\t * @private\n\t * @param {string} match Matched property\n\t * @param {string} pre Text before value\n\t * @param {string} value Horizontal value\n\t * @return {string} Inverted property\n\t */\n\tfunction calculateNewBackgroundPosition( match, pre, value ) {\n\t\tvar idx, len;\n\t\tif ( value.slice( -1 ) === '%' ) {\n\t\t\tidx = value.indexOf( '.' );\n\t\t\tif ( idx !== -1 ) {\n\t\t\t\t// Two off, one for the \"%\" at the end, one for the dot itself\n\t\t\t\tlen = value.length - idx - 2;\n\t\t\t\tvalue = 100 - parseFloat( value );\n\t\t\t\tvalue = value.toFixed( len ) + '%';\n\t\t\t} else {\n\t\t\t\tvalue = 100 - parseFloat( value ) + '%';\n\t\t\t}\n\t\t}\n\t\treturn pre + value;\n\t}\n\n\t/**\n\t * Invert a set of border radius values.\n\t *\n\t * @private\n\t * @param {Array} values Matched values\n\t * @return {string} Inverted values\n\t */\n\tfunction flipBorderRadiusValues( values ) {\n\t\tswitch ( values.length ) {\n\t\t\tcase 4:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ], values[ 3 ], values[ 2 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 3:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ], values[ 1 ], values[ 2 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 2:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 1:\n\t\t\t\tvalues = [ values[ 0 ] ];\n\t\t\t\tbreak;\n\t\t}\n\n\t\treturn values.join( ' ' );\n\t}\n\n\t/**\n\t * Invert a set of border radius values.\n\t *\n\t * @private\n\t * @param {string} match Matched property\n\t * @param {string} pre Text before value\n\t * @param {string} [firstGroup1]\n\t * @param {string} [firstGroup2]\n\t * @param {string} [firstGroup3]\n\t * @param {string} [firstGroup4]\n\t * @param {string} [secondGroup1]\n\t * @param {string} [secondGroup2]\n\t * @param {string} [secondGroup3]\n\t * @param {string} [secondGroup4]\n\t * @param {string} [post] Text after value\n\t * @return {string} Inverted property\n\t */\n\tfunction calculateNewBorderRadius( match, pre ) {\n\t\tvar values,\n\t\t\targs = [].slice.call( arguments ),\n\t\t\tfirstGroup = args.slice( 2, 6 ).filter( function ( val ) {\n\t\t\t\treturn val;\n\t\t\t} ),\n\t\t\tsecondGroup = args.slice( 6, 10 ).filter( function ( val ) {\n\t\t\t\treturn val;\n\t\t\t} ),\n\t\t\tpost = args[ 10 ] || '';\n\n\t\tif ( secondGroup.length ) {\n\t\t\tvalues = flipBorderRadiusValues( firstGroup ) + ' / ' + flipBorderRadiusValues( secondGroup );\n\t\t} else {\n\t\t\tvalues = flipBorderRadiusValues( firstGroup );\n\t\t}\n\n\t\treturn pre + values + post;\n\t}\n\n\t/**\n\t * Flip the sign of a CSS value, possibly with a unit.\n\t *\n\t * We can't just negate the value with unary minus due to the units.\n\t *\n\t * @private\n\t * @param {string} value\n\t * @return {string}\n\t */\n\tfunction flipSign( value ) {\n\t\tif ( parseFloat( value ) === 0 ) {\n\t\t\t// Don't mangle zeroes\n\t\t\treturn value;\n\t\t}\n\n\t\tif ( value[ 0 ] === '-' ) {\n\t\t\treturn value.slice( 1 );\n\t\t}\n\n\t\treturn '-' + value;\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} offset\n\t * @return {string}\n\t */\n\tfunction calculateNewShadow( match, property, offset ) {\n\t\treturn property + flipSign( offset );\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} prefix\n\t * @param {string} offset\n\t * @param {string} suffix\n\t * @return {string}\n\t */\n\tfunction calculateNewTranslate( match, property, prefix, offset, suffix ) {\n\t\treturn property + prefix + flipSign( offset ) + suffix;\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} color\n\t * @param {string} space\n\t * @param {string} offset\n\t * @return {string}\n\t */\n\tfunction calculateNewFourTextShadow( match, property, color, space, offset ) {\n\t\treturn property + color + space + flipSign( offset );\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Transform a left-to-right stylesheet to right-to-left.\n\t\t *\n\t\t * @param {string} css Stylesheet to transform\n\t\t * @param {Object} options Options\n\t\t * @param {boolean} [options.transformDirInUrl=false] Transform directions in URLs\n\t\t * (e.g. 'ltr', 'rtl')\n\t\t * @param {boolean} [options.transformEdgeInUrl=false] Transform edges in URLs\n\t\t * (e.g. 'left', 'right')\n\t\t * @return {string} Transformed stylesheet\n\t\t */\n\t\t'transform': function ( css, options ) { // eslint-disable-line quote-props\n\t\t\t// Use single quotes in this object literal key for closure compiler.\n\t\t\t// Tokenizers\n\t\t\tvar noFlipSingleTokenizer = new Tokenizer( noFlipSingleRegExp, noFlipSingleToken ),\n\t\t\t\tnoFlipClassTokenizer = new Tokenizer( noFlipClassRegExp, noFlipClassToken ),\n\t\t\t\tcommentTokenizer = new Tokenizer( commentRegExp, commentToken );\n\n\t\t\t// Tokenize\n\t\t\tcss = commentTokenizer.tokenize(\n\t\t\t\tnoFlipClassTokenizer.tokenize(\n\t\t\t\t\tnoFlipSingleTokenizer.tokenize(\n\t\t\t\t\t\t// We wrap tokens in ` , not ~ like the original implementation does.\n\t\t\t\t\t\t// This was done because ` is not a legal character in CSS and can only\n\t\t\t\t\t\t// occur in URLs, where we escape it to %60 before inserting our tokens.\n\t\t\t\t\t\tcss.replace( '`', '%60' )\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t);\n\n\t\t\t// Transform URLs\n\t\t\tif ( options.transformDirInUrl ) {\n\t\t\t\t// Replace 'ltr' with 'rtl' and vice versa in background URLs\n\t\t\t\tcss = css\n\t\t\t\t\t.replace( ltrDirSelector, '$1' + temporaryLtrToken + '$2' )\n\t\t\t\t\t.replace( rtlDirSelector, '$1' + temporaryRtlToken + '$2' )\n\t\t\t\t\t.replace( ltrInUrlRegExp, '$1' + temporaryToken )\n\t\t\t\t\t.replace( rtlInUrlRegExp, '$1ltr' )\n\t\t\t\t\t.replace( temporaryTokenRegExp, 'rtl' )\n\t\t\t\t\t.replace( temporaryLtrTokenRegExp, 'ltr' )\n\t\t\t\t\t.replace( temporaryRtlTokenRegExp, 'rtl' );\n\t\t\t}\n\t\t\tif ( options.transformEdgeInUrl ) {\n\t\t\t\t// Replace 'left' with 'right' and vice versa in background URLs\n\t\t\t\tcss = css\n\t\t\t\t\t.replace( leftInUrlRegExp, '$1' + temporaryToken )\n\t\t\t\t\t.replace( rightInUrlRegExp, '$1left' )\n\t\t\t\t\t.replace( temporaryTokenRegExp, 'right' );\n\t\t\t}\n\n\t\t\t// Transform rules\n\t\t\tcss = css\n\t\t\t\t// Replace direction: ltr; with direction: rtl; and vice versa.\n\t\t\t\t.replace( directionLtrRegExp, '$1' + temporaryToken )\n\t\t\t\t.replace( directionRtlRegExp, '$1ltr' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'rtl' )\n\t\t\t\t// Flip rules like left: , padding-right: , etc.\n\t\t\t\t.replace( leftRegExp, '$1' + temporaryToken )\n\t\t\t\t.replace( rightRegExp, '$1left' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'right' )\n\t\t\t\t// Flip East and West in rules like cursor: nw-resize;\n\t\t\t\t.replace( cursorEastRegExp, '$1$2' + temporaryToken )\n\t\t\t\t.replace( cursorWestRegExp, '$1$2e-resize' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'w-resize' )\n\t\t\t\t// Border radius\n\t\t\t\t.replace( borderRadiusRegExp, calculateNewBorderRadius )\n\t\t\t\t// Shadows\n\t\t\t\t.replace( boxShadowRegExp, calculateNewShadow )\n\t\t\t\t.replace( textShadow1RegExp, calculateNewFourTextShadow )\n\t\t\t\t.replace( textShadow2RegExp, calculateNewFourTextShadow )\n\t\t\t\t.replace( textShadow3RegExp, calculateNewShadow )\n\t\t\t\t// Translate\n\t\t\t\t.replace( translateXRegExp, calculateNewTranslate )\n\t\t\t\t.replace( translateRegExp, calculateNewTranslate )\n\t\t\t\t// Swap the second and fourth parts in four-part notation rules\n\t\t\t\t// like padding: 1px 2px 3px 4px;\n\t\t\t\t.replace( fourNotationQuantRegExp, '$1$2$3$8$5$6$7$4$9' )\n\t\t\t\t.replace( fourNotationColorRegExp, '$1$2$3$8$5$6$7$4$9' )\n\t\t\t\t// Flip horizontal background percentages\n\t\t\t\t.replace( bgHorizontalPercentageRegExp, calculateNewBackgroundPosition )\n\t\t\t\t.replace( bgHorizontalPercentageXRegExp, calculateNewBackgroundPosition );\n\n\t\t\t// Detokenize\n\t\t\tcss = noFlipSingleTokenizer.detokenize(\n\t\t\t\tnoFlipClassTokenizer.detokenize(\n\t\t\t\t\tcommentTokenizer.detokenize( css )\n\t\t\t\t)\n\t\t\t);\n\n\t\t\treturn css;\n\t\t}\n\t};\n}\n\n/* Initialization */\n\ncssjanus = new CSSJanus();\n\n/* Exports */\n\nif ( typeof module !== 'undefined' && module.exports ) {\n\t/**\n\t * Transform a left-to-right stylesheet to right-to-left.\n\t *\n\t * This function is a static wrapper around the transform method of an instance of CSSJanus.\n\t *\n\t * @param {string} css Stylesheet to transform\n\t * @param {Object|boolean} [options] Options object, or transformDirInUrl option (back-compat)\n\t * @param {boolean} [options.transformDirInUrl=false] Transform directions in URLs\n\t * (e.g. 'ltr', 'rtl')\n\t * @param {boolean} [options.transformEdgeInUrl=false] Transform edges in URLs\n\t * (e.g. 'left', 'right')\n\t * @param {boolean} [transformEdgeInUrl] Back-compat parameter\n\t * @return {string} Transformed stylesheet\n\t */\n\texports.transform = function ( css, options, transformEdgeInUrl ) {\n\t\tvar norm;\n\t\tif ( typeof options === 'object' ) {\n\t\t\tnorm = options;\n\t\t} else {\n\t\t\tnorm = {};\n\t\t\tif ( typeof options === 'boolean' ) {\n\t\t\t\tnorm.transformDirInUrl = options;\n\t\t\t}\n\t\t\tif ( typeof transformEdgeInUrl === 'boolean' ) {\n\t\t\t\tnorm.transformEdgeInUrl = transformEdgeInUrl;\n\t\t\t}\n\t\t}\n\t\treturn cssjanus.transform( css, norm );\n\t};\n} else if ( typeof window !== 'undefined' ) {\n\t/* global window */\n\t// Allow cssjanus to be used in a browser.\n\t// eslint-disable-next-line dot-notation\n\twindow[ 'cssjanus' ] = cssjanus;\n}\n", null, "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, token, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tif ((token(previous || 1) == 5 || token(peek() || 1) == 5) && strlen(characters) && substr(characters, -1, void 0) !== ' ') characters += ' '\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length || (variable === 0 && previous === 47)))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tswitch (atrule) {\n\t\t\t\t\t\t\t\t\t// c(ontainer)\n\t\t\t\t\t\t\t\t\tcase 99:\n\t\t\t\t\t\t\t\t\t\tif (charat(characters, 3) === 110) break\n\t\t\t\t\t\t\t\t\t// l(ayer)\n\t\t\t\t\t\t\t\t\tcase 108:\n\t\t\t\t\t\t\t\t\t\tif (charat(characters, 2) === 97) break\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\toffset = 0\n\t\t\t\t\t\t\t\t\t// d(ocument) m(edia) s(upports)\n\t\t\t\t\t\t\t\t\tcase 100: case 109: case 115:\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (offset) parse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\telse parse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RU<PERSON>SE<PERSON>, DECL<PERSON>AT<PERSON>, KEYFRAMES, NAMESPACE} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case NAMESPACE: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n"], "mappings": ";;;;;;AAAA;AAAA;AAoBA,QAAIA;AAaJ,aAAS,UAAW,OAAOC,QAAQ;AAElC,UAAI,UAAU,CAAC,GACd,QAAQ;AAST,eAAS,iBAAkBC,QAAQ;AAClC,gBAAQ,KAAMA,MAAM;AACpB,eAAOD;AAAA,MACR;AAQA,eAAS,qBAAqB;AAC7B,eAAO,QAAS,OAAQ;AAAA,MACzB;AAEA,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAON,UAAU,SAAW,KAAM;AAC1B,iBAAO,IAAI,QAAS,OAAO,gBAAiB;AAAA,QAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,YAAY,SAAW,KAAM;AAC5B,iBAAO,IAAI,QAAS,IAAI,OAAQ,MAAMA,SAAQ,KAAK,GAAI,GAAG,kBAAmB;AAAA,QAC9E;AAAA,MACD;AAAA,IACD;AAYA,aAAS,WAAW;AAEnB,UAEC,iBAAiB,SACjB,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,mBACpB,mBAAmB,kBACnB,eAAe,aAEf,kBAAkB,sBAClB,iBAAiB,4CACjB,aAAa,8BACb,cAAc,0DACd,mBAAmB,sBACnB,yBAAyB,cACzB,4BAA4B,aAC5B,mBAAmB,iBACnB,6BAA6B,YAC7B,gBAAgB,mCAChB,iBAAiB,yCACjB,gBAAgB,QAAQ,iBAAiB,4BACzC,iBAAiB,eAAe,kBAAkB,MAAM,gBAAgB,KACxE,gBAAgB,mBAAmB,kBAAkB,MAAM,gBAAgB,KAC3E,eAAe,OAAO,iBAAiB,gBAAgB,KACvD,eAAe,aAAa,YAAY,cAAc,MAAM,eAAe,MAC3E,qBAAqB,WAAW,eAAe,uBAC/C,2BAA2B,UAAU,aAAa,YAAY,cAAc,OAC5E,uBAAuB,uBACvB,sBAAsB,qBACtB,sBAAsB,QAAQ,sBAAsB,MAAM,2BAA2B,MAAM,uBAAuB,SAClH,cAAc,kBAAkB,sBAAsB,SACtD,yBAAyB,WAAW,eAAe,wBAAwB,cAAc,KACzF,gCAAgC,8CAChC,gCAAgC,sCAChC,eAAe,QAAQ,gBAAgB,uCAMvC,kBAAkB,QAAQ,yBAAyB,MAAM,kBAAkB,MAAM,gBAAgB,OACjG,4BAA4B,gBAC5B,+BAA+B,SAAS,gBAAgB,uFAA0F,eAAe,SACjK,kCAAkC,QAAQ,kBAAkB,4BAA4B,QACxF,kCAAkC,QAAQ,kBAAkB,4BAA4B,QACxF,gBAAgB,iCAEhB,uBAAuB,UACvB,0BAA0B,aAC1B,0BAA0B,aAC1B,gBAAgB,IAAI,OAAQ,gBAAgB,IAAK,GACjD,qBAAqB,IAAI,OAAQ,MAAM,gBAAgB,+BAA+B,aAAa,IAAK,GACxG,oBAAoB,IAAI,OAAQ,MAAM,gBAAgB,6BAA6B,MAAM,IAAK,GAC9F,qBAAqB,IAAI,OAAQ,MAAM,mBAAmB,QAAQ,IAAK,GACvE,qBAAqB,IAAI,OAAQ,MAAM,mBAAmB,QAAQ,IAAK,GACvE,aAAa,IAAI,OAAQ,mBAAmB,WAAW,4BAA4B,kCAAkC,8BAA8B,IAAK,GACxJ,cAAc,IAAI,OAAQ,mBAAmB,YAAY,4BAA4B,kCAAkC,8BAA8B,IAAK,GAC1J,kBAAkB,IAAI,OAAQ,mBAAmB,WAAW,iCAAiC,IAAK,GAClG,mBAAmB,IAAI,OAAQ,mBAAmB,YAAY,iCAAiC,IAAK,GACpG,iBAAiB,wBACjB,iBAAiB,wBACjB,iBAAiB,IAAI,OAAQ,mBAAmB,UAAU,iCAAiC,IAAK,GAChG,iBAAiB,IAAI,OAAQ,mBAAmB,UAAU,iCAAiC,IAAK,GAChG,mBAAmB,IAAI,OAAQ,mBAAmB,mBAAmB,IAAK,GAC1E,mBAAmB,IAAI,OAAQ,mBAAmB,mBAAmB,IAAK,GAC1E,0BAA0B,IAAI,OAAQ,gCAAgC,yBAAyB,WAAW,yBAAyB,WAAW,yBAAyB,WAAW,yBAAyB,eAAe,IAAK,GAC/N,0BAA0B,IAAI,OAAQ,gCAAgC,eAAe,WAAW,eAAe,WAAW,eAAe,WAAW,eAAe,eAAe,IAAK,GACvL,+BAA+B,IAAI,OAAQ,6DAA6D,eAAe,KAAK,IAAK,GACjI,gCAAgC,IAAI,OAAQ,wCAAwC,aAAa,MAAM,IAAK,GAE5G,qBAAqB,IAAI,OAAQ,6BAA6B,qBAAqB,eAAe,qBAAqB,aAAa,qBAAqB,cAAc,qBAAqB,8BACjK,qBAAqB,aAAa,qBAAqB,cAAc,qBAAqB,cAAc,qBAAqB,SAAS,eAAe,IAAK,GACrL,kBAAkB,IAAI,OAAQ,wCAAwC,oBAAoB,IAAK,GAC/F,oBAAoB,IAAI,OAAQ,2BAA2B,qBAAqB,WAAW,cAAc,IAAK,GAC9G,oBAAoB,IAAI,OAAQ,2BAA2B,eAAe,WAAW,oBAAoB,IAAK,GAC9G,oBAAoB,IAAI,OAAQ,2BAA2B,oBAAoB,IAAK,GACpF,mBAAmB,IAAI,OAAQ,kDAAkD,qBAAqB,aAAa,IAAK,GACxH,kBAAkB,IAAI,OAAQ,iDAAiD,qBAAqB,kBAAkB,qBAAqB,kBAAkB,IAAK;AAWnK,eAAS,+BAAgCC,QAAO,KAAK,OAAQ;AAC5D,YAAI,KAAK;AACT,YAAK,MAAM,MAAO,EAAG,MAAM,KAAM;AAChC,gBAAM,MAAM,QAAS,GAAI;AACzB,cAAK,QAAQ,IAAK;AAEjB,kBAAM,MAAM,SAAS,MAAM;AAC3B,oBAAQ,MAAM,WAAY,KAAM;AAChC,oBAAQ,MAAM,QAAS,GAAI,IAAI;AAAA,UAChC,OAAO;AACN,oBAAQ,MAAM,WAAY,KAAM,IAAI;AAAA,UACrC;AAAA,QACD;AACA,eAAO,MAAM;AAAA,MACd;AASA,eAAS,uBAAwB,QAAS;AACzC,gBAAS,OAAO,QAAS;AAAA,UACxB,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AAC9D;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AAC9D;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AACpC;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,CAAE;AACvB;AAAA,QACF;AAEA,eAAO,OAAO,KAAM,GAAI;AAAA,MACzB;AAmBA,eAAS,yBAA0BA,QAAO,KAAM;AAC/C,YAAI,QACH,OAAO,CAAC,EAAE,MAAM,KAAM,SAAU,GAChC,aAAa,KAAK,MAAO,GAAG,CAAE,EAAE,OAAQ,SAAW,KAAM;AACxD,iBAAO;AAAA,QACR,CAAE,GACF,cAAc,KAAK,MAAO,GAAG,EAAG,EAAE,OAAQ,SAAW,KAAM;AAC1D,iBAAO;AAAA,QACR,CAAE,GACF,OAAO,KAAM,EAAG,KAAK;AAEtB,YAAK,YAAY,QAAS;AACzB,mBAAS,uBAAwB,UAAW,IAAI,QAAQ,uBAAwB,WAAY;AAAA,QAC7F,OAAO;AACN,mBAAS,uBAAwB,UAAW;AAAA,QAC7C;AAEA,eAAO,MAAM,SAAS;AAAA,MACvB;AAWA,eAAS,SAAU,OAAQ;AAC1B,YAAK,WAAY,KAAM,MAAM,GAAI;AAEhC,iBAAO;AAAA,QACR;AAEA,YAAK,MAAO,CAAE,MAAM,KAAM;AACzB,iBAAO,MAAM,MAAO,CAAE;AAAA,QACvB;AAEA,eAAO,MAAM;AAAA,MACd;AASA,eAAS,mBAAoBA,QAAO,UAAU,QAAS;AACtD,eAAO,WAAW,SAAU,MAAO;AAAA,MACpC;AAWA,eAAS,sBAAuBA,QAAO,UAAUC,SAAQ,QAAQ,QAAS;AACzE,eAAO,WAAWA,UAAS,SAAU,MAAO,IAAI;AAAA,MACjD;AAWA,eAAS,2BAA4BD,QAAO,UAAU,OAAO,OAAO,QAAS;AAC5E,eAAO,WAAW,QAAQ,QAAQ,SAAU,MAAO;AAAA,MACpD;AAEA,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYN,aAAa,SAAW,KAAK,SAAU;AAGtC,cAAI,wBAAwB,IAAI,UAAW,oBAAoB,iBAAkB,GAChF,uBAAuB,IAAI,UAAW,mBAAmB,gBAAiB,GAC1E,mBAAmB,IAAI,UAAW,eAAe,YAAa;AAG/D,gBAAM,iBAAiB;AAAA,YACtB,qBAAqB;AAAA,cACpB,sBAAsB;AAAA;AAAA;AAAA;AAAA,gBAIrB,IAAI,QAAS,KAAK,KAAM;AAAA,cACzB;AAAA,YACD;AAAA,UACD;AAGA,cAAK,QAAQ,mBAAoB;AAEhC,kBAAM,IACJ,QAAS,gBAAgB,OAAO,oBAAoB,IAAK,EACzD,QAAS,gBAAgB,OAAO,oBAAoB,IAAK,EACzD,QAAS,gBAAgB,OAAO,cAAe,EAC/C,QAAS,gBAAgB,OAAQ,EACjC,QAAS,sBAAsB,KAAM,EACrC,QAAS,yBAAyB,KAAM,EACxC,QAAS,yBAAyB,KAAM;AAAA,UAC3C;AACA,cAAK,QAAQ,oBAAqB;AAEjC,kBAAM,IACJ,QAAS,iBAAiB,OAAO,cAAe,EAChD,QAAS,kBAAkB,QAAS,EACpC,QAAS,sBAAsB,OAAQ;AAAA,UAC1C;AAGA,gBAAM,IAEJ,QAAS,oBAAoB,OAAO,cAAe,EACnD,QAAS,oBAAoB,OAAQ,EACrC,QAAS,sBAAsB,KAAM,EAErC,QAAS,YAAY,OAAO,cAAe,EAC3C,QAAS,aAAa,QAAS,EAC/B,QAAS,sBAAsB,OAAQ,EAEvC,QAAS,kBAAkB,SAAS,cAAe,EACnD,QAAS,kBAAkB,cAAe,EAC1C,QAAS,sBAAsB,UAAW,EAE1C,QAAS,oBAAoB,wBAAyB,EAEtD,QAAS,iBAAiB,kBAAmB,EAC7C,QAAS,mBAAmB,0BAA2B,EACvD,QAAS,mBAAmB,0BAA2B,EACvD,QAAS,mBAAmB,kBAAmB,EAE/C,QAAS,kBAAkB,qBAAsB,EACjD,QAAS,iBAAiB,qBAAsB,EAGhD,QAAS,yBAAyB,oBAAqB,EACvD,QAAS,yBAAyB,oBAAqB,EAEvD,QAAS,8BAA8B,8BAA+B,EACtE,QAAS,+BAA+B,8BAA+B;AAGzE,gBAAM,sBAAsB;AAAA,YAC3B,qBAAqB;AAAA,cACpB,iBAAiB,WAAY,GAAI;AAAA,YAClC;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAIA,IAAAF,YAAW,IAAI,SAAS;AAIxB,QAAK,OAAO,WAAW,eAAe,OAAO,SAAU;AAetD,cAAQ,YAAY,SAAW,KAAK,SAAS,oBAAqB;AACjE,YAAI;AACJ,YAAK,OAAO,YAAY,UAAW;AAClC,iBAAO;AAAA,QACR,OAAO;AACN,iBAAO,CAAC;AACR,cAAK,OAAO,YAAY,WAAY;AACnC,iBAAK,oBAAoB;AAAA,UAC1B;AACA,cAAK,OAAO,uBAAuB,WAAY;AAC9C,iBAAK,qBAAqB;AAAA,UAC3B;AAAA,QACD;AACA,eAAOA,UAAS,UAAW,KAAK,IAAK;AAAA,MACtC;AAAA,IACD,WAAY,OAAO,WAAW,aAAc;AAI3C,aAAQ,UAAW,IAAIA;AAAA,IACxB;AAAA;AAAA;;;ACzcA,sBAAqB;;;ACId,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,cAAc;AAGlB,IAAI,QAAQ;AACZ,IAAI,SAAS;AAGb,IAAI,WAAW;AAGf,IAAI,YAAY;;;ACZhB,IAAI,MAAM,KAAK;AAMf,IAAI,OAAO,OAAO;AAqBlB,SAAS,KAAM,OAAO;AAC5B,SAAO,MAAM,KAAK;AACnB;AAiBO,SAAS,QAAS,OAAO,SAAS,aAAa;AACrD,SAAO,MAAM,QAAQ,SAAS,WAAW;AAC1C;AAQO,SAAS,QAAS,OAAO,QAAQI,WAAU;AACjD,SAAO,MAAM,QAAQ,QAAQA,SAAQ;AACtC;AAOO,SAAS,OAAQ,OAAO,OAAO;AACrC,SAAO,MAAM,WAAW,KAAK,IAAI;AAClC;AAQO,SAAS,OAAQ,OAAO,OAAO,KAAK;AAC1C,SAAO,MAAM,MAAM,OAAO,GAAG;AAC9B;AAMO,SAAS,OAAQ,OAAO;AAC9B,SAAO,MAAM;AACd;AAMO,SAAS,OAAQ,OAAO;AAC9B,SAAO,MAAM;AACd;AAOO,SAAS,OAAQ,OAAO,OAAO;AACrC,SAAO,MAAM,KAAK,KAAK,GAAG;AAC3B;;;ACxGO,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,aAAa;AAYjB,SAAS,KAAM,OAAO,MAAM,QAAQ,MAAM,OAAO,UAAUC,SAAQ,UAAU;AACnF,SAAO,EAAC,OAAc,MAAY,QAAgB,MAAY,OAAc,UAAoB,MAAY,QAAgB,QAAQA,SAAQ,QAAQ,IAAI,SAAkB;AAC3K;AAwBO,SAAS,OAAQ;AACvB,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,cAAY,WAAW,IAAI,OAAO,YAAY,EAAE,QAAQ,IAAI;AAE5D,MAAI,UAAU,cAAc;AAC3B,aAAS,GAAG;AAEb,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,cAAY,WAAW,SAAS,OAAO,YAAY,UAAU,IAAI;AAEjE,MAAI,UAAU,cAAc;AAC3B,aAAS,GAAG;AAEb,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,SAAO,OAAO,YAAY,QAAQ;AACnC;AAKO,SAAS,QAAS;AACxB,SAAO;AACR;AAOO,SAAS,MAAO,OAAO,KAAK;AAClC,SAAO,OAAO,YAAY,OAAO,GAAG;AACrC;AAMO,SAAS,MAAO,MAAM;AAC5B,UAAQ,MAAM;AAAA,IAEb,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AACtC,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAE3D,KAAK;AAAA,IAAI,KAAK;AAAA,IAAK,KAAK;AACvB,aAAO;AAAA,IAER,KAAK;AACJ,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAC/B,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AACb,aAAO;AAAA,EACT;AAEA,SAAO;AACR;AAMO,SAAS,MAAO,OAAO;AAC7B,SAAO,OAAO,SAAS,GAAG,SAAS,OAAO,aAAa,KAAK,GAAG,WAAW,GAAG,CAAC;AAC/E;AAMO,SAAS,QAAS,OAAO;AAC/B,SAAO,aAAa,IAAI;AACzB;AAMO,SAAS,QAAS,MAAM;AAC9B,SAAO,KAAK,MAAM,WAAW,GAAG,UAAU,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AACnG;AAcO,SAAS,WAAY,MAAM;AACjC,SAAO,YAAY,KAAK;AACvB,QAAI,YAAY;AACf,WAAK;AAAA;AAEL;AAEF,SAAO,MAAM,IAAI,IAAI,KAAK,MAAM,SAAS,IAAI,IAAI,KAAK;AACvD;AAwBO,SAAS,SAAU,OAAO,OAAO;AACvC,SAAO,EAAE,SAAS,KAAK;AAEtB,QAAI,YAAY,MAAM,YAAY,OAAQ,YAAY,MAAM,YAAY,MAAQ,YAAY,MAAM,YAAY;AAC7G;AAEF,SAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,GAAG;AAC1E;AAMO,SAAS,UAAW,MAAM;AAChC,SAAO,KAAK;AACX,YAAQ,WAAW;AAAA,MAElB,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AAAA,MAAI,KAAK;AACb,YAAI,SAAS,MAAM,SAAS;AAC3B,oBAAU,SAAS;AACpB;AAAA,MAED,KAAK;AACJ,YAAI,SAAS;AACZ,oBAAU,IAAI;AACf;AAAA,MAED,KAAK;AACJ,aAAK;AACL;AAAA,IACF;AAED,SAAO;AACR;AAOO,SAAS,UAAW,MAAM,OAAO;AACvC,SAAO,KAAK;AAEX,QAAI,OAAO,cAAc,KAAK;AAC7B;AAAA,aAEQ,OAAO,cAAc,KAAK,MAAM,KAAK,MAAM;AACnD;AAEF,SAAO,OAAO,MAAM,OAAO,WAAW,CAAC,IAAI,MAAM,KAAK,SAAS,KAAK,OAAO,KAAK,CAAC;AAClF;AAMO,SAAS,WAAY,OAAO;AAClC,SAAO,CAAC,MAAM,KAAK,CAAC;AACnB,SAAK;AAEN,SAAO,MAAM,OAAO,QAAQ;AAC7B;;;ACxPO,SAAS,QAAS,OAAO;AAC/B,SAAO,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,CAAC,EAAE,GAAG,QAAQ,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACtF;AAcO,SAAS,MAAO,OAAO,MAAM,QAAQ,MAAM,OAAO,UAAU,QAAQ,QAAQ,cAAc;AAChG,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAIC,UAAS;AACb,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAIC,aAAY;AAChB,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAIC,cAAa;AAEjB,SAAO;AACN,YAAQ,WAAWD,YAAWA,aAAY,KAAK,GAAG;AAAA,MAEjD,KAAK;AACJ,YAAI,YAAY,OAAO,OAAOC,aAAYF,UAAS,CAAC,KAAK,IAAI;AAC5D,cAAI,QAAQE,eAAc,QAAQ,QAAQD,UAAS,GAAG,KAAK,KAAK,GAAG,OAAO,IAAI,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK;AAChH,wBAAY;AACb;AAAA,QACD;AAAA,MAED,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AACtB,QAAAC,eAAc,QAAQD,UAAS;AAC/B;AAAA,MAED,KAAK;AAAA,MAAG,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AAC9B,QAAAC,eAAc,WAAW,QAAQ;AACjC;AAAA,MAED,KAAK;AACJ,QAAAA,eAAc,SAAS,MAAM,IAAI,GAAG,CAAC;AACrC;AAAA,MAED,KAAK;AACJ,gBAAQ,KAAK,GAAG;AAAA,UACf,KAAK;AAAA,UAAI,KAAK;AACb,mBAAO,QAAQ,UAAU,KAAK,GAAG,MAAM,CAAC,GAAG,MAAM,QAAQ,YAAY,GAAG,YAAY;AACpF,iBAAK,MAAM,YAAY,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,OAAOA,WAAU,KAAK,OAAOA,aAAY,IAAI,MAAM,MAAM,IAAK,CAAAA,eAAc;AAC1I;AAAA,UACD;AACC,YAAAA,eAAc;AAAA,QAChB;AACA;AAAA,MAED,KAAK,MAAM;AACV,eAAO,OAAO,IAAI,OAAOA,WAAU,IAAI;AAAA,MAExC,KAAK,MAAM;AAAA,MAAU,KAAK;AAAA,MAAI,KAAK;AAClC,gBAAQD,YAAW;AAAA,UAElB,KAAK;AAAA,UAAG,KAAK;AAAK,uBAAW;AAAA,UAE7B,KAAK,KAAK;AAAQ,gBAAI,aAAa,GAAI,CAAAC,cAAa,QAAQA,aAAY,OAAO,EAAE;AAChF,gBAAI,WAAW,MAAM,OAAOA,WAAU,IAAIF,WAAW,aAAa,KAAK,aAAa;AACnF,qBAAO,WAAW,KAAK,YAAYE,cAAa,KAAK,MAAM,QAAQF,UAAS,GAAG,YAAY,IAAI,YAAY,QAAQE,aAAY,KAAK,EAAE,IAAI,KAAK,MAAM,QAAQF,UAAS,GAAG,YAAY,GAAG,YAAY;AACrM;AAAA,UAED,KAAK;AAAI,YAAAE,eAAc;AAAA,UAEvB;AACC,mBAAO,YAAY,QAAQA,aAAY,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAGF,SAAQ,QAAQ,GAAG,QAAQ;AAE/I,gBAAIC,eAAc;AACjB,kBAAI,WAAW;AACd,sBAAMC,aAAY,MAAM,WAAW,WAAW,OAAO,UAAUF,SAAQ,QAAQ,QAAQ;AAAA,mBACnF;AACJ,wBAAQ,QAAQ;AAAA,kBAEf,KAAK;AACJ,wBAAI,OAAOE,aAAY,CAAC,MAAM,IAAK;AAAA,kBAEpC,KAAK;AACJ,wBAAI,OAAOA,aAAY,CAAC,MAAM,GAAI;AAAA,kBACnC;AACC,6BAAS;AAAA,kBAEV,KAAK;AAAA,kBAAK,KAAK;AAAA,kBAAK,KAAK;AAAA,gBAC1B;AACA,oBAAI,OAAQ,OAAM,OAAO,WAAW,WAAW,QAAQ,OAAO,QAAQ,OAAO,WAAW,WAAW,GAAG,GAAG,OAAO,QAAQ,MAAM,OAAO,QAAQ,CAAC,GAAGF,SAAQ,QAAQ,GAAG,QAAQ,GAAG,OAAO,UAAUA,SAAQ,QAAQ,OAAO,QAAQ,QAAQ;AAAA,oBAClO,OAAME,aAAY,WAAW,WAAW,WAAW,CAAC,EAAE,GAAG,UAAU,GAAG,QAAQ,QAAQ;AAAA,cAC5F;AAAA,QACH;AAEA,gBAAQ,SAAS,WAAW,GAAG,WAAW,YAAY,GAAG,OAAOA,cAAa,IAAIF,UAAS;AAC1F;AAAA,MAED,KAAK;AACJ,QAAAA,UAAS,IAAI,OAAOE,WAAU,GAAG,WAAW;AAAA,MAC7C;AACC,YAAI,WAAW;AACd,cAAID,cAAa;AAChB,cAAE;AAAA,mBACMA,cAAa,OAAO,cAAc,KAAK,KAAK,KAAK;AACzD;AAAA;AAEF,gBAAQC,eAAc,KAAKD,UAAS,GAAGA,aAAY,UAAU;AAAA,UAE5D,KAAK;AACJ,wBAAY,SAAS,IAAI,KAAKC,eAAc,MAAM;AAClD;AAAA,UAED,KAAK;AACJ,mBAAO,OAAO,KAAK,OAAOA,WAAU,IAAI,KAAK,WAAW,YAAY;AACpE;AAAA,UAED,KAAK;AAEJ,gBAAI,KAAK,MAAM;AACd,cAAAA,eAAc,QAAQ,KAAK,CAAC;AAE7B,qBAAS,KAAK,GAAG,SAASF,UAAS,OAAO,OAAOE,eAAc,WAAW,MAAM,CAAC,CAAC,GAAGD;AACrF;AAAA,UAED,KAAK;AACJ,gBAAI,aAAa,MAAM,OAAOC,WAAU,KAAK;AAC5C,yBAAW;AAAA,QACd;AAAA,IACF;AAED,SAAO;AACR;AAiBO,SAAS,QAAS,OAAO,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM,OAAO,UAAUF,SAAQ,UAAU;AACpH,MAAI,OAAO,SAAS;AACpB,MAAI,OAAO,WAAW,IAAI,QAAQ,CAAC,EAAE;AACrC,MAAI,OAAO,OAAO,IAAI;AAEtB,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,EAAE;AAC1C,aAAS,IAAI,GAAG,IAAI,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,EAAE;AAC9F,UAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,IAAI,QAAQ,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC;AACnE,cAAM,GAAG,IAAI;AAEhB,SAAO,KAAK,OAAO,MAAM,QAAQ,WAAW,IAAI,UAAU,MAAM,OAAO,UAAUA,SAAQ,QAAQ;AAClG;AASO,SAAS,QAAS,OAAO,MAAM,QAAQ,UAAU;AACvD,SAAO,KAAK,OAAO,MAAM,QAAQ,SAAS,KAAK,KAAK,CAAC,GAAG,OAAO,OAAO,GAAG,EAAE,GAAG,GAAG,QAAQ;AAC1F;AAUO,SAAS,YAAa,OAAO,MAAM,QAAQA,SAAQ,UAAU;AACnE,SAAO,KAAK,OAAO,MAAM,QAAQ,aAAa,OAAO,OAAO,GAAGA,OAAM,GAAG,OAAO,OAAOA,UAAS,GAAG,EAAE,GAAGA,SAAQ,QAAQ;AACxH;;;ACjMO,SAAS,UAAW,UAAU,UAAU;AAC9C,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACpC,cAAU,SAAS,SAAS,CAAC,GAAG,GAAG,UAAU,QAAQ,KAAK;AAE3D,SAAO;AACR;;;ALEA,SAAS,0BACP,SACA,OACA,UAA6B;AAE7B,UAAQ,QAAQ,MAAM;IACpB,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAQ,QAAQ,SAAS,QAAQ,UAAU,QAAQ;IACrD,KAAK,SAAS;AACZ,cAAQ,QAAQ,MAAM,QAAQ,QAAQ,KAAK,IAAI,QAAQ,MAAM,KAAK,GAAG,IAAI,QAAQ;AAEjF,UAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,gBAAQ,SAAS,QAAQ,SAAC,GAAC;AACzB,cAAI,EAAE,SAAS;AAAS,cAAE,WAAW,EAAE;QACzC,CAAC;;;;AAKP,MAAM,qBAAqB,UAAU,MAAM,UAAU,OAAO,QAAQ,QAAQ,GAAG,yBAAyB;AAExG,SAAO,OAAO,kBAAkB,IAAK,QAAQ,SAAS,QAAQ,QAAQ,MAAM,qBAAqB,MAAO;AAC1G;AAEA,SAAS,gBACP,SACA,OACA,UACA,UAA6B;AAE7B,MACE,QAAQ,SAAS,aACjB,QAAQ,SAAS,YAChB,QAAQ,SAAS,YAAY,CAAC,QAAQ,UAAU,QAAQ,OAAO,SAAS,SAAS,QAAQ,OAAO,SAAS,UAC1G;AACA,QAAM,cAAc,gBAAAG,QAAS,UAAU,0BAA0B,SAAS,OAAO,QAAQ,CAAC;AAC1F,YAAQ,WAAW,cAAc,QAAQ,WAAW,EAAE,CAAC,EAAE,WAAW,CAAA;AAEpE,YAAQ,SAAS;;AAErB;AAIA,OAAO,eAAe,iBAAiB,QAAQ,EAAE,OAAO,kBAAiB,CAAE;AAE3E,IAAA,qBAAe;", "names": ["c<PERSON><PERSON><PERSON>", "token", "match", "prefix", "position", "length", "length", "character", "characters", "c<PERSON><PERSON><PERSON>"]}