"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF]);
const Image = {
  id: 0x22,
  type: 'IMAGE',
  name: 'Image',
  hasTableName: true,
  declaration: function () {
    return 'image';
  },
  resolveLength: function (parameter) {
    if (parameter.value != null) {
      const value = parameter.value; // TODO: Temporary solution. Replace 'any' more with specific type;
      return value.length;
    } else {
      return -1;
    }
  },
  generateTypeInfo(parameter) {
    const buffer = Buffer.alloc(5);
    buffer.writeUInt8(this.id, 0);
    buffer.writeInt32LE(parameter.length, 1);
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const buffer = Buffer.alloc(4);
    buffer.writeInt32LE(parameter.value.length, 0);
    return buffer;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    yield parameter.value;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (!Buffer.isBuffer(value)) {
      throw new TypeError('Invalid buffer.');
    }
    return value;
  }
};
var _default = Image;
exports.default = _default;
module.exports = Image;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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