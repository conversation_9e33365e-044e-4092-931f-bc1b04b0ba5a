// Export all services
export { default as apiClient, apiService } from './apiClient';
export { default as productService } from './productService';
export { default as categoryService } from './categoryService';
export { default as salesService } from './salesService';
export { default as authService } from './authService';
export { default as dashboardService } from './dashboardService';

// Export types
export type { ApiResponse, PaginatedResponse } from './apiClient';
export type { Category, CategoryFormData } from './categoryService';
export type { 
  SalesInvoice, 
  SalesInvoiceDetail, 
  CreateSalesInvoiceData, 
  SalesStatistics 
} from './salesService';
export type { 
  User, 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  ChangePasswordRequest 
} from './authService';
