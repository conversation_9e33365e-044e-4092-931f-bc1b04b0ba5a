{"version": 3, "file": "infoerror-token-parser.js", "names": ["_helpers", "require", "_token", "readToken", "buf", "offset", "options", "token<PERSON><PERSON>th", "value", "readUInt16LE", "length", "NotEnoughDataError", "number", "readUInt32LE", "state", "readUInt8", "clazz", "message", "readUsVarChar", "serverName", "readBVarChar", "procName", "lineNumber", "tdsVersion", "Result", "infoParser", "data", "InfoMessageToken", "error<PERSON><PERSON>er", "ErrorMessageToken"], "sources": ["../../src/token/infoerror-token-parser.ts"], "sourcesContent": ["import { NotEnoughDataError, readBVar<PERSON>har, readUInt16LE, readUInt32LE, readUInt8, readUsVarChar, Result } from './helpers';\nimport { type ParserOptions } from './stream-parser';\n\nimport { InfoMessageToken, ErrorMessageToken } from './token';\n\ninterface TokenData {\n  number: number;\n  state: number;\n  class: number;\n  message: string;\n  serverName: string;\n  procName: string;\n  lineNumber: number;\n}\n\nfunction readToken(buf: Buffer, offset: number, options: ParserOptions): Result<TokenData> {\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < tokenLength + offset) {\n    throw new NotEnoughDataError(tokenLength + offset);\n  }\n\n  let number;\n  ({ offset, value: number } = readUInt32LE(buf, offset));\n\n  let state;\n  ({ offset, value: state } = readUInt8(buf, offset));\n\n  let clazz;\n  ({ offset, value: clazz } = readUInt8(buf, offset));\n\n  let message;\n  ({ offset, value: message } = readUsVarChar(buf, offset));\n\n  let serverName;\n  ({ offset, value: serverName } = readBVarChar(buf, offset));\n\n  let procName;\n  ({ offset, value: procName } = readBVarChar(buf, offset));\n\n  let lineNumber;\n  ({ offset, value: lineNumber } = options.tdsVersion < '7_2' ? readUInt16LE(buf, offset) : readUInt32LE(buf, offset));\n\n  return new Result({\n    'number': number,\n    'state': state,\n    'class': clazz,\n    'message': message,\n    'serverName': serverName,\n    'procName': procName,\n    'lineNumber': lineNumber\n  }, offset);\n}\n\nexport function infoParser(buf: Buffer, offset: number, options: ParserOptions): Result<InfoMessageToken> {\n  let data;\n  ({ offset, value: data } = readToken(buf, offset, options));\n\n  return new Result(new InfoMessageToken(data), offset);\n}\n\nexport function errorParser(buf: Buffer, offset: number, options: ParserOptions): Result<ErrorMessageToken> {\n  let data;\n  ({ offset, value: data } = readToken(buf, offset, options));\n\n  return new Result(new ErrorMessageToken(data), offset);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAYA,SAASE,SAASA,CAACC,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAAqB;EACzF,IAAIC,WAAW;EACf,CAAC;IAAEF,MAAM;IAAEG,KAAK,EAAED;EAAY,CAAC,GAAG,IAAAE,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAID,GAAG,CAACM,MAAM,GAAGH,WAAW,GAAGF,MAAM,EAAE;IACrC,MAAM,IAAIM,2BAAkB,CAACJ,WAAW,GAAGF,MAAM,CAAC;EACpD;EAEA,IAAIO,MAAM;EACV,CAAC;IAAEP,MAAM;IAAEG,KAAK,EAAEI;EAAO,CAAC,GAAG,IAAAC,qBAAY,EAACT,GAAG,EAAEC,MAAM,CAAC;EAEtD,IAAIS,KAAK;EACT,CAAC;IAAET,MAAM;IAAEG,KAAK,EAAEM;EAAM,CAAC,GAAG,IAAAC,kBAAS,EAACX,GAAG,EAAEC,MAAM,CAAC;EAElD,IAAIW,KAAK;EACT,CAAC;IAAEX,MAAM;IAAEG,KAAK,EAAEQ;EAAM,CAAC,GAAG,IAAAD,kBAAS,EAACX,GAAG,EAAEC,MAAM,CAAC;EAElD,IAAIY,OAAO;EACX,CAAC;IAAEZ,MAAM;IAAEG,KAAK,EAAES;EAAQ,CAAC,GAAG,IAAAC,sBAAa,EAACd,GAAG,EAAEC,MAAM,CAAC;EAExD,IAAIc,UAAU;EACd,CAAC;IAAEd,MAAM;IAAEG,KAAK,EAAEW;EAAW,CAAC,GAAG,IAAAC,qBAAY,EAAChB,GAAG,EAAEC,MAAM,CAAC;EAE1D,IAAIgB,QAAQ;EACZ,CAAC;IAAEhB,MAAM;IAAEG,KAAK,EAAEa;EAAS,CAAC,GAAG,IAAAD,qBAAY,EAAChB,GAAG,EAAEC,MAAM,CAAC;EAExD,IAAIiB,UAAU;EACd,CAAC;IAAEjB,MAAM;IAAEG,KAAK,EAAEc;EAAW,CAAC,GAAGhB,OAAO,CAACiB,UAAU,GAAG,KAAK,GAAG,IAAAd,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC,GAAG,IAAAQ,qBAAY,EAACT,GAAG,EAAEC,MAAM,CAAC;EAEnH,OAAO,IAAImB,eAAM,CAAC;IAChB,QAAQ,EAAEZ,MAAM;IAChB,OAAO,EAAEE,KAAK;IACd,OAAO,EAAEE,KAAK;IACd,SAAS,EAAEC,OAAO;IAClB,YAAY,EAAEE,UAAU;IACxB,UAAU,EAAEE,QAAQ;IACpB,YAAY,EAAEC;EAChB,CAAC,EAAEjB,MAAM,CAAC;AACZ;AAEO,SAASoB,UAAUA,CAACrB,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAA4B;EACxG,IAAIoB,IAAI;EACR,CAAC;IAAErB,MAAM;IAAEG,KAAK,EAAEkB;EAAK,CAAC,GAAGvB,SAAS,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;EAE1D,OAAO,IAAIkB,eAAM,CAAC,IAAIG,uBAAgB,CAACD,IAAI,CAAC,EAAErB,MAAM,CAAC;AACvD;AAEO,SAASuB,WAAWA,CAACxB,GAAW,EAAEC,MAAc,EAAEC,OAAsB,EAA6B;EAC1G,IAAIoB,IAAI;EACR,CAAC;IAAErB,MAAM;IAAEG,KAAK,EAAEkB;EAAK,CAAC,GAAGvB,SAAS,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;EAE1D,OAAO,IAAIkB,eAAM,CAAC,IAAIK,wBAAiB,CAACH,IAAI,CAAC,EAAErB,MAAM,CAAC;AACxD"}