import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  ShoppingBag as OrdersIcon,
} from '@mui/icons-material';
import PageContainer from '../components/PageContainer';
import DashboardHeader from '../components/DashboardHeader';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'vip';
  joinDate: string;
  lastOrder: string;
  avatar?: string;
}

const Customers: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Mock data
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: 1,
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '+966501234567',
        address: 'شارع الملك فهد، حي النخيل',
        city: 'الرياض',
        totalOrders: 15,
        totalSpent: 12500,
        status: 'vip',
        joinDate: '2023-06-15',
        lastOrder: '2024-01-20',
        avatar: '/api/placeholder/100/100',
      },
      {
        id: 2,
        name: 'فاطمة أحمد السالم',
        email: '<EMAIL>',
        phone: '+966507654321',
        address: 'طريق الأمير محمد بن عبدالعزيز',
        city: 'جدة',
        totalOrders: 8,
        totalSpent: 6800,
        status: 'active',
        joinDate: '2023-08-22',
        lastOrder: '2024-01-18',
      },
      {
        id: 3,
        name: 'محمد عبدالله الغامدي',
        email: '<EMAIL>',
        phone: '+966512345678',
        address: 'شارع التحلية، حي السلامة',
        city: 'الدمام',
        totalOrders: 22,
        totalSpent: 18900,
        status: 'vip',
        joinDate: '2023-03-10',
        lastOrder: '2024-01-19',
      },
      {
        id: 4,
        name: 'نورا سعد الحربي',
        email: '<EMAIL>',
        phone: '+966598765432',
        address: 'حي الملقا، شارع العليا',
        city: 'الرياض',
        totalOrders: 3,
        totalSpent: 2100,
        status: 'active',
        joinDate: '2023-11-05',
        lastOrder: '2024-01-15',
      },
      {
        id: 5,
        name: 'خالد عبدالرحمن القحطاني',
        email: '<EMAIL>',
        phone: '+966543216789',
        address: 'شارع الأمير سلطان',
        city: 'أبها',
        totalOrders: 1,
        totalSpent: 450,
        status: 'inactive',
        joinDate: '2023-12-01',
        lastOrder: '2023-12-15',
      },
    ];

    setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
    }, 1000);
  }, []);

  const statuses = ['all', 'active', 'inactive', 'vip'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'vip': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'vip': return 'عميل مميز';
      default: return status;
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setOpenDialog(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setOpenDialog(true);
  };

  const handleDeleteCustomer = (customerId: number) => {
    setCustomers(customers.filter(c => c.id !== customerId));
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Typography variant="h6">جاري تحميل العملاء...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <DashboardHeader
        title="إدارة العملاء"
        subtitle="عرض وإدارة جميع عملاء المتجر"
        showBreadcrumbs={true}
        showLastUpdate={true}
      />

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="primary">
                    {customers.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي العملاء
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <PersonIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="success.main">
                    {customers.filter(c => c.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    عملاء نشطون
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <PersonIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {customers.filter(c => c.status === 'vip').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    عملاء مميزون
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <PersonIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="info.main">
                    {customers.reduce((sum, c) => sum + c.totalOrders, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <OrdersIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="البحث في العملاء (الاسم، البريد الإلكتروني، رقم الهاتف)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="الحالة"
              >
                {statuses.map(status => (
                  <MenuItem key={status} value={status}>
                    {status === 'all' ? 'جميع الحالات' : getStatusText(status)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Card>

      {/* Customers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>العميل</TableCell>
              <TableCell>معلومات الاتصال</TableCell>
              <TableCell>الموقع</TableCell>
              <TableCell>الطلبات</TableCell>
              <TableCell>إجمالي الإنفاق</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>آخر طلب</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCustomers.map((customer) => (
              <TableRow key={customer.id}>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Badge
                      badgeContent={customer.status === 'vip' ? '⭐' : null}
                      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                    >
                      <Avatar src={customer.avatar} alt={customer.name}>
                        {customer.name.charAt(0)}
                      </Avatar>
                    </Badge>
                    <Box>
                      <Typography variant="subtitle2">{customer.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        عضو منذ {formatDate(customer.joinDate)}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                      <EmailIcon fontSize="small" color="action" />
                      <Typography variant="body2">{customer.email}</Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <PhoneIcon fontSize="small" color="action" />
                      <Typography variant="body2">{customer.phone}</Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LocationIcon fontSize="small" color="action" />
                    <Box>
                      <Typography variant="body2">{customer.city}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {customer.address}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2">{customer.totalOrders}</Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" color="success.main">
                    {formatCurrency(customer.totalSpent)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusText(customer.status)}
                    color={getStatusColor(customer.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(customer.lastOrder)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="عرض">
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="تعديل">
                      <IconButton size="small" onClick={() => handleEditCustomer(customer)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف">
                      <IconButton size="small" color="error" onClick={() => handleDeleteCustomer(customer.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Customer FAB */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={handleAddCustomer}
      >
        <AddIcon />
      </Fab>

      {/* Add/Edit Customer Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedCustomer ? 'تعديل العميل' : 'إضافة عميل جديد'}
        </DialogTitle>
        <DialogContent>
          <Typography>نموذج إضافة/تعديل العميل سيتم إضافته لاحقاً</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>إلغاء</Button>
          <Button variant="contained">حفظ</Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default Customers;
