"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var iconv = _interopRequireWildcard(require("iconv-lite"));
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// s2.2.7.17

async function rowParser(parser) {
  const columns = [];
  for (const metadata of parser.colMetadata) {
    while (true) {
      if ((0, _valueParser.isPLPStream)(metadata)) {
        const chunks = await (0, _valueParser.readPLPStream)(parser);
        if (chunks === null) {
          columns.push({
            value: chunks,
            metadata
          });
        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
          columns.push({
            value: Buffer.concat(chunks).toString('ucs2'),
            metadata
          });
        } else if (metadata.type.name === 'VarChar') {
          var _metadata$collation;
          columns.push({
            value: iconv.decode(Buffer.concat(chunks), ((_metadata$collation = metadata.collation) === null || _metadata$collation === void 0 ? void 0 : _metadata$collation.codepage) ?? 'utf8'),
            metadata
          });
        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
          columns.push({
            value: Buffer.concat(chunks),
            metadata
          });
        }
      } else {
        let result;
        try {
          result = (0, _valueParser.readValue)(parser.buffer, parser.position, metadata, parser.options);
        } catch (err) {
          if (err instanceof _helpers.NotEnoughDataError) {
            await parser.waitForChunk();
            continue;
          }
          throw err;
        }
        parser.position = result.offset;
        columns.push({
          value: result.value,
          metadata
        });
      }
      break;
    }
  }
  if (parser.options.useColumnNames) {
    const columnsMap = Object.create(null);
    columns.forEach(column => {
      const colName = column.metadata.colName;
      if (columnsMap[colName] == null) {
        columnsMap[colName] = column;
      }
    });
    return new _token.RowToken(columnsMap);
  } else {
    return new _token.RowToken(columns);
  }
}
var _default = rowParser;
exports.default = _default;
module.exports = rowParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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