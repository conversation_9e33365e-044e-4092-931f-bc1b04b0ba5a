"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var _metadataParser = require("../metadata-parser");
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
var iconv = _interopRequireWildcard(require("iconv-lite"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// s2.2.7.16

async function returnParser(parser) {
  let paramName;
  let paramOrdinal;
  let metadata;
  while (true) {
    const buf = parser.buffer;
    let offset = parser.position;
    try {
      ({
        offset,
        value: paramOrdinal
      } = (0, _helpers.readUInt16LE)(buf, offset));
      ({
        offset,
        value: paramName
      } = (0, _helpers.readBVarChar)(buf, offset));
      // status
      ({
        offset
      } = (0, _helpers.readUInt8)(buf, offset));
      ({
        offset,
        value: metadata
      } = (0, _metadataParser.readMetadata)(buf, offset, parser.options));
      if (paramName.charAt(0) === '@') {
        paramName = paramName.slice(1);
      }
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        await parser.waitForChunk();
        continue;
      }
      throw err;
    }
    parser.position = offset;
    break;
  }
  let value;
  while (true) {
    const buf = parser.buffer;
    let offset = parser.position;
    if ((0, _valueParser.isPLPStream)(metadata)) {
      const chunks = await (0, _valueParser.readPLPStream)(parser);
      if (chunks === null) {
        value = chunks;
      } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
        value = Buffer.concat(chunks).toString('ucs2');
      } else if (metadata.type.name === 'VarChar') {
        var _metadata$collation;
        value = iconv.decode(Buffer.concat(chunks), ((_metadata$collation = metadata.collation) === null || _metadata$collation === void 0 ? void 0 : _metadata$collation.codepage) ?? 'utf8');
      } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
        value = Buffer.concat(chunks);
      }
    } else {
      try {
        ({
          value,
          offset
        } = (0, _valueParser.readValue)(buf, offset, metadata, parser.options));
      } catch (err) {
        if (err instanceof _helpers.NotEnoughDataError) {
          await parser.waitForChunk();
          continue;
        }
        throw err;
      }
      parser.position = offset;
    }
    break;
  }
  return new _token.ReturnValueToken({
    paramOrdinal: paramOrdinal,
    paramName: paramName,
    metadata: metadata,
    value: value
  });
}
var _default = returnParser;
exports.default = _default;
module.exports = returnParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfdG9rZW4iLCJyZXF1aXJlIiwiX21ldGFkYXRhUGFyc2VyIiwiX3ZhbHVlUGFyc2VyIiwiX2hlbHBlcnMiLCJpY29udiIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwiX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlIiwibm9kZUludGVyb3AiLCJXZWFrTWFwIiwiY2FjaGVCYWJlbEludGVyb3AiLCJjYWNoZU5vZGVJbnRlcm9wIiwib2JqIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJjYWNoZSIsImhhcyIsImdldCIsIm5ld09iaiIsImhhc1Byb3BlcnR5RGVzY3JpcHRvciIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwia2V5IiwicHJvdG90eXBlIiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiZGVzYyIsInNldCIsInJldHVyblBhcnNlciIsInBhcnNlciIsInBhcmFtTmFtZSIsInBhcmFtT3JkaW5hbCIsIm1ldGFkYXRhIiwiYnVmIiwiYnVmZmVyIiwib2Zmc2V0IiwicG9zaXRpb24iLCJ2YWx1ZSIsInJlYWRVSW50MTZMRSIsInJlYWRCVmFyQ2hhciIsInJlYWRVSW50OCIsInJlYWRNZXRhZGF0YSIsIm9wdGlvbnMiLCJjaGFyQXQiLCJzbGljZSIsImVyciIsIk5vdEVub3VnaERhdGFFcnJvciIsIndhaXRGb3JDaHVuayIsImlzUExQU3RyZWFtIiwiY2h1bmtzIiwicmVhZFBMUFN0cmVhbSIsInR5cGUiLCJuYW1lIiwiQnVmZmVyIiwiY29uY2F0IiwidG9TdHJpbmciLCJfbWV0YWRhdGEkY29sbGF0aW9uIiwiZGVjb2RlIiwiY29sbGF0aW9uIiwiY29kZXBhZ2UiLCJyZWFkVmFsdWUiLCJSZXR1cm5WYWx1ZVRva2VuIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwibW9kdWxlIl0sInNvdXJjZXMiOlsiLi4vLi4vc3JjL3Rva2VuL3JldHVybnZhbHVlLXRva2VuLXBhcnNlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzMi4yLjcuMTZcblxuaW1wb3J0IFBhcnNlciBmcm9tICcuL3N0cmVhbS1wYXJzZXInO1xuXG5pbXBvcnQgeyBSZXR1cm5WYWx1ZVRva2VuIH0gZnJvbSAnLi90b2tlbic7XG5cbmltcG9ydCB7IHJlYWRNZXRhZGF0YSB9IGZyb20gJy4uL21ldGFkYXRhLXBhcnNlcic7XG5pbXBvcnQgeyBpc1BMUFN0cmVhbSwgcmVhZFBMUFN0cmVhbSwgcmVhZFZhbHVlIH0gZnJvbSAnLi4vdmFsdWUtcGFyc2VyJztcbmltcG9ydCB7IE5vdEVub3VnaERhdGFFcnJvciwgcmVhZEJWYXJDaGFyLCByZWFkVUludDE2TEUsIHJlYWRVSW50OCB9IGZyb20gJy4vaGVscGVycyc7XG5pbXBvcnQgKiBhcyBpY29udiBmcm9tICdpY29udi1saXRlJztcblxuYXN5bmMgZnVuY3Rpb24gcmV0dXJuUGFyc2VyKHBhcnNlcjogUGFyc2VyKTogUHJvbWlzZTxSZXR1cm5WYWx1ZVRva2VuPiB7XG4gIGxldCBwYXJhbU5hbWU7XG4gIGxldCBwYXJhbU9yZGluYWw7XG4gIGxldCBtZXRhZGF0YTtcblxuICB3aGlsZSAodHJ1ZSkge1xuICAgIGNvbnN0IGJ1ZiA9IHBhcnNlci5idWZmZXI7XG4gICAgbGV0IG9mZnNldCA9IHBhcnNlci5wb3NpdGlvbjtcblxuICAgIHRyeSB7XG4gICAgICAoeyBvZmZzZXQsIHZhbHVlOiBwYXJhbU9yZGluYWwgfSA9IHJlYWRVSW50MTZMRShidWYsIG9mZnNldCkpO1xuICAgICAgKHsgb2Zmc2V0LCB2YWx1ZTogcGFyYW1OYW1lIH0gPSByZWFkQlZhckNoYXIoYnVmLCBvZmZzZXQpKTtcbiAgICAgIC8vIHN0YXR1c1xuICAgICAgKHsgb2Zmc2V0IH0gPSByZWFkVUludDgoYnVmLCBvZmZzZXQpKTtcbiAgICAgICh7IG9mZnNldCwgdmFsdWU6IG1ldGFkYXRhIH0gPSByZWFkTWV0YWRhdGEoYnVmLCBvZmZzZXQsIHBhcnNlci5vcHRpb25zKSk7XG5cbiAgICAgIGlmIChwYXJhbU5hbWUuY2hhckF0KDApID09PSAnQCcpIHtcbiAgICAgICAgcGFyYW1OYW1lID0gcGFyYW1OYW1lLnNsaWNlKDEpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgaWYgKGVyciBpbnN0YW5jZW9mIE5vdEVub3VnaERhdGFFcnJvcikge1xuICAgICAgICBhd2FpdCBwYXJzZXIud2FpdEZvckNodW5rKCk7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuXG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuXG4gICAgcGFyc2VyLnBvc2l0aW9uID0gb2Zmc2V0O1xuICAgIGJyZWFrO1xuICB9XG5cbiAgbGV0IHZhbHVlO1xuICB3aGlsZSAodHJ1ZSkge1xuICAgIGNvbnN0IGJ1ZiA9IHBhcnNlci5idWZmZXI7XG4gICAgbGV0IG9mZnNldCA9IHBhcnNlci5wb3NpdGlvbjtcblxuICAgIGlmIChpc1BMUFN0cmVhbShtZXRhZGF0YSkpIHtcbiAgICAgIGNvbnN0IGNodW5rcyA9IGF3YWl0IHJlYWRQTFBTdHJlYW0ocGFyc2VyKTtcblxuICAgICAgaWYgKGNodW5rcyA9PT0gbnVsbCkge1xuICAgICAgICB2YWx1ZSA9IGNodW5rcztcbiAgICAgIH0gZWxzZSBpZiAobWV0YWRhdGEudHlwZS5uYW1lID09PSAnTlZhckNoYXInIHx8IG1ldGFkYXRhLnR5cGUubmFtZSA9PT0gJ1htbCcpIHtcbiAgICAgICAgdmFsdWUgPSBCdWZmZXIuY29uY2F0KGNodW5rcykudG9TdHJpbmcoJ3VjczInKTtcbiAgICAgIH0gZWxzZSBpZiAobWV0YWRhdGEudHlwZS5uYW1lID09PSAnVmFyQ2hhcicpIHtcbiAgICAgICAgdmFsdWUgPSBpY29udi5kZWNvZGUoQnVmZmVyLmNvbmNhdChjaHVua3MpLCBtZXRhZGF0YS5jb2xsYXRpb24/LmNvZGVwYWdlID8/ICd1dGY4Jyk7XG4gICAgICB9IGVsc2UgaWYgKG1ldGFkYXRhLnR5cGUubmFtZSA9PT0gJ1ZhckJpbmFyeScgfHwgbWV0YWRhdGEudHlwZS5uYW1lID09PSAnVURUJykge1xuICAgICAgICB2YWx1ZSA9IEJ1ZmZlci5jb25jYXQoY2h1bmtzKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgdHJ5IHtcbiAgICAgICAgKHsgdmFsdWUsIG9mZnNldCB9ID0gcmVhZFZhbHVlKGJ1Ziwgb2Zmc2V0LCBtZXRhZGF0YSwgcGFyc2VyLm9wdGlvbnMpKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBpZiAoZXJyIGluc3RhbmNlb2YgTm90RW5vdWdoRGF0YUVycm9yKSB7XG4gICAgICAgICAgYXdhaXQgcGFyc2VyLndhaXRGb3JDaHVuaygpO1xuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG5cbiAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgfVxuXG4gICAgICBwYXJzZXIucG9zaXRpb24gPSBvZmZzZXQ7XG4gICAgfVxuXG4gICAgYnJlYWs7XG4gIH1cblxuICByZXR1cm4gbmV3IFJldHVyblZhbHVlVG9rZW4oe1xuICAgIHBhcmFtT3JkaW5hbDogcGFyYW1PcmRpbmFsLFxuICAgIHBhcmFtTmFtZTogcGFyYW1OYW1lLFxuICAgIG1ldGFkYXRhOiBtZXRhZGF0YSxcbiAgICB2YWx1ZTogdmFsdWVcbiAgfSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHJldHVyblBhcnNlcjtcbm1vZHVsZS5leHBvcnRzID0gcmV0dXJuUGFyc2VyO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFJQSxJQUFBQSxNQUFBLEdBQUFDLE9BQUE7QUFFQSxJQUFBQyxlQUFBLEdBQUFELE9BQUE7QUFDQSxJQUFBRSxZQUFBLEdBQUFGLE9BQUE7QUFDQSxJQUFBRyxRQUFBLEdBQUFILE9BQUE7QUFDQSxJQUFBSSxLQUFBLEdBQUFDLHVCQUFBLENBQUFMLE9BQUE7QUFBb0MsU0FBQU0seUJBQUFDLFdBQUEsZUFBQUMsT0FBQSxrQ0FBQUMsaUJBQUEsT0FBQUQsT0FBQSxRQUFBRSxnQkFBQSxPQUFBRixPQUFBLFlBQUFGLHdCQUFBLFlBQUFBLENBQUFDLFdBQUEsV0FBQUEsV0FBQSxHQUFBRyxnQkFBQSxHQUFBRCxpQkFBQSxLQUFBRixXQUFBO0FBQUEsU0FBQUYsd0JBQUFNLEdBQUEsRUFBQUosV0FBQSxTQUFBQSxXQUFBLElBQUFJLEdBQUEsSUFBQUEsR0FBQSxDQUFBQyxVQUFBLFdBQUFELEdBQUEsUUFBQUEsR0FBQSxvQkFBQUEsR0FBQSx3QkFBQUEsR0FBQSw0QkFBQUUsT0FBQSxFQUFBRixHQUFBLFVBQUFHLEtBQUEsR0FBQVIsd0JBQUEsQ0FBQUMsV0FBQSxPQUFBTyxLQUFBLElBQUFBLEtBQUEsQ0FBQUMsR0FBQSxDQUFBSixHQUFBLFlBQUFHLEtBQUEsQ0FBQUUsR0FBQSxDQUFBTCxHQUFBLFNBQUFNLE1BQUEsV0FBQUMscUJBQUEsR0FBQUMsTUFBQSxDQUFBQyxjQUFBLElBQUFELE1BQUEsQ0FBQUUsd0JBQUEsV0FBQUMsR0FBQSxJQUFBWCxHQUFBLFFBQUFXLEdBQUEsa0JBQUFILE1BQUEsQ0FBQUksU0FBQSxDQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQWQsR0FBQSxFQUFBVyxHQUFBLFNBQUFJLElBQUEsR0FBQVIscUJBQUEsR0FBQUMsTUFBQSxDQUFBRSx3QkFBQSxDQUFBVixHQUFBLEVBQUFXLEdBQUEsY0FBQUksSUFBQSxLQUFBQSxJQUFBLENBQUFWLEdBQUEsSUFBQVUsSUFBQSxDQUFBQyxHQUFBLEtBQUFSLE1BQUEsQ0FBQUMsY0FBQSxDQUFBSCxNQUFBLEVBQUFLLEdBQUEsRUFBQUksSUFBQSxZQUFBVCxNQUFBLENBQUFLLEdBQUEsSUFBQVgsR0FBQSxDQUFBVyxHQUFBLFNBQUFMLE1BQUEsQ0FBQUosT0FBQSxHQUFBRixHQUFBLE1BQUFHLEtBQUEsSUFBQUEsS0FBQSxDQUFBYSxHQUFBLENBQUFoQixHQUFBLEVBQUFNLE1BQUEsWUFBQUEsTUFBQTtBQVRwQzs7QUFXQSxlQUFlVyxZQUFZQSxDQUFDQyxNQUFjLEVBQTZCO0VBQ3JFLElBQUlDLFNBQVM7RUFDYixJQUFJQyxZQUFZO0VBQ2hCLElBQUlDLFFBQVE7RUFFWixPQUFPLElBQUksRUFBRTtJQUNYLE1BQU1DLEdBQUcsR0FBR0osTUFBTSxDQUFDSyxNQUFNO0lBQ3pCLElBQUlDLE1BQU0sR0FBR04sTUFBTSxDQUFDTyxRQUFRO0lBRTVCLElBQUk7TUFDRixDQUFDO1FBQUVELE1BQU07UUFBRUUsS0FBSyxFQUFFTjtNQUFhLENBQUMsR0FBRyxJQUFBTyxxQkFBWSxFQUFDTCxHQUFHLEVBQUVFLE1BQU0sQ0FBQztNQUM1RCxDQUFDO1FBQUVBLE1BQU07UUFBRUUsS0FBSyxFQUFFUDtNQUFVLENBQUMsR0FBRyxJQUFBUyxxQkFBWSxFQUFDTixHQUFHLEVBQUVFLE1BQU0sQ0FBQztNQUN6RDtNQUNBLENBQUM7UUFBRUE7TUFBTyxDQUFDLEdBQUcsSUFBQUssa0JBQVMsRUFBQ1AsR0FBRyxFQUFFRSxNQUFNLENBQUM7TUFDcEMsQ0FBQztRQUFFQSxNQUFNO1FBQUVFLEtBQUssRUFBRUw7TUFBUyxDQUFDLEdBQUcsSUFBQVMsNEJBQVksRUFBQ1IsR0FBRyxFQUFFRSxNQUFNLEVBQUVOLE1BQU0sQ0FBQ2EsT0FBTyxDQUFDO01BRXhFLElBQUlaLFNBQVMsQ0FBQ2EsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLEdBQUcsRUFBRTtRQUMvQmIsU0FBUyxHQUFHQSxTQUFTLENBQUNjLEtBQUssQ0FBQyxDQUFDLENBQUM7TUFDaEM7SUFDRixDQUFDLENBQUMsT0FBT0MsR0FBRyxFQUFFO01BQ1osSUFBSUEsR0FBRyxZQUFZQywyQkFBa0IsRUFBRTtRQUNyQyxNQUFNakIsTUFBTSxDQUFDa0IsWUFBWSxDQUFDLENBQUM7UUFDM0I7TUFDRjtNQUVBLE1BQU1GLEdBQUc7SUFDWDtJQUVBaEIsTUFBTSxDQUFDTyxRQUFRLEdBQUdELE1BQU07SUFDeEI7RUFDRjtFQUVBLElBQUlFLEtBQUs7RUFDVCxPQUFPLElBQUksRUFBRTtJQUNYLE1BQU1KLEdBQUcsR0FBR0osTUFBTSxDQUFDSyxNQUFNO0lBQ3pCLElBQUlDLE1BQU0sR0FBR04sTUFBTSxDQUFDTyxRQUFRO0lBRTVCLElBQUksSUFBQVksd0JBQVcsRUFBQ2hCLFFBQVEsQ0FBQyxFQUFFO01BQ3pCLE1BQU1pQixNQUFNLEdBQUcsTUFBTSxJQUFBQywwQkFBYSxFQUFDckIsTUFBTSxDQUFDO01BRTFDLElBQUlvQixNQUFNLEtBQUssSUFBSSxFQUFFO1FBQ25CWixLQUFLLEdBQUdZLE1BQU07TUFDaEIsQ0FBQyxNQUFNLElBQUlqQixRQUFRLENBQUNtQixJQUFJLENBQUNDLElBQUksS0FBSyxVQUFVLElBQUlwQixRQUFRLENBQUNtQixJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLLEVBQUU7UUFDNUVmLEtBQUssR0FBR2dCLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDTCxNQUFNLENBQUMsQ0FBQ00sUUFBUSxDQUFDLE1BQU0sQ0FBQztNQUNoRCxDQUFDLE1BQU0sSUFBSXZCLFFBQVEsQ0FBQ21CLElBQUksQ0FBQ0MsSUFBSSxLQUFLLFNBQVMsRUFBRTtRQUFBLElBQUFJLG1CQUFBO1FBQzNDbkIsS0FBSyxHQUFHakMsS0FBSyxDQUFDcUQsTUFBTSxDQUFDSixNQUFNLENBQUNDLE1BQU0sQ0FBQ0wsTUFBTSxDQUFDLEVBQUUsRUFBQU8sbUJBQUEsR0FBQXhCLFFBQVEsQ0FBQzBCLFNBQVMsY0FBQUYsbUJBQUEsdUJBQWxCQSxtQkFBQSxDQUFvQkcsUUFBUSxLQUFJLE1BQU0sQ0FBQztNQUNyRixDQUFDLE1BQU0sSUFBSTNCLFFBQVEsQ0FBQ21CLElBQUksQ0FBQ0MsSUFBSSxLQUFLLFdBQVcsSUFBSXBCLFFBQVEsQ0FBQ21CLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUssRUFBRTtRQUM3RWYsS0FBSyxHQUFHZ0IsTUFBTSxDQUFDQyxNQUFNLENBQUNMLE1BQU0sQ0FBQztNQUMvQjtJQUNGLENBQUMsTUFBTTtNQUNMLElBQUk7UUFDRixDQUFDO1VBQUVaLEtBQUs7VUFBRUY7UUFBTyxDQUFDLEdBQUcsSUFBQXlCLHNCQUFTLEVBQUMzQixHQUFHLEVBQUVFLE1BQU0sRUFBRUgsUUFBUSxFQUFFSCxNQUFNLENBQUNhLE9BQU8sQ0FBQztNQUN2RSxDQUFDLENBQUMsT0FBT0csR0FBRyxFQUFFO1FBQ1osSUFBSUEsR0FBRyxZQUFZQywyQkFBa0IsRUFBRTtVQUNyQyxNQUFNakIsTUFBTSxDQUFDa0IsWUFBWSxDQUFDLENBQUM7VUFDM0I7UUFDRjtRQUVBLE1BQU1GLEdBQUc7TUFDWDtNQUVBaEIsTUFBTSxDQUFDTyxRQUFRLEdBQUdELE1BQU07SUFDMUI7SUFFQTtFQUNGO0VBRUEsT0FBTyxJQUFJMEIsdUJBQWdCLENBQUM7SUFDMUI5QixZQUFZLEVBQUVBLFlBQVk7SUFDMUJELFNBQVMsRUFBRUEsU0FBUztJQUNwQkUsUUFBUSxFQUFFQSxRQUFRO0lBQ2xCSyxLQUFLLEVBQUVBO0VBQ1QsQ0FBQyxDQUFDO0FBQ0o7QUFBQyxJQUFBeUIsUUFBQSxHQUVjbEMsWUFBWTtBQUFBbUMsT0FBQSxDQUFBbEQsT0FBQSxHQUFBaUQsUUFBQTtBQUMzQkUsTUFBTSxDQUFDRCxPQUFPLEdBQUduQyxZQUFZIn0=