# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.4](https://github.com/inspect-js/is-shared-array-buffer/compare/v1.0.3...v1.0.4) - 2024-12-18

### Commits

- [types] use shared config [`2966419`](https://github.com/inspect-js/is-shared-array-buffer/commit/2966419ac786ee171a38b80e77b21becc59fd7be)
- [actions] split out node 10-20, and 20+ [`9961138`](https://github.com/inspect-js/is-shared-array-buffer/commit/9961138844e05cf66846597410110aa92dfbf5cb)
- [Dev <PERSON>] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/node`, `@types/object-inspect`, `@types/tape auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`a808a5d`](https://github.com/inspect-js/is-shared-array-buffer/commit/a808a5d53445f9d498d6d445b728fde63eac3fdb)
- [Refactor] use `call-bound` directly [`5235143`](https://github.com/inspect-js/is-shared-array-buffer/commit/5235143ef80f1c2e5f5168fd445db386a3b82224)
- [Tests] replace `aud` with `npm audit` [`954a0c7`](https://github.com/inspect-js/is-shared-array-buffer/commit/954a0c7c4922f8c034b7d7258b1f28337dc59746)
- [Tests] use `@arethetypeswrong/cli` [`0ba297a`](https://github.com/inspect-js/is-shared-array-buffer/commit/0ba297a7b243380179d982761cd96a04aa33e108)
- [Deps] update `call-bind` [`d5c5bab`](https://github.com/inspect-js/is-shared-array-buffer/commit/d5c5babb76e502c00d8982e6c551dddd648f2bcd)
- [Dev Deps] update `@types/node` [`705f5c7`](https://github.com/inspect-js/is-shared-array-buffer/commit/705f5c7dd5605fb4ac09fcb1041b3336f245bfb4)
- [Dev Deps] add missing peer dep [`4123434`](https://github.com/inspect-js/is-shared-array-buffer/commit/412343426cc6a264a5d7dcbfa673b4ca4248291d)

## [v1.0.3](https://github.com/inspect-js/is-shared-array-buffer/compare/v1.0.2...v1.0.3) - 2024-02-20

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`c4131f5`](https://github.com/inspect-js/is-shared-array-buffer/commit/c4131f568b1828c1b5d068871332712f475e6c96)
- add types [`41cb419`](https://github.com/inspect-js/is-shared-array-buffer/commit/41cb41918c2cf423938c767ffc67fd352130f6d1)
- [actions] skip ls check on node &lt; 10; remove redundant finisher [`2655b01`](https://github.com/inspect-js/is-shared-array-buffer/commit/2655b0142c06220a2f7912dc10caab31a465e9bc)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `available-typed-arrays`, `npmignore`, `object-inspect`, `tape` [`5917f9a`](https://github.com/inspect-js/is-shared-array-buffer/commit/5917f9ac45800df43d53fb77b1506c6d08e58370)
- [Tests] add tests that TypedArrays are not SABs [`823dd7a`](https://github.com/inspect-js/is-shared-array-buffer/commit/823dd7a0c933efa3abdb2e9ae5c903fe15d6b2fe)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `es-value-fixtures`, `object-inspect`, `tape` [`6701ad4`](https://github.com/inspect-js/is-shared-array-buffer/commit/6701ad425da15c6557d01744bfe68eaf56fd9ab3)
- [actions] update rebase action to use reusable workflow [`b5119f0`](https://github.com/inspect-js/is-shared-array-buffer/commit/b5119f05ececf1f0f516a38d7f444e3a7174bd43)
- [Dev Deps] update `@ljharb/eslint-config`, `aud` [`38a6d72`](https://github.com/inspect-js/is-shared-array-buffer/commit/38a6d721588e6e4db83fd24e63b8a19fd4398123)
- [meta] add missing `engines.node` [`aac97e1`](https://github.com/inspect-js/is-shared-array-buffer/commit/aac97e1839f671dbba07941c6d7c4f153918548f)
- [readme] remove dead badges [`07c452d`](https://github.com/inspect-js/is-shared-array-buffer/commit/07c452daa96c1cfe49a428b11da692facb72eb08)
- [Deps] update `call-bind` [`b8576fe`](https://github.com/inspect-js/is-shared-array-buffer/commit/b8576feb56508f3bf43905f5d23f7178c9e1af39)
- [meta] add `sideEffects` flag [`3e6730e`](https://github.com/inspect-js/is-shared-array-buffer/commit/3e6730e0ee3a47cb6d7c2ee8bc34bd61dd8f2455)

## [v1.0.2](https://github.com/inspect-js/is-shared-array-buffer/compare/v1.0.1...v1.0.2) - 2022-04-01

### Commits

- [actions] reuse common workflows [`48d01e6`](https://github.com/inspect-js/is-shared-array-buffer/commit/48d01e690f76c92f5c9072fbcb9b6215402db8a7)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`7b0e12a`](https://github.com/inspect-js/is-shared-array-buffer/commit/7b0e12a4e8f5db8eac586be68c879119a4a12e7a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`8d57a8e`](https://github.com/inspect-js/is-shared-array-buffer/commit/8d57a8e1d9ce093f04f83e196ca7c80a02617939)
- [readme] update URLs [`dca4d27`](https://github.com/inspect-js/is-shared-array-buffer/commit/dca4d27d35352309da5abb4feb584158004008cf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`2a7bb99`](https://github.com/inspect-js/is-shared-array-buffer/commit/2a7bb990610d7f6c058bdae7f21c49cc7276848f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape` [`389c6db`](https://github.com/inspect-js/is-shared-array-buffer/commit/389c6db4311a85a84fd4cb75646f26023b0c1685)
- [actions] update codecov uploader [`b9661f9`](https://github.com/inspect-js/is-shared-array-buffer/commit/b9661f9ac2e1e002372b9b1e136faca837a6647f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect` [`f99cd48`](https://github.com/inspect-js/is-shared-array-buffer/commit/f99cd4827e23bc893ed711cbffe28f3e51a4d401)
- [readme] add actions and codecov badges [`9515ed2`](https://github.com/inspect-js/is-shared-array-buffer/commit/9515ed2184a3ed1ce913b92b5092884dad5ac794)
- [Fix] add missing `call-bind` dependency [`cff5358`](https://github.com/inspect-js/is-shared-array-buffer/commit/cff53582740f9f053ec67e1acbf2bafc83bdb7b5)
- [meta] add `safe-publish-latest`; use `prepublishOnly` script for npm 7+ [`ba0b719`](https://github.com/inspect-js/is-shared-array-buffer/commit/ba0b7190a42d4290d31a5fce215e874da573dd77)

## [v1.0.1](https://github.com/inspect-js/is-shared-array-buffer/compare/v1.0.0...v1.0.1) - 2021-03-04

### Commits

- [readme] fix repo URLs [`37c38f3`](https://github.com/inspect-js/is-shared-array-buffer/commit/37c38f347392da177197dd2fd518b61240a56203)

## v1.0.0 - 2021-03-04

### Commits

- [Tests] add tests [`9c7b806`](https://github.com/inspect-js/is-shared-array-buffer/commit/9c7b806ab1528814308a7420f8198644f55c916f)
- Initial commit [`4e65c5e`](https://github.com/inspect-js/is-shared-array-buffer/commit/4e65c5ecdaa255162bc6507de4ff98cea2472e3b)
- [meta] do not publish github action workflow files [`ac3693d`](https://github.com/inspect-js/is-shared-array-buffer/commit/ac3693db8ec26db5444ef4b46aa38a81e8841d30)
- readme [`7a984d0`](https://github.com/inspect-js/is-shared-array-buffer/commit/7a984d0db73b77943f6731098134e3351a36793b)
- npm init [`a586c99`](https://github.com/inspect-js/is-shared-array-buffer/commit/a586c99316f3c8ae4fd5125621ea933e97a1bf1b)
- [actions] add automatic rebasing / merge commit blocking [`184fe62`](https://github.com/inspect-js/is-shared-array-buffer/commit/184fe622680d523e89ac322fa1a52dbba46a8fc0)
- Implementation [`207e26d`](https://github.com/inspect-js/is-shared-array-buffer/commit/207e26d1128930f28384cb213b38d69fd52bbd7c)
- [meta] create `FUNDING.yml`; add "funding" field [`3cad3fc`](https://github.com/inspect-js/is-shared-array-buffer/commit/3cad3fc9509f91fbc71e84565529f53a94d538d4)
- [meta] add auto-changelog [`31f1f2c`](https://github.com/inspect-js/is-shared-array-buffer/commit/31f1f2cbcd616d6c09089d62198d5cc775053324)
- [Tests] add `npm run lint` [`2e5146e`](https://github.com/inspect-js/is-shared-array-buffer/commit/2e5146e18f44533382a781fa09a50d4f47caa0e5)
- Only apps should have lockfiles [`7b2adfa`](https://github.com/inspect-js/is-shared-array-buffer/commit/7b2adfad6dcd95271ab6ba34658a9a1a21dbeacf)
