"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.codepageBySortId = exports.codepageByLanguageId = exports.Flags = exports.Collation = void 0;
// http://technet.microsoft.com/en-us/library/aa176553(v=sql.80).aspx
const codepageByLanguageId = {
  // Arabic_*
  [0x0401]: 'CP1256',
  // Chinese_Taiwan_Stroke_*
  // Chinese_Traditional_Stroke_Count_*
  // Chinese_Taiwan_Bopomofo_*
  // Chinese_Traditional_Bopomofo_*
  [0x0404]: 'CP950',
  // Czech_*
  [0x0405]: 'CP1250',
  // Danish_Greenlandic_*
  // Danish_Norwegian_*
  [0x0406]: 'CP1252',
  // Greek_*
  [0x0408]: 'CP1253',
  // Latin1_General_*
  [0x0409]: 'CP1252',
  // Traditional_Spanish_*
  [0x040A]: 'CP1252',
  // Finnish_Swedish_*
  [0x040B]: 'CP1252',
  // French_*
  [0x040C]: 'CP1252',
  // Hebrew_*
  [0x040D]: 'CP1255',
  // Hungarian_*
  // Hungarian_Technical_*
  [0x040E]: 'CP1250',
  // Icelandic_*
  [0x040F]: 'CP1252',
  // Japanese_*
  // Japanese_XJIS_*
  // Japanese_Unicode_*
  // Japanese_Bushu_Kakusu_*
  [0x0411]: 'CP932',
  // Korean_*
  // Korean_Wansung_*
  [0x0412]: 'CP949',
  // Norwegian_*
  [0x0414]: 'CP1252',
  // Polish_*
  [0x0415]: 'CP1250',
  // Romansh_*
  [0x0417]: 'CP1252',
  // Romanian_*
  [0x0418]: 'CP1250',
  // Cyrillic_*
  [0x0419]: 'CP1251',
  // Croatian_*
  [0x041A]: 'CP1250',
  // Slovak_*
  [0x041B]: 'CP1250',
  // Albanian_*
  [0x041C]: 'CP1250',
  // Thai_*
  [0x041E]: 'CP874',
  // Turkish_*
  [0x041F]: 'CP1254',
  // Urdu_*
  [0x0420]: 'CP1256',
  // Ukrainian_*
  [0x0422]: 'CP1251',
  // Slovenian_*
  [0x0424]: 'CP1250',
  // Estonian_*
  [0x0425]: 'CP1257',
  // Latvian_*
  [0x0426]: 'CP1257',
  // Lithuanian_*
  [0x0427]: 'CP1257',
  // Persian_*
  [0x0429]: 'CP1256',
  // Vietnamese_*
  [0x042A]: 'CP1258',
  // Azeri_Latin_*
  [0x042C]: 'CP1254',
  // Upper_Sorbian_*
  [0x042E]: 'CP1252',
  // Macedonian_FYROM_*
  [0x042F]: 'CP1251',
  // Sami_Norway_*
  [0x043B]: 'CP1252',
  // Kazakh_*
  [0x043F]: 'CP1251',
  // Turkmen_*
  [0x0442]: 'CP1250',
  // Uzbek_Latin_*
  [0x0443]: 'CP1254',
  // Tatar_*
  [0x0444]: 'CP1251',
  // Welsh_*
  [0x0452]: 'CP1252',
  // Frisian_*
  [0x0462]: 'CP1252',
  // Bashkir_*
  [0x046D]: 'CP1251',
  // Mapudungan_*
  [0x047A]: 'CP1252',
  // Mohawk_*
  [0x047C]: 'CP1252',
  // Breton_*
  [0x047E]: 'CP1252',
  // Uighur_*
  [0x0480]: 'CP1256',
  // Corsican_*
  [0x0483]: 'CP1252',
  // Yakut_*
  [0x0485]: 'CP1251',
  // Dari_*
  [0x048C]: 'CP1256',
  // Chinese_PRC_*
  // Chinese_Simplified_Pinyin_*
  // Chinese_PRC_Stroke_*
  // Chinese_Simplified_Stroke_Order_*
  [0x0804]: 'CP936',
  // Serbian_Latin_*
  [0x081A]: 'CP1250',
  // Azeri_Cyrillic_*
  [0x082C]: 'CP1251',
  // Sami_Sweden_Finland_*
  [0x083B]: 'CP1252',
  // Tamazight_*
  [0x085F]: 'CP1252',
  // Chinese_Hong_Kong_Stroke_*
  [0x0C04]: 'CP950',
  // Modern_Spanish_*
  [0x0C0A]: 'CP1252',
  // Serbian_Cyrillic_*
  [0x0C1A]: 'CP1251',
  // Chinese_Traditional_Pinyin_*
  // Chinese_Traditional_Stroke_Order_*
  [0x1404]: 'CP950',
  // Bosnian_Latin_*
  [0x141A]: 'CP1250',
  // Bosnian_Cyrillic_*
  [0x201A]: 'CP1251',
  // German
  // German_PhoneBook_*
  [0x0407]: 'CP1252',
  // Georgian_Modern_Sort_*
  [0x0437]: 'CP1252'
};
exports.codepageByLanguageId = codepageByLanguageId;
const codepageBySortId = {
  [30]: 'CP437',
  // SQL_Latin1_General_CP437_BIN
  [31]: 'CP437',
  // SQL_Latin1_General_CP437_CS_AS
  [32]: 'CP437',
  // SQL_Latin1_General_CP437_CI_AS
  [33]: 'CP437',
  // SQL_Latin1_General_Pref_CP437_CI_AS
  [34]: 'CP437',
  // SQL_Latin1_General_CP437_CI_AI
  [40]: 'CP850',
  // SQL_Latin1_General_CP850_BIN
  [41]: 'CP850',
  // SQL_Latin1_General_CP850_CS_AS
  [42]: 'CP850',
  // SQL_Latin1_General_CP850_CI_AS
  [43]: 'CP850',
  // SQL_Latin1_General_Pref_CP850_CI_AS
  [44]: 'CP850',
  // SQL_Latin1_General_CP850_CI_AI
  [49]: 'CP850',
  // SQL_1xCompat_CP850_CI_AS
  [51]: 'CP1252',
  // SQL_Latin1_General_Cp1_CS_AS_KI_WI
  [52]: 'CP1252',
  // SQL_Latin1_General_Cp1_CI_AS_KI_WI
  [53]: 'CP1252',
  // SQL_Latin1_General_Pref_Cp1_CI_AS_KI_WI
  [54]: 'CP1252',
  // SQL_Latin1_General_Cp1_CI_AI_KI_WI
  [55]: 'CP850',
  // SQL_AltDiction_CP850_CS_AS
  [56]: 'CP850',
  // SQL_AltDiction_Pref_CP850_CI_AS
  [57]: 'CP850',
  // SQL_AltDiction_CP850_CI_AI
  [58]: 'CP850',
  // SQL_Scandinavian_Pref_CP850_CI_AS
  [59]: 'CP850',
  // SQL_Scandinavian_CP850_CS_AS
  [60]: 'CP850',
  // SQL_Scandinavian_CP850_CI_AS
  [61]: 'CP850',
  // SQL_AltDiction_CP850_CI_AS
  [80]: 'CP1250',
  // SQL_Latin1_General_1250_BIN
  [81]: 'CP1250',
  // SQL_Latin1_General_CP1250_CS_AS
  [82]: 'CP1250',
  // SQL_Latin1_General_Cp1250_CI_AS_KI_WI
  [83]: 'CP1250',
  // SQL_Czech_Cp1250_CS_AS_KI_WI
  [84]: 'CP1250',
  // SQL_Czech_Cp1250_CI_AS_KI_WI
  [85]: 'CP1250',
  // SQL_Hungarian_Cp1250_CS_AS_KI_WI
  [86]: 'CP1250',
  // SQL_Hungarian_Cp1250_CI_AS_KI_WI
  [87]: 'CP1250',
  // SQL_Polish_Cp1250_CS_AS_KI_WI
  [88]: 'CP1250',
  // SQL_Polish_Cp1250_CI_AS_KI_WI
  [89]: 'CP1250',
  // SQL_Romanian_Cp1250_CS_AS_KI_WI
  [90]: 'CP1250',
  // SQL_Romanian_Cp1250_CI_AS_KI_WI
  [91]: 'CP1250',
  // SQL_Croatian_Cp1250_CS_AS_KI_WI
  [92]: 'CP1250',
  // SQL_Croatian_Cp1250_CI_AS_KI_WI
  [93]: 'CP1250',
  // SQL_Slovak_Cp1250_CS_AS_KI_WI
  [94]: 'CP1250',
  // SQL_Slovak_Cp1250_CI_AS_KI_WI
  [95]: 'CP1250',
  // SQL_Slovenian_Cp1250_CS_AS_KI_WI
  [96]: 'CP1250',
  // SQL_Slovenian_Cp1250_CI_AS_KI_WI
  [104]: 'CP1251',
  // SQL_Latin1_General_1251_BIN
  [105]: 'CP1251',
  // SQL_Latin1_General_CP1251_CS_AS
  [106]: 'CP1251',
  // SQL_Latin1_General_CP1251_CI_AS
  [107]: 'CP1251',
  // SQL_Ukrainian_Cp1251_CS_AS_KI_WI
  [108]: 'CP1251',
  // SQL_Ukrainian_Cp1251_CI_AS_KI_WI
  [112]: 'CP1253',
  // SQL_Latin1_General_1253_BIN
  [113]: 'CP1253',
  // SQL_Latin1_General_CP1253_CS_AS
  [114]: 'CP1253',
  // SQL_Latin1_General_CP1253_CI_AS
  [120]: 'CP1253',
  // SQL_MixDiction_CP1253_CS_AS
  [121]: 'CP1253',
  // SQL_AltDiction_CP1253_CS_AS
  [122]: 'CP1253',
  // SQL_AltDiction2_CP1253_CS_AS
  [124]: 'CP1253',
  // SQL_Latin1_General_CP1253_CI_AI
  [128]: 'CP1254',
  // SQL_Latin1_General_1254_BIN
  [129]: 'CP1254',
  // SQL_Latin1_General_Cp1254_CS_AS_KI_WI
  [130]: 'CP1254',
  // SQL_Latin1_General_Cp1254_CI_AS_KI_WI
  [136]: 'CP1255',
  // SQL_Latin1_General_1255_BIN
  [137]: 'CP1255',
  // SQL_Latin1_General_CP1255_CS_AS
  [138]: 'CP1255',
  // SQL_Latin1_General_CP1255_CI_AS
  [144]: 'CP1256',
  // SQL_Latin1_General_1256_BIN
  [145]: 'CP1256',
  // SQL_Latin1_General_CP1256_CS_AS
  [146]: 'CP1256',
  // SQL_Latin1_General_CP1256_CI_AS
  [152]: 'CP1257',
  // SQL_Latin1_General_1257_BIN
  [153]: 'CP1257',
  // SQL_Latin1_General_CP1257_CS_AS
  [154]: 'CP1257',
  // SQL_Latin1_General_CP1257_CI_AS
  [155]: 'CP1257',
  // SQL_Estonian_Cp1257_CS_AS_KI_WI
  [156]: 'CP1257',
  // SQL_Estonian_Cp1257_CI_AS_KI_WI
  [157]: 'CP1257',
  // SQL_Latvian_Cp1257_CS_AS_KI_WI
  [158]: 'CP1257',
  // SQL_Latvian_Cp1257_CI_AS_KI_WI
  [159]: 'CP1257',
  // SQL_Lithuanian_Cp1257_CS_AS_KI_WI
  [160]: 'CP1257',
  // SQL_Lithuanian_Cp1257_CI_AS_KI_WI
  [183]: 'CP1252',
  // SQL_Danish_Pref_Cp1_CI_AS_KI_WI
  [184]: 'CP1252',
  // SQL_SwedishPhone_Pref_Cp1_CI_AS_KI_WI
  [185]: 'CP1252',
  // SQL_SwedishStd_Pref_Cp1_CI_AS_KI_WI
  [186]: 'CP1252' // SQL_Icelandic_Pref_Cp1_CI_AS_KI_WI
};
exports.codepageBySortId = codepageBySortId;
const Flags = {
  IGNORE_CASE: 1 << 0,
  IGNORE_ACCENT: 1 << 1,
  IGNORE_KANA: 1 << 2,
  IGNORE_WIDTH: 1 << 3,
  BINARY: 1 << 4,
  BINARY2: 1 << 5,
  UTF8: 1 << 6
};
exports.Flags = Flags;
class Collation {
  static fromBuffer(buffer, offset = 0) {
    let lcid = (buffer[offset + 2] & 0x0F) << 16;
    lcid |= buffer[offset + 1] << 8;
    lcid |= buffer[offset + 0];
    let flags = (buffer[offset + 3] & 0x0F) << 4;
    flags |= (buffer[offset + 2] & 0xF0) >>> 4;
    const version = (buffer[offset + 3] & 0xF0) >>> 4;
    const sortId = buffer[offset + 4];
    return new this(lcid, flags, version, sortId);
  }
  constructor(lcid, flags, version, sortId) {
    this.buffer = undefined;
    this.lcid = lcid;
    this.flags = flags;
    this.version = version;
    this.sortId = sortId;
    if (this.flags & Flags.UTF8) {
      this.codepage = 'utf-8';
    } else if (this.sortId) {
      this.codepage = codepageBySortId[this.sortId];
    } else {
      // The last 16 bits of the LCID are the language id.
      // The first 4 bits define additional sort orders.
      const languageId = this.lcid & 0xFFFF;
      this.codepage = codepageByLanguageId[languageId];
    }
  }
  toBuffer() {
    if (this.buffer) {
      return this.buffer;
    }
    this.buffer = Buffer.alloc(5);
    this.buffer[0] = this.lcid & 0xFF;
    this.buffer[1] = this.lcid >>> 8 & 0xFF;
    this.buffer[2] = this.lcid >>> 16 & 0x0F | (this.flags & 0x0F) << 4;
    this.buffer[3] = (this.flags & 0xF0) >>> 4 | (this.version & 0x0F) << 4;
    this.buffer[4] = this.sortId & 0xFF;
    return this.buffer;
  }
}
exports.Collation = Collation;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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