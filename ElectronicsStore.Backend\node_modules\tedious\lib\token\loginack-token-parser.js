"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var _tdsVersions = require("../tds-versions");
var _helpers = require("./helpers");
const interfaceTypes = {
  0: 'SQL_DFLT',
  1: 'SQL_TSQL'
};
function loginAckParser(buf, offset, _options) {
  // length
  let tokenLength;
  ({
    offset,
    value: tokenLength
  } = (0, _helpers.readUInt16LE)(buf, offset));
  if (buf.length < tokenLength + offset) {
    throw new _helpers.NotEnoughDataError(tokenLength + offset);
  }
  let interfaceNumber;
  ({
    offset,
    value: interfaceNumber
  } = (0, _helpers.readUInt8)(buf, offset));
  const interfaceType = interfaceTypes[interfaceNumber];
  let tdsVersionNumber;
  ({
    offset,
    value: tdsVersionNumber
  } = (0, _helpers.readUInt32BE)(buf, offset));
  const tdsVersion = _tdsVersions.versionsByValue[tdsVersionNumber];
  let progName;
  ({
    offset,
    value: progName
  } = (0, _helpers.readBVarChar)(buf, offset));
  let major;
  ({
    offset,
    value: major
  } = (0, _helpers.readUInt8)(buf, offset));
  let minor;
  ({
    offset,
    value: minor
  } = (0, _helpers.readUInt8)(buf, offset));
  let buildNumHi;
  ({
    offset,
    value: buildNumHi
  } = (0, _helpers.readUInt8)(buf, offset));
  let buildNumLow;
  ({
    offset,
    value: buildNumLow
  } = (0, _helpers.readUInt8)(buf, offset));
  return new _helpers.Result(new _token.LoginAckToken({
    interface: interfaceType,
    tdsVersion: tdsVersion,
    progName: progName,
    progVersion: {
      major: major,
      minor: minor,
      buildNumHi: buildNumHi,
      buildNumLow: buildNumLow
    }
  }), offset);
}
var _default = loginAckParser;
exports.default = _default;
module.exports = loginAckParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfdG9rZW4iLCJyZXF1aXJlIiwiX3Rkc1ZlcnNpb25zIiwiX2hlbHBlcnMiLCJpbnRlcmZhY2VUeXBlcyIsImxvZ2luQWNrUGFyc2VyIiwiYnVmIiwib2Zmc2V0IiwiX29wdGlvbnMiLCJ0b2tlbkxlbmd0aCIsInZhbHVlIiwicmVhZFVJbnQxNkxFIiwibGVuZ3RoIiwiTm90RW5vdWdoRGF0YUVycm9yIiwiaW50ZXJmYWNlTnVtYmVyIiwicmVhZFVJbnQ4IiwiaW50ZXJmYWNlVHlwZSIsInRkc1ZlcnNpb25OdW1iZXIiLCJyZWFkVUludDMyQkUiLCJ0ZHNWZXJzaW9uIiwidmVyc2lvbnMiLCJwcm9nTmFtZSIsInJlYWRCVmFyQ2hhciIsIm1ham9yIiwibWlub3IiLCJidWlsZE51bUhpIiwiYnVpbGROdW1Mb3ciLCJSZXN1bHQiLCJMb2dpbkFja1Rva2VuIiwiaW50ZXJmYWNlIiwicHJvZ1ZlcnNpb24iLCJfZGVmYXVsdCIsImV4cG9ydHMiLCJkZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiLi4vLi4vc3JjL3Rva2VuL2xvZ2luYWNrLXRva2VuLXBhcnNlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIFBhcnNlck9wdGlvbnMgfSBmcm9tICcuL3N0cmVhbS1wYXJzZXInO1xuXG5pbXBvcnQgeyBMb2dpbkFja1Rva2VuIH0gZnJvbSAnLi90b2tlbic7XG5cbmltcG9ydCB7IHZlcnNpb25zQnlWYWx1ZSBhcyB2ZXJzaW9ucyB9IGZyb20gJy4uL3Rkcy12ZXJzaW9ucyc7XG5pbXBvcnQgeyBOb3RFbm91Z2hEYXRhRXJyb3IsIHJlYWRCVmFyQ2hhciwgcmVhZFVJbnQxNkxFLCByZWFkVUludDMyQkUsIHJlYWRVSW50OCwgUmVzdWx0IH0gZnJvbSAnLi9oZWxwZXJzJztcblxuY29uc3QgaW50ZXJmYWNlVHlwZXM6IHsgW2tleTogbnVtYmVyXTogc3RyaW5nIH0gPSB7XG4gIDA6ICdTUUxfREZMVCcsXG4gIDE6ICdTUUxfVFNRTCdcbn07XG5cbmZ1bmN0aW9uIGxvZ2luQWNrUGFyc2VyKGJ1ZjogQnVmZmVyLCBvZmZzZXQ6IG51bWJlciwgX29wdGlvbnM6IFBhcnNlck9wdGlvbnMpOiBSZXN1bHQ8TG9naW5BY2tUb2tlbj4ge1xuICAvLyBsZW5ndGhcbiAgbGV0IHRva2VuTGVuZ3RoO1xuICAoeyBvZmZzZXQsIHZhbHVlOiB0b2tlbkxlbmd0aCB9ID0gcmVhZFVJbnQxNkxFKGJ1Ziwgb2Zmc2V0KSk7XG5cbiAgaWYgKGJ1Zi5sZW5ndGggPCB0b2tlbkxlbmd0aCArIG9mZnNldCkge1xuICAgIHRocm93IG5ldyBOb3RFbm91Z2hEYXRhRXJyb3IodG9rZW5MZW5ndGggKyBvZmZzZXQpO1xuICB9XG5cbiAgbGV0IGludGVyZmFjZU51bWJlcjtcbiAgKHsgb2Zmc2V0LCB2YWx1ZTogaW50ZXJmYWNlTnVtYmVyIH0gPSByZWFkVUludDgoYnVmLCBvZmZzZXQpKTtcblxuICBjb25zdCBpbnRlcmZhY2VUeXBlID0gaW50ZXJmYWNlVHlwZXNbaW50ZXJmYWNlTnVtYmVyXTtcblxuICBsZXQgdGRzVmVyc2lvbk51bWJlcjtcbiAgKHsgb2Zmc2V0LCB2YWx1ZTogdGRzVmVyc2lvbk51bWJlciB9ID0gcmVhZFVJbnQzMkJFKGJ1Ziwgb2Zmc2V0KSk7XG5cbiAgY29uc3QgdGRzVmVyc2lvbiA9IHZlcnNpb25zW3Rkc1ZlcnNpb25OdW1iZXJdO1xuXG4gIGxldCBwcm9nTmFtZTtcbiAgKHsgb2Zmc2V0LCB2YWx1ZTogcHJvZ05hbWUgfSA9IHJlYWRCVmFyQ2hhcihidWYsIG9mZnNldCkpO1xuXG4gIGxldCBtYWpvcjtcbiAgKHsgb2Zmc2V0LCB2YWx1ZTogbWFqb3IgfSA9IHJlYWRVSW50OChidWYsIG9mZnNldCkpO1xuXG4gIGxldCBtaW5vcjtcbiAgKHsgb2Zmc2V0LCB2YWx1ZTogbWlub3IgfSA9IHJlYWRVSW50OChidWYsIG9mZnNldCkpO1xuXG4gIGxldCBidWlsZE51bUhpO1xuICAoeyBvZmZzZXQsIHZhbHVlOiBidWlsZE51bUhpIH0gPSByZWFkVUludDgoYnVmLCBvZmZzZXQpKTtcblxuICBsZXQgYnVpbGROdW1Mb3c7XG4gICh7IG9mZnNldCwgdmFsdWU6IGJ1aWxkTnVtTG93IH0gPSByZWFkVUludDgoYnVmLCBvZmZzZXQpKTtcblxuICByZXR1cm4gbmV3IFJlc3VsdChuZXcgTG9naW5BY2tUb2tlbih7XG4gICAgaW50ZXJmYWNlOiBpbnRlcmZhY2VUeXBlLFxuICAgIHRkc1ZlcnNpb246IHRkc1ZlcnNpb24sXG4gICAgcHJvZ05hbWU6IHByb2dOYW1lLFxuICAgIHByb2dWZXJzaW9uOiB7XG4gICAgICBtYWpvcjogbWFqb3IsXG4gICAgICBtaW5vcjogbWlub3IsXG4gICAgICBidWlsZE51bUhpOiBidWlsZE51bUhpLFxuICAgICAgYnVpbGROdW1Mb3c6IGJ1aWxkTnVtTG93XG4gICAgfVxuICB9KSwgb2Zmc2V0KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbG9naW5BY2tQYXJzZXI7XG5tb2R1bGUuZXhwb3J0cyA9IGxvZ2luQWNrUGFyc2VyO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxJQUFBQSxNQUFBLEdBQUFDLE9BQUE7QUFFQSxJQUFBQyxZQUFBLEdBQUFELE9BQUE7QUFDQSxJQUFBRSxRQUFBLEdBQUFGLE9BQUE7QUFFQSxNQUFNRyxjQUF5QyxHQUFHO0VBQ2hELENBQUMsRUFBRSxVQUFVO0VBQ2IsQ0FBQyxFQUFFO0FBQ0wsQ0FBQztBQUVELFNBQVNDLGNBQWNBLENBQUNDLEdBQVcsRUFBRUMsTUFBYyxFQUFFQyxRQUF1QixFQUF5QjtFQUNuRztFQUNBLElBQUlDLFdBQVc7RUFDZixDQUFDO0lBQUVGLE1BQU07SUFBRUcsS0FBSyxFQUFFRDtFQUFZLENBQUMsR0FBRyxJQUFBRSxxQkFBWSxFQUFDTCxHQUFHLEVBQUVDLE1BQU0sQ0FBQztFQUUzRCxJQUFJRCxHQUFHLENBQUNNLE1BQU0sR0FBR0gsV0FBVyxHQUFHRixNQUFNLEVBQUU7SUFDckMsTUFBTSxJQUFJTSwyQkFBa0IsQ0FBQ0osV0FBVyxHQUFHRixNQUFNLENBQUM7RUFDcEQ7RUFFQSxJQUFJTyxlQUFlO0VBQ25CLENBQUM7SUFBRVAsTUFBTTtJQUFFRyxLQUFLLEVBQUVJO0VBQWdCLENBQUMsR0FBRyxJQUFBQyxrQkFBUyxFQUFDVCxHQUFHLEVBQUVDLE1BQU0sQ0FBQztFQUU1RCxNQUFNUyxhQUFhLEdBQUdaLGNBQWMsQ0FBQ1UsZUFBZSxDQUFDO0VBRXJELElBQUlHLGdCQUFnQjtFQUNwQixDQUFDO0lBQUVWLE1BQU07SUFBRUcsS0FBSyxFQUFFTztFQUFpQixDQUFDLEdBQUcsSUFBQUMscUJBQVksRUFBQ1osR0FBRyxFQUFFQyxNQUFNLENBQUM7RUFFaEUsTUFBTVksVUFBVSxHQUFHQyw0QkFBUSxDQUFDSCxnQkFBZ0IsQ0FBQztFQUU3QyxJQUFJSSxRQUFRO0VBQ1osQ0FBQztJQUFFZCxNQUFNO0lBQUVHLEtBQUssRUFBRVc7RUFBUyxDQUFDLEdBQUcsSUFBQUMscUJBQVksRUFBQ2hCLEdBQUcsRUFBRUMsTUFBTSxDQUFDO0VBRXhELElBQUlnQixLQUFLO0VBQ1QsQ0FBQztJQUFFaEIsTUFBTTtJQUFFRyxLQUFLLEVBQUVhO0VBQU0sQ0FBQyxHQUFHLElBQUFSLGtCQUFTLEVBQUNULEdBQUcsRUFBRUMsTUFBTSxDQUFDO0VBRWxELElBQUlpQixLQUFLO0VBQ1QsQ0FBQztJQUFFakIsTUFBTTtJQUFFRyxLQUFLLEVBQUVjO0VBQU0sQ0FBQyxHQUFHLElBQUFULGtCQUFTLEVBQUNULEdBQUcsRUFBRUMsTUFBTSxDQUFDO0VBRWxELElBQUlrQixVQUFVO0VBQ2QsQ0FBQztJQUFFbEIsTUFBTTtJQUFFRyxLQUFLLEVBQUVlO0VBQVcsQ0FBQyxHQUFHLElBQUFWLGtCQUFTLEVBQUNULEdBQUcsRUFBRUMsTUFBTSxDQUFDO0VBRXZELElBQUltQixXQUFXO0VBQ2YsQ0FBQztJQUFFbkIsTUFBTTtJQUFFRyxLQUFLLEVBQUVnQjtFQUFZLENBQUMsR0FBRyxJQUFBWCxrQkFBUyxFQUFDVCxHQUFHLEVBQUVDLE1BQU0sQ0FBQztFQUV4RCxPQUFPLElBQUlvQixlQUFNLENBQUMsSUFBSUMsb0JBQWEsQ0FBQztJQUNsQ0MsU0FBUyxFQUFFYixhQUFhO0lBQ3hCRyxVQUFVLEVBQUVBLFVBQVU7SUFDdEJFLFFBQVEsRUFBRUEsUUFBUTtJQUNsQlMsV0FBVyxFQUFFO01BQ1hQLEtBQUssRUFBRUEsS0FBSztNQUNaQyxLQUFLLEVBQUVBLEtBQUs7TUFDWkMsVUFBVSxFQUFFQSxVQUFVO01BQ3RCQyxXQUFXLEVBQUVBO0lBQ2Y7RUFDRixDQUFDLENBQUMsRUFBRW5CLE1BQU0sQ0FBQztBQUNiO0FBQUMsSUFBQXdCLFFBQUEsR0FFYzFCLGNBQWM7QUFBQTJCLE9BQUEsQ0FBQUMsT0FBQSxHQUFBRixRQUFBO0FBQzdCRyxNQUFNLENBQUNGLE9BQU8sR0FBRzNCLGNBQWMifQ==