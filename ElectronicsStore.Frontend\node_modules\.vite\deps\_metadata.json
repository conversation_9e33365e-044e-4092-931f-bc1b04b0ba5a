{"hash": "f3656cff", "configHash": "7ac6bd62", "lockfileHash": "4f4da663", "browserHash": "457d89ff", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c405597d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b809164a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "014304eb", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b477736c", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ae51a2bd", "needsInterop": true}, "@mui/material/styles": {"src": "../../@mui/material/styles/index.js", "file": "@mui_material_styles.js", "fileHash": "8dd39120", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2631a03c", "needsInterop": false}, "@mui/material/locale": {"src": "../../@mui/material/locale/index.js", "file": "@mui_material_locale.js", "fileHash": "701ac7f2", "needsInterop": false}, "@mui/material": {"src": "../../@mui/material/index.js", "file": "@mui_material.js", "fileHash": "e9a67715", "needsInterop": false}, "@mui/icons-material": {"src": "../../@mui/icons-material/esm/index.js", "file": "@mui_icons-material.js", "fileHash": "e5fa0747", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "f2ffebef", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "bcb06185", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "667cb138", "needsInterop": false}, "stylis-plugin-rtl": {"src": "../../stylis-plugin-rtl/dist/stylis-rtl.js", "file": "stylis-plugin-rtl.js", "fileHash": "c0002705", "needsInterop": false}, "@emotion/cache": {"src": "../../@emotion/cache/dist/emotion-cache.browser.development.esm.js", "file": "@emotion_cache.js", "fileHash": "db2a1e98", "needsInterop": false}, "@emotion/react": {"src": "../../@emotion/react/dist/emotion-react.browser.development.esm.js", "file": "@emotion_react.js", "fileHash": "803c3daf", "needsInterop": false}}, "chunks": {"chunk-SC2MFI6L": {"file": "chunk-SC2MFI6L.js"}, "chunk-FWWIZNCT": {"file": "chunk-FWWIZNCT.js"}, "chunk-UPELNCPK": {"file": "chunk-UPELNCPK.js"}, "chunk-YDBUMCZN": {"file": "chunk-YDBUMCZN.js"}, "chunk-CH4NBI7V": {"file": "chunk-CH4NBI7V.js"}, "chunk-PGYJAMXC": {"file": "chunk-PGYJAMXC.js"}, "chunk-DDSKKFML": {"file": "chunk-DDSKKFML.js"}, "chunk-R6Y3Z3FF": {"file": "chunk-R6Y3Z3FF.js"}, "chunk-NIGXDIBO": {"file": "chunk-NIGXDIBO.js"}, "chunk-WKPQ4ZTV": {"file": "chunk-WKPQ4ZTV.js"}, "chunk-BG45W2ER": {"file": "chunk-BG45W2ER.js"}, "chunk-HXA6O6EE": {"file": "chunk-HXA6O6EE.js"}}}