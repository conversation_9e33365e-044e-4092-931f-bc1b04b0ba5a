"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var iconv = _interopRequireWildcard(require("iconv-lite"));
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// s2.2.7.13 (introduced in TDS 7.3.B)

async function nbcRowParser(parser) {
  const colMetadata = parser.colMetadata;
  const columns = [];
  const bitmap = [];
  const bitmapByteLength = Math.ceil(colMetadata.length / 8);
  while (parser.buffer.length - parser.position < bitmapByteLength) {
    await parser.waitForChunk();
  }
  const bytes = parser.buffer.slice(parser.position, parser.position + bitmapByteLength);
  parser.position += bitmapByteLength;
  for (let i = 0, len = bytes.length; i < len; i++) {
    const byte = bytes[i];
    bitmap.push(byte & 0b1 ? true : false);
    bitmap.push(byte & 0b10 ? true : false);
    bitmap.push(byte & 0b100 ? true : false);
    bitmap.push(byte & 0b1000 ? true : false);
    bitmap.push(byte & 0b10000 ? true : false);
    bitmap.push(byte & 0b100000 ? true : false);
    bitmap.push(byte & 0b1000000 ? true : false);
    bitmap.push(byte & 0b10000000 ? true : false);
  }
  for (let i = 0; i < colMetadata.length; i++) {
    const metadata = colMetadata[i];
    if (bitmap[i]) {
      columns.push({
        value: null,
        metadata
      });
      continue;
    }
    while (true) {
      if ((0, _valueParser.isPLPStream)(metadata)) {
        const chunks = await (0, _valueParser.readPLPStream)(parser);
        if (chunks === null) {
          columns.push({
            value: chunks,
            metadata
          });
        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
          columns.push({
            value: Buffer.concat(chunks).toString('ucs2'),
            metadata
          });
        } else if (metadata.type.name === 'VarChar') {
          var _metadata$collation;
          columns.push({
            value: iconv.decode(Buffer.concat(chunks), ((_metadata$collation = metadata.collation) === null || _metadata$collation === void 0 ? void 0 : _metadata$collation.codepage) ?? 'utf8'),
            metadata
          });
        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
          columns.push({
            value: Buffer.concat(chunks),
            metadata
          });
        }
      } else {
        let result;
        try {
          result = (0, _valueParser.readValue)(parser.buffer, parser.position, metadata, parser.options);
        } catch (err) {
          if (err instanceof _helpers.NotEnoughDataError) {
            await parser.waitForChunk();
            continue;
          }
          throw err;
        }
        parser.position = result.offset;
        columns.push({
          value: result.value,
          metadata
        });
      }
      break;
    }
  }
  if (parser.options.useColumnNames) {
    const columnsMap = Object.create(null);
    columns.forEach(column => {
      const colName = column.metadata.colName;
      if (columnsMap[colName] == null) {
        columnsMap[colName] = column;
      }
    });
    return new _token.NBCRowToken(columnsMap);
  } else {
    return new _token.NBCRowToken(columns);
  }
}
var _default = nbcRowParser;
exports.default = _default;
module.exports = nbcRowParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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