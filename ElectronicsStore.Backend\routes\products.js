const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const ProductController = require('../controllers/ProductController');

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/products');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'product-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Initialize controller
const productController = new ProductController();

// Routes
router.get('/', (req, res) => productController.getAllProducts(req, res));
router.get('/search', (req, res) => productController.searchProducts(req, res));
router.get('/statistics', (req, res) => productController.getProductStatistics(req, res));
router.get('/reports/low-stock', (req, res) => productController.getLowStockProducts(req, res));
router.get('/:id', (req, res) => productController.getProductById(req, res));
router.post('/', upload.single('image'), (req, res) => productController.createProduct(req, res));
router.put('/:id', upload.single('image'), (req, res) => productController.updateProduct(req, res));
router.delete('/:id', (req, res) => productController.deleteProduct(req, res));

module.exports = router;
  try {
    const { id } = req.params;
    const product = await getRow('SELECT * FROM products WHERE id = ?', [id]);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتج'
    });
  }
});

// POST /api/products - Create new product
router.post('/', upload.single('image'), async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      cost,
      stock,
      min_stock,
      category,
      brand,
      barcode,
      status
    } = req.body;
    
    // Validation
    if (!name || !price || !cost || !category || !brand || !barcode) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة مفقودة'
      });
    }
    
    // Check if barcode already exists
    const existingProduct = await getRow('SELECT id FROM products WHERE barcode = ?', [barcode]);
    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'الباركود موجود مسبقاً'
      });
    }
    
    const imagePath = req.file ? `/uploads/products/${req.file.filename}` : null;
    
    const result = await runQuery(`
      INSERT INTO products (
        name, description, price, cost, stock, min_stock,
        category, brand, barcode, image, status, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [
      name, description, parseFloat(price), parseFloat(cost),
      parseInt(stock), parseInt(min_stock), category, brand,
      barcode, imagePath, status || 'active'
    ]);
    
    const newProduct = await getRow('SELECT * FROM products WHERE id = ?', [result.id]);
    
    res.status(201).json({
      success: true,
      message: 'تم إضافة المنتج بنجاح',
      data: newProduct
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة المنتج'
    });
  }
});

// PUT /api/products/:id - Update product
router.put('/:id', upload.single('image'), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      price,
      cost,
      stock,
      min_stock,
      category,
      brand,
      barcode,
      status
    } = req.body;
    
    // Check if product exists
    const existingProduct = await getRow('SELECT * FROM products WHERE id = ?', [id]);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    // Check if barcode is unique (excluding current product)
    const duplicateBarcode = await getRow(
      'SELECT id FROM products WHERE barcode = ? AND id != ?',
      [barcode, id]
    );
    if (duplicateBarcode) {
      return res.status(400).json({
        success: false,
        message: 'الباركود موجود مسبقاً'
      });
    }
    
    let imagePath = existingProduct.image;
    if (req.file) {
      // Delete old image if exists
      if (existingProduct.image) {
        const oldImagePath = path.join(__dirname, '..', existingProduct.image);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      imagePath = `/uploads/products/${req.file.filename}`;
    }
    
    await runQuery(`
      UPDATE products SET
        name = ?, description = ?, price = ?, cost = ?, stock = ?,
        min_stock = ?, category = ?, brand = ?, barcode = ?,
        image = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      name, description, parseFloat(price), parseFloat(cost),
      parseInt(stock), parseInt(min_stock), category, brand,
      barcode, imagePath, status, id
    ]);
    
    const updatedProduct = await getRow('SELECT * FROM products WHERE id = ?', [id]);
    
    res.json({
      success: true,
      message: 'تم تحديث المنتج بنجاح',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المنتج'
    });
  }
});

// DELETE /api/products/:id - Delete product
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if product exists
    const product = await getRow('SELECT * FROM products WHERE id = ?', [id]);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }
    
    // Delete image file if exists
    if (product.image) {
      const imagePath = path.join(__dirname, '..', product.image);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    await runQuery('DELETE FROM products WHERE id = ?', [id]);
    
    res.json({
      success: true,
      message: 'تم حذف المنتج بنجاح'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف المنتج'
    });
  }
});

// GET /api/products/reports/low-stock - Get low stock products
router.get('/reports/low-stock', async (req, res) => {
  try {
    const products = await getAllRows(`
      SELECT * FROM products 
      WHERE stock <= min_stock AND status = 'active'
      ORDER BY stock ASC
    `);
    
    res.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتجات منخفضة المخزون'
    });
  }
});

module.exports = router;
