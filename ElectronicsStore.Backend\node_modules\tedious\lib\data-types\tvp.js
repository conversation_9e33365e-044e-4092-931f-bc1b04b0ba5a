"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const TVP_ROW_TOKEN = Buffer.from([0x01]);
const TVP_END_TOKEN = Buffer.from([0x00]);
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const TVP = {
  id: 0xF3,
  type: 'TVPTYPE',
  name: 'TVP',
  declaration: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.
    return value.name + ' readonly';
  },
  generateTypeInfo(parameter) {
    var _parameter$value, _parameter$value2;
    const databaseName = '';
    const schema = ((_parameter$value = parameter.value) === null || _parameter$value === void 0 ? void 0 : _parameter$value.schema) ?? '';
    const typeName = ((_parameter$value2 = parameter.value) === null || _parameter$value2 === void 0 ? void 0 : _parameter$value2.name) ?? '';
    const bufferLength = 1 + 1 + Buffer.byteLength(databaseName, 'ucs2') + 1 + Buffer.byteLength(schema, 'ucs2') + 1 + Buffer.byteLength(typeName, 'ucs2');
    const buffer = new _writableTrackingBuffer.default(bufferLength, 'ucs2');
    buffer.writeUInt8(this.id);
    buffer.writeBVarchar(databaseName);
    buffer.writeBVarchar(schema);
    buffer.writeBVarchar(typeName);
    return buffer.data;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const {
      columns
    } = parameter.value;
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16LE(columns.length, 0);
    return buffer;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      yield TVP_END_TOKEN;
      yield TVP_END_TOKEN;
      return;
    }
    const {
      columns,
      rows
    } = parameter.value;
    for (let i = 0, len = columns.length; i < len; i++) {
      const column = columns[i];
      const buff = Buffer.alloc(6);
      // UserType
      buff.writeUInt32LE(0x00000000, 0);

      // Flags
      buff.writeUInt16LE(0x0000, 4);
      yield buff;

      // TYPE_INFO
      yield column.type.generateTypeInfo(column);

      // ColName
      yield Buffer.from([0x00]);
    }
    yield TVP_END_TOKEN;
    for (let i = 0, length = rows.length; i < length; i++) {
      yield TVP_ROW_TOKEN;
      const row = rows[i];
      for (let k = 0, len2 = row.length; k < len2; k++) {
        const column = columns[k];
        const value = row[k];
        const param = {
          value: column.type.validate(value, parameter.collation),
          length: column.length,
          scale: column.scale,
          precision: column.precision
        };

        // TvpColumnData
        yield column.type.generateParameterLength(param, options);
        yield* column.type.generateParameterData(param, options);
      }
    }
    yield TVP_END_TOKEN;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'object') {
      throw new TypeError('Invalid table.');
    }
    if (!Array.isArray(value.columns)) {
      throw new TypeError('Invalid table.');
    }
    if (!Array.isArray(value.rows)) {
      throw new TypeError('Invalid table.');
    }
    return value;
  }
};
var _default = TVP;
exports.default = _default;
module.exports = TVP;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfd3JpdGFibGVUcmFja2luZ0J1ZmZlciIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwib2JqIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJUVlBfUk9XX1RPS0VOIiwiQnVmZmVyIiwiZnJvbSIsIlRWUF9FTkRfVE9LRU4iLCJOVUxMX0xFTkdUSCIsIlRWUCIsImlkIiwidHlwZSIsIm5hbWUiLCJkZWNsYXJhdGlvbiIsInBhcmFtZXRlciIsInZhbHVlIiwiZ2VuZXJhdGVUeXBlSW5mbyIsIl9wYXJhbWV0ZXIkdmFsdWUiLCJfcGFyYW1ldGVyJHZhbHVlMiIsImRhdGFiYXNlTmFtZSIsInNjaGVtYSIsInR5cGVOYW1lIiwiYnVmZmVyTGVuZ3RoIiwiYnl0ZUxlbmd0aCIsImJ1ZmZlciIsIldyaXRhYmxlVHJhY2tpbmdCdWZmZXIiLCJ3cml0ZVVJbnQ4Iiwid3JpdGVCVmFyY2hhciIsImRhdGEiLCJnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCIsIm9wdGlvbnMiLCJjb2x1bW5zIiwiYWxsb2MiLCJ3cml0ZVVJbnQxNkxFIiwibGVuZ3RoIiwiZ2VuZXJhdGVQYXJhbWV0ZXJEYXRhIiwicm93cyIsImkiLCJsZW4iLCJjb2x1bW4iLCJidWZmIiwid3JpdGVVSW50MzJMRSIsInJvdyIsImsiLCJsZW4yIiwicGFyYW0iLCJ2YWxpZGF0ZSIsImNvbGxhdGlvbiIsInNjYWxlIiwicHJlY2lzaW9uIiwiVHlwZUVycm9yIiwiQXJyYXkiLCJpc0FycmF5IiwiX2RlZmF1bHQiLCJleHBvcnRzIiwibW9kdWxlIl0sInNvdXJjZXMiOlsiLi4vLi4vc3JjL2RhdGEtdHlwZXMvdHZwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgRGF0YVR5cGUgfSBmcm9tICcuLi9kYXRhLXR5cGUnO1xuaW1wb3J0IFdyaXRhYmxlVHJhY2tpbmdCdWZmZXIgZnJvbSAnLi4vdHJhY2tpbmctYnVmZmVyL3dyaXRhYmxlLXRyYWNraW5nLWJ1ZmZlcic7XG5cbmNvbnN0IFRWUF9ST1dfVE9LRU4gPSBCdWZmZXIuZnJvbShbMHgwMV0pO1xuY29uc3QgVFZQX0VORF9UT0tFTiA9IEJ1ZmZlci5mcm9tKFsweDAwXSk7XG5cbmNvbnN0IE5VTExfTEVOR1RIID0gQnVmZmVyLmZyb20oWzB4RkYsIDB4RkZdKTtcblxuY29uc3QgVFZQOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4RjMsXG4gIHR5cGU6ICdUVlBUWVBFJyxcbiAgbmFtZTogJ1RWUCcsXG5cbiAgZGVjbGFyYXRpb246IGZ1bmN0aW9uKHBhcmFtZXRlcikge1xuICAgIGNvbnN0IHZhbHVlID0gcGFyYW1ldGVyLnZhbHVlIGFzIGFueTsgLy8gVGVtcG9yYXJ5IHNvbHV0aW9uLiBSZW1vdmUgJ2FueScgbGF0ZXIuXG4gICAgcmV0dXJuIHZhbHVlLm5hbWUgKyAnIHJlYWRvbmx5JztcbiAgfSxcblxuICBnZW5lcmF0ZVR5cGVJbmZvKHBhcmFtZXRlcikge1xuICAgIGNvbnN0IGRhdGFiYXNlTmFtZSA9ICcnO1xuICAgIGNvbnN0IHNjaGVtYSA9IHBhcmFtZXRlci52YWx1ZT8uc2NoZW1hID8/ICcnO1xuICAgIGNvbnN0IHR5cGVOYW1lID0gcGFyYW1ldGVyLnZhbHVlPy5uYW1lID8/ICcnO1xuXG4gICAgY29uc3QgYnVmZmVyTGVuZ3RoID0gMSArXG4gICAgICAxICsgQnVmZmVyLmJ5dGVMZW5ndGgoZGF0YWJhc2VOYW1lLCAndWNzMicpICtcbiAgICAgIDEgKyBCdWZmZXIuYnl0ZUxlbmd0aChzY2hlbWEsICd1Y3MyJykgK1xuICAgICAgMSArIEJ1ZmZlci5ieXRlTGVuZ3RoKHR5cGVOYW1lLCAndWNzMicpO1xuXG4gICAgY29uc3QgYnVmZmVyID0gbmV3IFdyaXRhYmxlVHJhY2tpbmdCdWZmZXIoYnVmZmVyTGVuZ3RoLCAndWNzMicpO1xuICAgIGJ1ZmZlci53cml0ZVVJbnQ4KHRoaXMuaWQpO1xuICAgIGJ1ZmZlci53cml0ZUJWYXJjaGFyKGRhdGFiYXNlTmFtZSk7XG4gICAgYnVmZmVyLndyaXRlQlZhcmNoYXIoc2NoZW1hKTtcbiAgICBidWZmZXIud3JpdGVCVmFyY2hhcih0eXBlTmFtZSk7XG5cbiAgICByZXR1cm4gYnVmZmVyLmRhdGE7XG4gIH0sXG5cbiAgZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgocGFyYW1ldGVyLCBvcHRpb25zKSB7XG4gICAgaWYgKHBhcmFtZXRlci52YWx1ZSA9PSBudWxsKSB7XG4gICAgICByZXR1cm4gTlVMTF9MRU5HVEg7XG4gICAgfVxuXG4gICAgY29uc3QgeyBjb2x1bW5zIH0gPSBwYXJhbWV0ZXIudmFsdWU7XG4gICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDIpO1xuICAgIGJ1ZmZlci53cml0ZVVJbnQxNkxFKGNvbHVtbnMubGVuZ3RoLCAwKTtcbiAgICByZXR1cm4gYnVmZmVyO1xuICB9LFxuXG4gICpnZW5lcmF0ZVBhcmFtZXRlckRhdGEocGFyYW1ldGVyLCBvcHRpb25zKSB7XG4gICAgaWYgKHBhcmFtZXRlci52YWx1ZSA9PSBudWxsKSB7XG4gICAgICB5aWVsZCBUVlBfRU5EX1RPS0VOO1xuICAgICAgeWllbGQgVFZQX0VORF9UT0tFTjtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCB7IGNvbHVtbnMsIHJvd3MgfSA9IHBhcmFtZXRlci52YWx1ZTtcblxuICAgIGZvciAobGV0IGkgPSAwLCBsZW4gPSBjb2x1bW5zLmxlbmd0aDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgICBjb25zdCBjb2x1bW4gPSBjb2x1bW5zW2ldO1xuXG4gICAgICBjb25zdCBidWZmID0gQnVmZmVyLmFsbG9jKDYpO1xuICAgICAgLy8gVXNlclR5cGVcbiAgICAgIGJ1ZmYud3JpdGVVSW50MzJMRSgweDAwMDAwMDAwLCAwKTtcblxuICAgICAgLy8gRmxhZ3NcbiAgICAgIGJ1ZmYud3JpdGVVSW50MTZMRSgweDAwMDAsIDQpO1xuICAgICAgeWllbGQgYnVmZjtcblxuICAgICAgLy8gVFlQRV9JTkZPXG4gICAgICB5aWVsZCBjb2x1bW4udHlwZS5nZW5lcmF0ZVR5cGVJbmZvKGNvbHVtbik7XG5cbiAgICAgIC8vIENvbE5hbWVcbiAgICAgIHlpZWxkIEJ1ZmZlci5mcm9tKFsweDAwXSk7XG4gICAgfVxuXG4gICAgeWllbGQgVFZQX0VORF9UT0tFTjtcblxuICAgIGZvciAobGV0IGkgPSAwLCBsZW5ndGggPSByb3dzLmxlbmd0aDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICB5aWVsZCBUVlBfUk9XX1RPS0VOO1xuXG4gICAgICBjb25zdCByb3cgPSByb3dzW2ldO1xuICAgICAgZm9yIChsZXQgayA9IDAsIGxlbjIgPSByb3cubGVuZ3RoOyBrIDwgbGVuMjsgaysrKSB7XG4gICAgICAgIGNvbnN0IGNvbHVtbiA9IGNvbHVtbnNba107XG4gICAgICAgIGNvbnN0IHZhbHVlID0gcm93W2tdO1xuXG4gICAgICAgIGNvbnN0IHBhcmFtID0ge1xuICAgICAgICAgIHZhbHVlOiBjb2x1bW4udHlwZS52YWxpZGF0ZSh2YWx1ZSwgcGFyYW1ldGVyLmNvbGxhdGlvbiksXG4gICAgICAgICAgbGVuZ3RoOiBjb2x1bW4ubGVuZ3RoLFxuICAgICAgICAgIHNjYWxlOiBjb2x1bW4uc2NhbGUsXG4gICAgICAgICAgcHJlY2lzaW9uOiBjb2x1bW4ucHJlY2lzaW9uXG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gVHZwQ29sdW1uRGF0YVxuICAgICAgICB5aWVsZCBjb2x1bW4udHlwZS5nZW5lcmF0ZVBhcmFtZXRlckxlbmd0aChwYXJhbSwgb3B0aW9ucyk7XG4gICAgICAgIHlpZWxkICogY29sdW1uLnR5cGUuZ2VuZXJhdGVQYXJhbWV0ZXJEYXRhKHBhcmFtLCBvcHRpb25zKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB5aWVsZCBUVlBfRU5EX1RPS0VOO1xuICB9LFxuXG4gIHZhbGlkYXRlOiBmdW5jdGlvbih2YWx1ZSk6IEJ1ZmZlciB8IG51bGwge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAodHlwZW9mIHZhbHVlICE9PSAnb2JqZWN0Jykge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignSW52YWxpZCB0YWJsZS4nKTtcbiAgICB9XG5cbiAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUuY29sdW1ucykpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ludmFsaWQgdGFibGUuJyk7XG4gICAgfVxuXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHZhbHVlLnJvd3MpKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdJbnZhbGlkIHRhYmxlLicpO1xuICAgIH1cblxuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgVFZQO1xubW9kdWxlLmV4cG9ydHMgPSBUVlA7XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUNBLElBQUFBLHVCQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFBaUYsU0FBQUQsdUJBQUFFLEdBQUEsV0FBQUEsR0FBQSxJQUFBQSxHQUFBLENBQUFDLFVBQUEsR0FBQUQsR0FBQSxLQUFBRSxPQUFBLEVBQUFGLEdBQUE7QUFFakYsTUFBTUcsYUFBYSxHQUFHQyxNQUFNLENBQUNDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQ3pDLE1BQU1DLGFBQWEsR0FBR0YsTUFBTSxDQUFDQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUV6QyxNQUFNRSxXQUFXLEdBQUdILE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBRTdDLE1BQU1HLEdBQWEsR0FBRztFQUNwQkMsRUFBRSxFQUFFLElBQUk7RUFDUkMsSUFBSSxFQUFFLFNBQVM7RUFDZkMsSUFBSSxFQUFFLEtBQUs7RUFFWEMsV0FBVyxFQUFFLFNBQUFBLENBQVNDLFNBQVMsRUFBRTtJQUMvQixNQUFNQyxLQUFLLEdBQUdELFNBQVMsQ0FBQ0MsS0FBWSxDQUFDLENBQUM7SUFDdEMsT0FBT0EsS0FBSyxDQUFDSCxJQUFJLEdBQUcsV0FBVztFQUNqQyxDQUFDO0VBRURJLGdCQUFnQkEsQ0FBQ0YsU0FBUyxFQUFFO0lBQUEsSUFBQUcsZ0JBQUEsRUFBQUMsaUJBQUE7SUFDMUIsTUFBTUMsWUFBWSxHQUFHLEVBQUU7SUFDdkIsTUFBTUMsTUFBTSxHQUFHLEVBQUFILGdCQUFBLEdBQUFILFNBQVMsQ0FBQ0MsS0FBSyxjQUFBRSxnQkFBQSx1QkFBZkEsZ0JBQUEsQ0FBaUJHLE1BQU0sS0FBSSxFQUFFO0lBQzVDLE1BQU1DLFFBQVEsR0FBRyxFQUFBSCxpQkFBQSxHQUFBSixTQUFTLENBQUNDLEtBQUssY0FBQUcsaUJBQUEsdUJBQWZBLGlCQUFBLENBQWlCTixJQUFJLEtBQUksRUFBRTtJQUU1QyxNQUFNVSxZQUFZLEdBQUcsQ0FBQyxHQUNwQixDQUFDLEdBQUdqQixNQUFNLENBQUNrQixVQUFVLENBQUNKLFlBQVksRUFBRSxNQUFNLENBQUMsR0FDM0MsQ0FBQyxHQUFHZCxNQUFNLENBQUNrQixVQUFVLENBQUNILE1BQU0sRUFBRSxNQUFNLENBQUMsR0FDckMsQ0FBQyxHQUFHZixNQUFNLENBQUNrQixVQUFVLENBQUNGLFFBQVEsRUFBRSxNQUFNLENBQUM7SUFFekMsTUFBTUcsTUFBTSxHQUFHLElBQUlDLCtCQUFzQixDQUFDSCxZQUFZLEVBQUUsTUFBTSxDQUFDO0lBQy9ERSxNQUFNLENBQUNFLFVBQVUsQ0FBQyxJQUFJLENBQUNoQixFQUFFLENBQUM7SUFDMUJjLE1BQU0sQ0FBQ0csYUFBYSxDQUFDUixZQUFZLENBQUM7SUFDbENLLE1BQU0sQ0FBQ0csYUFBYSxDQUFDUCxNQUFNLENBQUM7SUFDNUJJLE1BQU0sQ0FBQ0csYUFBYSxDQUFDTixRQUFRLENBQUM7SUFFOUIsT0FBT0csTUFBTSxDQUFDSSxJQUFJO0VBQ3BCLENBQUM7RUFFREMsdUJBQXVCQSxDQUFDZixTQUFTLEVBQUVnQixPQUFPLEVBQUU7SUFDMUMsSUFBSWhCLFNBQVMsQ0FBQ0MsS0FBSyxJQUFJLElBQUksRUFBRTtNQUMzQixPQUFPUCxXQUFXO0lBQ3BCO0lBRUEsTUFBTTtNQUFFdUI7SUFBUSxDQUFDLEdBQUdqQixTQUFTLENBQUNDLEtBQUs7SUFDbkMsTUFBTVMsTUFBTSxHQUFHbkIsTUFBTSxDQUFDMkIsS0FBSyxDQUFDLENBQUMsQ0FBQztJQUM5QlIsTUFBTSxDQUFDUyxhQUFhLENBQUNGLE9BQU8sQ0FBQ0csTUFBTSxFQUFFLENBQUMsQ0FBQztJQUN2QyxPQUFPVixNQUFNO0VBQ2YsQ0FBQztFQUVELENBQUNXLHFCQUFxQkEsQ0FBQ3JCLFNBQVMsRUFBRWdCLE9BQU8sRUFBRTtJQUN6QyxJQUFJaEIsU0FBUyxDQUFDQyxLQUFLLElBQUksSUFBSSxFQUFFO01BQzNCLE1BQU1SLGFBQWE7TUFDbkIsTUFBTUEsYUFBYTtNQUNuQjtJQUNGO0lBRUEsTUFBTTtNQUFFd0IsT0FBTztNQUFFSztJQUFLLENBQUMsR0FBR3RCLFNBQVMsQ0FBQ0MsS0FBSztJQUV6QyxLQUFLLElBQUlzQixDQUFDLEdBQUcsQ0FBQyxFQUFFQyxHQUFHLEdBQUdQLE9BQU8sQ0FBQ0csTUFBTSxFQUFFRyxDQUFDLEdBQUdDLEdBQUcsRUFBRUQsQ0FBQyxFQUFFLEVBQUU7TUFDbEQsTUFBTUUsTUFBTSxHQUFHUixPQUFPLENBQUNNLENBQUMsQ0FBQztNQUV6QixNQUFNRyxJQUFJLEdBQUduQyxNQUFNLENBQUMyQixLQUFLLENBQUMsQ0FBQyxDQUFDO01BQzVCO01BQ0FRLElBQUksQ0FBQ0MsYUFBYSxDQUFDLFVBQVUsRUFBRSxDQUFDLENBQUM7O01BRWpDO01BQ0FELElBQUksQ0FBQ1AsYUFBYSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7TUFDN0IsTUFBTU8sSUFBSTs7TUFFVjtNQUNBLE1BQU1ELE1BQU0sQ0FBQzVCLElBQUksQ0FBQ0ssZ0JBQWdCLENBQUN1QixNQUFNLENBQUM7O01BRTFDO01BQ0EsTUFBTWxDLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDM0I7SUFFQSxNQUFNQyxhQUFhO0lBRW5CLEtBQUssSUFBSThCLENBQUMsR0FBRyxDQUFDLEVBQUVILE1BQU0sR0FBR0UsSUFBSSxDQUFDRixNQUFNLEVBQUVHLENBQUMsR0FBR0gsTUFBTSxFQUFFRyxDQUFDLEVBQUUsRUFBRTtNQUNyRCxNQUFNakMsYUFBYTtNQUVuQixNQUFNc0MsR0FBRyxHQUFHTixJQUFJLENBQUNDLENBQUMsQ0FBQztNQUNuQixLQUFLLElBQUlNLENBQUMsR0FBRyxDQUFDLEVBQUVDLElBQUksR0FBR0YsR0FBRyxDQUFDUixNQUFNLEVBQUVTLENBQUMsR0FBR0MsSUFBSSxFQUFFRCxDQUFDLEVBQUUsRUFBRTtRQUNoRCxNQUFNSixNQUFNLEdBQUdSLE9BQU8sQ0FBQ1ksQ0FBQyxDQUFDO1FBQ3pCLE1BQU01QixLQUFLLEdBQUcyQixHQUFHLENBQUNDLENBQUMsQ0FBQztRQUVwQixNQUFNRSxLQUFLLEdBQUc7VUFDWjlCLEtBQUssRUFBRXdCLE1BQU0sQ0FBQzVCLElBQUksQ0FBQ21DLFFBQVEsQ0FBQy9CLEtBQUssRUFBRUQsU0FBUyxDQUFDaUMsU0FBUyxDQUFDO1VBQ3ZEYixNQUFNLEVBQUVLLE1BQU0sQ0FBQ0wsTUFBTTtVQUNyQmMsS0FBSyxFQUFFVCxNQUFNLENBQUNTLEtBQUs7VUFDbkJDLFNBQVMsRUFBRVYsTUFBTSxDQUFDVTtRQUNwQixDQUFDOztRQUVEO1FBQ0EsTUFBTVYsTUFBTSxDQUFDNUIsSUFBSSxDQUFDa0IsdUJBQXVCLENBQUNnQixLQUFLLEVBQUVmLE9BQU8sQ0FBQztRQUN6RCxPQUFRUyxNQUFNLENBQUM1QixJQUFJLENBQUN3QixxQkFBcUIsQ0FBQ1UsS0FBSyxFQUFFZixPQUFPLENBQUM7TUFDM0Q7SUFDRjtJQUVBLE1BQU12QixhQUFhO0VBQ3JCLENBQUM7RUFFRHVDLFFBQVEsRUFBRSxTQUFBQSxDQUFTL0IsS0FBSyxFQUFpQjtJQUN2QyxJQUFJQSxLQUFLLElBQUksSUFBSSxFQUFFO01BQ2pCLE9BQU8sSUFBSTtJQUNiO0lBRUEsSUFBSSxPQUFPQSxLQUFLLEtBQUssUUFBUSxFQUFFO01BQzdCLE1BQU0sSUFBSW1DLFNBQVMsQ0FBQyxnQkFBZ0IsQ0FBQztJQUN2QztJQUVBLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPLENBQUNyQyxLQUFLLENBQUNnQixPQUFPLENBQUMsRUFBRTtNQUNqQyxNQUFNLElBQUltQixTQUFTLENBQUMsZ0JBQWdCLENBQUM7SUFDdkM7SUFFQSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxDQUFDckMsS0FBSyxDQUFDcUIsSUFBSSxDQUFDLEVBQUU7TUFDOUIsTUFBTSxJQUFJYyxTQUFTLENBQUMsZ0JBQWdCLENBQUM7SUFDdkM7SUFFQSxPQUFPbkMsS0FBSztFQUNkO0FBQ0YsQ0FBQztBQUFDLElBQUFzQyxRQUFBLEdBRWE1QyxHQUFHO0FBQUE2QyxPQUFBLENBQUFuRCxPQUFBLEdBQUFrRCxRQUFBO0FBQ2xCRSxNQUFNLENBQUNELE9BQU8sR0FBRzdDLEdBQUcifQ==