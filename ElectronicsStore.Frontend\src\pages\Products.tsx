import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  GridView as GridViewIcon,
  ViewList as ListViewIcon,
  ShoppingCart as ProductIcon,
} from '@mui/icons-material';
import PageContainer from '../components/PageContainer';
import DashboardHeader from '../components/DashboardHeader';
import ProductForm from '../components/ProductForm';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  cost: number;
  stock: number;
  category: string;
  brand: string;
  image: string;
  status: 'active' | 'inactive' | 'out_of_stock';
  createdAt: string;
  updatedAt: string;
}

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [saving, setSaving] = useState(false);

  // Mock data
  useEffect(() => {
    const mockProducts: Product[] = [
      {
        id: 1,
        name: 'iPhone 15 Pro',
        description: 'أحدث هاتف من آبل مع كاميرا متطورة',
        price: 4500,
        cost: 3200,
        stock: 25,
        category: 'هواتف ذكية',
        brand: 'Apple',
        image: '/api/placeholder/300/200',
        status: 'active',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
      },
      {
        id: 2,
        name: 'Samsung Galaxy S24',
        description: 'هاتف سامسونج الرائد مع شاشة AMOLED',
        price: 3800,
        cost: 2800,
        stock: 15,
        category: 'هواتف ذكية',
        brand: 'Samsung',
        image: '/api/placeholder/300/200',
        status: 'active',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
      },
      {
        id: 3,
        name: 'MacBook Pro M3',
        description: 'لابتوب آبل بمعالج M3 الجديد',
        price: 8500,
        cost: 6200,
        stock: 8,
        category: 'لابتوب',
        brand: 'Apple',
        image: '/api/placeholder/300/200',
        status: 'active',
        createdAt: '2024-01-05',
        updatedAt: '2024-01-15',
      },
      {
        id: 4,
        name: 'AirPods Pro',
        description: 'سماعات لاسلكية مع إلغاء الضوضاء',
        price: 950,
        cost: 650,
        stock: 0,
        category: 'إكسسوارات',
        brand: 'Apple',
        image: '/api/placeholder/300/200',
        status: 'out_of_stock',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-12',
      },
      {
        id: 5,
        name: 'Dell XPS 13',
        description: 'لابتوب ديل بتصميم أنيق وأداء قوي',
        price: 4200,
        cost: 3100,
        stock: 12,
        category: 'لابتوب',
        brand: 'Dell',
        image: '/api/placeholder/300/200',
        status: 'active',
        createdAt: '2023-12-28',
        updatedAt: '2024-01-08',
      },
    ];

    setTimeout(() => {
      setProducts(mockProducts);
      setLoading(false);
    }, 1000);
  }, []);

  const categories = ['all', 'هواتف ذكية', 'لابتوب', 'إكسسوارات', 'أجهزة لوحية'];
  const statuses = ['all', 'active', 'inactive', 'out_of_stock'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'out_of_stock': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'out_of_stock': return 'نفد المخزون';
      default: return status;
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = (productId: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      setProducts(products.filter(p => p.id !== productId));
    }
  };

  const handleSaveProduct = async (productData: any) => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (selectedProduct) {
        // Update existing product
        setProducts(products.map(p =>
          p.id === selectedProduct.id
            ? { ...productData, id: selectedProduct.id, updatedAt: new Date().toISOString() }
            : p
        ));
        alert('تم تحديث المنتج بنجاح!');
      } else {
        // Add new product
        const newProduct = {
          ...productData,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setProducts([...products, newProduct]);
        alert('تم إضافة المنتج بنجاح!');
      }

      setOpenDialog(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error saving product:', error);
      alert('حدث خطأ أثناء حفظ المنتج');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Typography variant="h6">جاري تحميل المنتجات...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <DashboardHeader
        title="إدارة المنتجات"
        subtitle="عرض وإدارة جميع منتجات المتجر"
        showBreadcrumbs={true}
        showLastUpdate={true}
      />

      {/* Filters and Search */}
      <Card sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>الفئة</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="الفئة"
              >
                {categories.map(category => (
                  <MenuItem key={category} value={category}>
                    {category === 'all' ? 'جميع الفئات' : category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="الحالة"
              >
                {statuses.map(status => (
                  <MenuItem key={status} value={status}>
                    {status === 'all' ? 'جميع الحالات' : getStatusText(status)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box display="flex" justifyContent="flex-end" gap={1}>
              <IconButton
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
              >
                <GridViewIcon />
              </IconButton>
              <IconButton
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
              >
                <ListViewIcon />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Card>

      {/* Products Display */}
      {viewMode === 'grid' ? (
        <Grid container spacing={3}>
          {filteredProducts.map((product) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardMedia
                  component="img"
                  height="200"
                  image={product.image}
                  alt={product.name}
                  sx={{ objectFit: 'cover' }}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" gutterBottom noWrap>
                    {product.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {product.description}
                  </Typography>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="h6" color="primary">
                      {formatCurrency(product.price)}
                    </Typography>
                    <Chip
                      label={getStatusText(product.status)}
                      color={getStatusColor(product.status) as any}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    المخزون: {product.stock} قطعة
                  </Typography>
                </CardContent>
                <Box sx={{ p: 1, display: 'flex', justifyContent: 'space-between' }}>
                  <Tooltip title="عرض">
                    <IconButton size="small">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="تعديل">
                    <IconButton size="small" onClick={() => handleEditProduct(product)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="حذف">
                    <IconButton size="small" color="error" onClick={() => handleDeleteProduct(product.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>المنتج</TableCell>
                <TableCell>الفئة</TableCell>
                <TableCell>السعر</TableCell>
                <TableCell>المخزون</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar src={product.image} alt={product.name}>
                        <ProductIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2">{product.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {product.brand}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell>{formatCurrency(product.price)}</TableCell>
                  <TableCell>{product.stock}</TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(product.status)}
                      color={getStatusColor(product.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleEditProduct(product)}>
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error" onClick={() => handleDeleteProduct(product.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add Product FAB */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={handleAddProduct}
      >
        <AddIcon />
      </Fab>

      {/* Product Form Dialog */}
      <ProductForm
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setSelectedProduct(null);
        }}
        onSave={handleSaveProduct}
        product={selectedProduct}
        loading={saving}
      />
    </PageContainer>
  );
};

export default Products;
