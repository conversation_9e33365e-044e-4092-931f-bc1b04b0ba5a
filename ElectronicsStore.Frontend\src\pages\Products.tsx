import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  Grid,
  Typography,
  TextField,
  Button,
  IconButton,
  Fab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  GridView as GridViewIcon,
  List as ListIcon,
  Search as SearchIcon,
  Inventory as ProductIcon,
} from '@mui/icons-material';

import PageContainer from '../components/PageContainer';
import DashboardHeader from '../components/DashboardHeader';
import ProductForm from '../components/ProductForm';
import { productService, categoryService } from '../services';
import { Product, Category } from '../types';

const Products: React.FC = () => {
  // State
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>('');

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Load products and categories
      const [productsResponse, categoriesResponse] = await Promise.all([
        productService.getProducts({ pageSize: 100 }),
        categoryService.getCategories()
      ]);
      
      setProducts(productsResponse.data || []);
      setCategories(categoriesResponse || []);
    } catch (error: any) {
      console.error('Error loading data:', error);
      setError(error.message || 'حدث خطأ أثناء تحميل البيانات');
      
      // Fallback to mock data
      const mockProducts: Product[] = [
        {
          id: 1,
          name: 'iPhone 15 Pro',
          description: 'أحدث هاتف من آبل مع كاميرا متطورة',
          defaultSellingPrice: 4500,
          defaultCostPrice: 3200,
          minSellingPrice: 4000,
          currentStock: 25,
          categoryName: 'هواتف ذكية',
          categoryId: 1,
          barcode: '123456789',
          inventoryValue: 80000,
          createdAt: '2024-01-15',
        },
        {
          id: 2,
          name: 'Samsung Galaxy S24',
          description: 'هاتف سامسونج الرائد مع شاشة AMOLED',
          defaultSellingPrice: 3800,
          defaultCostPrice: 2800,
          minSellingPrice: 3500,
          currentStock: 15,
          categoryName: 'هواتف ذكية',
          categoryId: 1,
          barcode: '123456790',
          inventoryValue: 42000,
          createdAt: '2024-01-10',
        },
      ];
      
      setProducts(mockProducts);
    } finally {
      setLoading(false);
    }
  };

  // Handle product save
  const handleSaveProduct = async (product: Product) => {
    try {
      setSaving(true);
      
      if (selectedProduct) {
        // Update existing product
        const updatedProduct = await productService.updateProduct(selectedProduct.id, {
          name: product.name,
          description: product.description,
          categoryId: product.categoryId,
          supplierId: product.supplierId,
          defaultCostPrice: product.defaultCostPrice,
          defaultSellingPrice: product.defaultSellingPrice,
          minSellingPrice: product.minSellingPrice,
          barcode: product.barcode,
        });
        
        setProducts(prev => prev.map(p => p.id === selectedProduct.id ? updatedProduct : p));
      } else {
        // Create new product
        const newProduct = await productService.createProduct({
          name: product.name,
          description: product.description,
          categoryId: product.categoryId,
          supplierId: product.supplierId,
          defaultCostPrice: product.defaultCostPrice,
          defaultSellingPrice: product.defaultSellingPrice,
          minSellingPrice: product.minSellingPrice,
          barcode: product.barcode,
        });
        
        setProducts(prev => [...prev, newProduct]);
      }
      
      setOpenDialog(false);
      setSelectedProduct(null);
    } catch (error: any) {
      console.error('Error saving product:', error);
      setError(error.message || 'حدث خطأ أثناء حفظ المنتج');
    } finally {
      setSaving(false);
    }
  };

  // Other handlers
  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = async (productId: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        await productService.deleteProduct(productId);
        setProducts(products.filter(p => p.id !== productId));
      } catch (error: any) {
        console.error('Error deleting product:', error);
        setError(error.message || 'حدث خطأ أثناء حذف المنتج');
      }
    }
  };

  // Utility functions
  const getStockStatus = (stock: number) => {
    if (stock === 0) return { color: 'error', text: 'نفد المخزون' };
    if (stock <= 10) return { color: 'warning', text: 'مخزون منخفض' };
    return { color: 'success', text: 'متوفر' };
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = categoryFilter === 'all' || product.categoryName === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
          <Typography variant="h6" sx={{ ml: 2 }}>جاري تحميل المنتجات...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <DashboardHeader
        title="إدارة المنتجات"
        subtitle="عرض وإدارة جميع منتجات المتجر"
        showBreadcrumbs={true}
        showLastUpdate={true}
      />

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Filters and Search */}
      <Card sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>الفئة</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="الفئة"
              >
                <MenuItem value="all">جميع الفئات</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category.id} value={category.name}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" justifyContent="flex-end" gap={1}>
              <IconButton
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
              >
                <GridViewIcon />
              </IconButton>
              <IconButton
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
              >
                <ListIcon />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Card>

      {/* Products Display */}
      {viewMode === 'grid' ? (
        <Grid container spacing={3}>
          {filteredProducts.map((product) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ p: 2, flexGrow: 1 }}>
                  <Typography variant="h6" gutterBottom noWrap>
                    {product.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {product.categoryName}
                  </Typography>
                  <Typography variant="h6" color="primary" sx={{ mb: 1 }}>
                    {formatCurrency(product.defaultSellingPrice)}
                  </Typography>
                  <Chip
                    label={getStockStatus(product.currentStock).text}
                    color={getStockStatus(product.currentStock).color as any}
                    size="small"
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="body2">
                    المخزون: {product.currentStock}
                  </Typography>
                </Box>
                <Box sx={{ p: 1, display: 'flex', justifyContent: 'space-between' }}>
                  <Tooltip title="عرض">
                    <IconButton size="small">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="تعديل">
                    <IconButton size="small" onClick={() => handleEditProduct(product)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="حذف">
                    <IconButton size="small" color="error" onClick={() => handleDeleteProduct(product.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>المنتج</TableCell>
                <TableCell>الفئة</TableCell>
                <TableCell>السعر</TableCell>
                <TableCell>المخزون</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar alt={product.name}>
                        <ProductIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2">{product.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {product.barcode}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{product.categoryName}</TableCell>
                  <TableCell>{formatCurrency(product.defaultSellingPrice)}</TableCell>
                  <TableCell>{product.currentStock}</TableCell>
                  <TableCell>
                    <Chip
                      label={getStockStatus(product.currentStock).text}
                      color={getStockStatus(product.currentStock).color as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleEditProduct(product)}>
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error" onClick={() => handleDeleteProduct(product.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add Product FAB */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={handleAddProduct}
      >
        <AddIcon />
      </Fab>

      {/* Product Form Dialog */}
      <ProductForm
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setSelectedProduct(null);
        }}
        onSave={handleSaveProduct}
        product={selectedProduct}
        loading={saving}
      />
    </PageContainer>
  );
};

export default Products;
