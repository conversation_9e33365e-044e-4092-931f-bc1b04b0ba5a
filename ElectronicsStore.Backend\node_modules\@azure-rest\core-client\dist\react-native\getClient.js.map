{"version": 3, "file": "getClient.js", "sourceRoot": "", "sources": ["../../src/getClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAEtE,OAAO,EAAE,qBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAE3D,OAAO,EACL,SAAS,IAAI,YAAY,GAE1B,MAAM,2BAA2B,CAAC;AAEnC;;GAEG;AACH,SAAS,qBAAqB,CAAC,UAA6B;IAC1D,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;QAC1B,uCACK,UAAU,KACb,UAAU,CAAC,WAAW,EAAE,KAAK;;gBAC3B,MAAA,UAAU,CAAC,UAAU,2DAAG,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC,IACD;IACJ,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAmBD,MAAM,UAAU,SAAS,CACvB,QAAgB,EAChB,4BAAgF,EAChF,gBAA+B,EAAE;IAEjC,IAAI,WAAwD,CAAC;IAC7D,IAAI,4BAA4B,EAAE,CAAC;QACjC,IAAI,YAAY,CAAC,4BAA4B,CAAC,EAAE,CAAC;YAC/C,WAAW,GAAG,4BAA4B,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,4BAA4B,aAA5B,4BAA4B,cAA5B,4BAA4B,GAAI,EAAE,CAAC;QACrD,CAAC;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IAC7E,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,gCACpC,aAAa,KAChB,QAAQ,GACW,CAAW,CAAC;IAEjC,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,GAAG,IAAgB,EAAE,EAAE;QACnD,OAAO;YACL,GAAG,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAChE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YAClF,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACjE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACnF,CAAC;YACD,GAAG,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAChE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YAClF,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAClE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACpF,CAAC;YACD,MAAM,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACnE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACrF,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACjE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACnF,CAAC;YACD,OAAO,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACpE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACtF,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAClE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;YACpF,CAAC;SACF,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,MAAM;QACZ,aAAa,EAAE,MAAM;QACrB,QAAQ,EAAE,SAAS,CAAC,QAAQ;KAC7B,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CACnB,KAA0D;IAE1D,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { KeyCredential, TokenCredential } from \"@azure/core-auth\";\nimport { isKeyCredential, isTokenCredential } from \"@azure/core-auth\";\nimport { type PipelineOptions } from \"@azure/core-rest-pipeline\";\nimport { createDefaultPipeline } from \"./clientHelpers.js\";\nimport type { Client, ClientOptions, RequestParameters, StreamableMethod } from \"./common.js\";\nimport {\n  getClient as tspGetClient,\n  type ClientOptions as TspClientOptions,\n} from \"@typespec/ts-http-runtime\";\n\n/**\n * Function to wrap RequestParameters so that we get the legacy onResponse behavior in core-client-rest\n */\nfunction wrapRequestParameters(parameters: RequestParameters): RequestParameters {\n  if (parameters.onResponse) {\n    return {\n      ...parameters,\n      onResponse(rawResponse, error) {\n        parameters.onResponse?.(rawResponse, error, error);\n      },\n    };\n  }\n\n  return parameters;\n}\n\n/**\n * Creates a client with a default pipeline\n * @param endpoint - Base endpoint for the client\n * @param options - Client options\n */\nexport function getClient(endpoint: string, options?: ClientOptions): Client;\n/**\n * Creates a client with a default pipeline\n * @param endpoint - Base endpoint for the client\n * @param credentials - Credentials to authenticate the requests\n * @param options - Client options\n */\nexport function getClient(\n  endpoint: string,\n  credentials?: TokenCredential | KeyCredential,\n  options?: ClientOptions,\n): Client;\nexport function getClient(\n  endpoint: string,\n  credentialsOrPipelineOptions?: (TokenCredential | KeyCredential) | ClientOptions,\n  clientOptions: ClientOptions = {},\n): Client {\n  let credentials: TokenCredential | KeyCredential | undefined;\n  if (credentialsOrPipelineOptions) {\n    if (isCredential(credentialsOrPipelineOptions)) {\n      credentials = credentialsOrPipelineOptions;\n    } else {\n      clientOptions = credentialsOrPipelineOptions ?? {};\n    }\n  }\n\n  const pipeline = createDefaultPipeline(endpoint, credentials, clientOptions);\n  const tspClient = tspGetClient(endpoint, {\n    ...clientOptions,\n    pipeline,\n  } as TspClientOptions) as Client;\n\n  const client = (path: string, ...args: Array<any>) => {\n    return {\n      get: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).get(wrapRequestParameters(requestOptions));\n      },\n      post: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).post(wrapRequestParameters(requestOptions));\n      },\n      put: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).put(wrapRequestParameters(requestOptions));\n      },\n      patch: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).patch(wrapRequestParameters(requestOptions));\n      },\n      delete: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).delete(wrapRequestParameters(requestOptions));\n      },\n      head: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).head(wrapRequestParameters(requestOptions));\n      },\n      options: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).options(wrapRequestParameters(requestOptions));\n      },\n      trace: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return tspClient.path(path, ...args).trace(wrapRequestParameters(requestOptions));\n      },\n    };\n  };\n\n  return {\n    path: client,\n    pathUnchecked: client,\n    pipeline: tspClient.pipeline,\n  };\n}\n\nfunction isCredential(\n  param: (TokenCredential | KeyCredential) | PipelineOptions,\n): param is TokenCredential | KeyCredential {\n  return isKeyCredential(param) || isTokenCredential(param);\n}\n"]}