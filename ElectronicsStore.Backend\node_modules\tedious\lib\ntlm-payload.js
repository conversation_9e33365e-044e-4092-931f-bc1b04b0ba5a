"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var crypto = _interopRequireWildcard(require("crypto"));
var _jsMd = _interopRequireDefault(require("js-md4"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class NTLMResponsePayload {
  constructor(loginData) {
    this.data = this.createResponse(loginData);
  }
  toString(indent = '') {
    return indent + 'NTLM Auth';
  }
  createResponse(challenge) {
    const client_nonce = this.createClientNonce();
    const lmv2len = 24;
    const ntlmv2len = 16;
    const domain = challenge.domain;
    const username = challenge.userName;
    const password = challenge.password;
    const ntlmData = challenge.ntlmpacket;
    const server_data = ntlmData.target;
    const server_nonce = ntlmData.nonce;
    const bufferLength = 64 + domain.length * 2 + username.length * 2 + lmv2len + ntlmv2len + 8 + 8 + 8 + 4 + server_data.length + 4;
    const data = new _writableTrackingBuffer.default(bufferLength);
    data.position = 0;
    data.writeString('NTLMSSP\u0000', 'utf8');
    data.writeUInt32LE(0x03);
    const baseIdx = 64;
    const dnIdx = baseIdx;
    const unIdx = dnIdx + domain.length * 2;
    const l2Idx = unIdx + username.length * 2;
    const ntIdx = l2Idx + lmv2len;
    data.writeUInt16LE(lmv2len);
    data.writeUInt16LE(lmv2len);
    data.writeUInt32LE(l2Idx);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt32LE(ntIdx);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt32LE(dnIdx);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt32LE(unIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0x8201);
    data.writeUInt16LE(0x08);
    data.writeString(domain, 'ucs2');
    data.writeString(username, 'ucs2');
    const lmv2Data = this.lmv2Response(domain, username, password, server_nonce, client_nonce);
    data.copyFrom(lmv2Data);
    const genTime = new Date().getTime();
    const ntlmDataBuffer = this.ntlmv2Response(domain, username, password, server_nonce, server_data, client_nonce, genTime);
    data.copyFrom(ntlmDataBuffer);
    data.writeUInt32LE(0x0101);
    data.writeUInt32LE(0x0000);
    const timestamp = this.createTimestamp(genTime);
    data.copyFrom(timestamp);
    data.copyFrom(client_nonce);
    data.writeUInt32LE(0x0000);
    data.copyFrom(server_data);
    data.writeUInt32LE(0x0000);
    return data.data;
  }
  createClientNonce() {
    const client_nonce = Buffer.alloc(8, 0);
    let nidx = 0;
    while (nidx < 8) {
      client_nonce.writeUInt8(Math.ceil(Math.random() * 255), nidx);
      nidx++;
    }
    return client_nonce;
  }
  ntlmv2Response(domain, user, password, serverNonce, targetInfo, clientNonce, mytime) {
    const timestamp = this.createTimestamp(mytime);
    const hash = this.ntv2Hash(domain, user, password);
    const dataLength = 40 + targetInfo.length;
    const data = Buffer.alloc(dataLength, 0);
    serverNonce.copy(data, 0, 0, 8);
    data.writeUInt32LE(0x101, 8);
    data.writeUInt32LE(0x0, 12);
    timestamp.copy(data, 16, 0, 8);
    clientNonce.copy(data, 24, 0, 8);
    data.writeUInt32LE(0x0, 32);
    targetInfo.copy(data, 36, 0, targetInfo.length);
    data.writeUInt32LE(0x0, 36 + targetInfo.length);
    return this.hmacMD5(data, hash);
  }
  createTimestamp(time) {
    const tenthsOfAMicrosecond = (BigInt(time) + BigInt(11644473600)) * BigInt(10000000);
    const lo = Number(tenthsOfAMicrosecond & BigInt(0xffffffff));
    const hi = Number(tenthsOfAMicrosecond >> BigInt(32) & BigInt(0xffffffff));
    const result = Buffer.alloc(8);
    result.writeUInt32LE(lo, 0);
    result.writeUInt32LE(hi, 4);
    return result;
  }
  lmv2Response(domain, user, password, serverNonce, clientNonce) {
    const hash = this.ntv2Hash(domain, user, password);
    const data = Buffer.alloc(serverNonce.length + clientNonce.length, 0);
    serverNonce.copy(data);
    clientNonce.copy(data, serverNonce.length, 0, clientNonce.length);
    const newhash = this.hmacMD5(data, hash);
    const response = Buffer.alloc(newhash.length + clientNonce.length, 0);
    newhash.copy(response);
    clientNonce.copy(response, newhash.length, 0, clientNonce.length);
    return response;
  }
  ntv2Hash(domain, user, password) {
    const hash = this.ntHash(password);
    const identity = Buffer.from(user.toUpperCase() + domain.toUpperCase(), 'ucs2');
    return this.hmacMD5(identity, hash);
  }
  ntHash(text) {
    const unicodeString = Buffer.from(text, 'ucs2');
    return Buffer.from(_jsMd.default.arrayBuffer(unicodeString));
  }
  hmacMD5(data, key) {
    return crypto.createHmac('MD5', key).update(data).digest();
  }
}
var _default = NTLMResponsePayload;
exports.default = _default;
module.exports = NTLMResponsePayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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