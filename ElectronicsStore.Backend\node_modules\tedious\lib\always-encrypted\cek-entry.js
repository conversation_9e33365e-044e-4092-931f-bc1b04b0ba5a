"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CEKEntry = void 0;
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

class CEKEntry {
  constructor(ordinalVal) {
    this.ordinal = ordinalVal;
    this.databaseId = 0;
    this.cekId = 0;
    this.cekVersion = 0;
    this.cekMdVersion = Buffer.alloc(0);
    this.columnEncryptionKeyValues = [];
  }
  add(encryptedKey, dbId, keyId, keyVersion, mdVersion, keyPath, keyStoreName, algorithmName) {
    const encryptionKey = {
      encryptedKey,
      dbId,
      keyId,
      keyVersion,
      mdVersion,
      keyPath,
      keyStoreName,
      algorithmName
    };
    this.columnEncryptionKeyValues.push(encryptionKey);
    if (this.databaseId === 0) {
      this.databaseId = dbId;
      this.cekId = keyId;
      this.cekVersion = keyVersion;
      this.cekMdVersion = mdVersion;
    } else if (this.databaseId !== dbId || this.cekId !== keyId || this.cekVersion !== keyVersion || !this.cekMdVersion || !mdVersion || this.cekMdVersion.length !== mdVersion.length) {
      throw new Error('Invalid databaseId, cekId, cekVersion or cekMdVersion.');
    }
  }
}
exports.CEKEntry = CEKEntry;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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