{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC;;;GAGG;AAEH,+CAAiD;AAAxC,+GAAA,eAAe,OAAA;AACxB,uDAG4B;AAF1B,+HAAA,2BAA2B,OAAA;AAG7B,yEAAkF;AAAzE,gJAAA,mCAAmC,OAAA;AAC5C,yDAA+B;AAC/B,sDAA4B", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Azure Rest Core Client library for JavaScript\n * @packageDocumentation\n */\n\nexport { createRestError } from \"./restError.js\";\nexport {\n  addCredentialPipelinePolicy,\n  AddCredentialPipelinePolicyOptions,\n} from \"./clientHelpers.js\";\nexport { operationOptionsToRequestParameters } from \"./operationOptionHelpers.js\";\nexport * from \"./getClient.js\";\nexport * from \"./common.js\";\n"]}