{"version": 3, "file": "udt.js", "names": ["UDT", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/udt.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst UDT: DataType = {\n  id: 0xF0,\n  type: 'UDTTYPE',\n  name: 'UDT',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default UDT;\nmodule.exports = UDT;\n"], "mappings": ";;;;;;AAEA,MAAMA,GAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,KAAK;EAEXC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAEaV,GAAG;AAAAW,OAAA,CAAAC,OAAA,GAAAF,QAAA;AAClBG,MAAM,CAACF,OAAO,GAAGX,GAAG"}