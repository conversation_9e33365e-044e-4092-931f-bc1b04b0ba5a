const { executeQuery } = require('../config/database-sqlserver');

class ProductRepository {
  
  // Find all products with filters and pagination
  async findAll(filters = {}) {
    try {
      const { category_id, search, page = 1, limit = 50 } = filters;
      
      let query = `
        SELECT 
          p.id,
          p.name,
          p.barcode,
          p.description,
          p.default_cost_price,
          p.default_selling_price,
          p.min_selling_price,
          p.created_at,
          c.name as category_name,
          c.id as category_id,
          s.name as supplier_name,
          s.id as supplier_id,
          ISNULL(iv.current_quantity, 0) as stock
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory_view iv ON p.id = iv.product_id
        WHERE 1=1
      `;
      
      const params = {};
      
      if (category_id && category_id !== 'all') {
        query += ' AND p.category_id = @category_id';
        params.category_id = parseInt(category_id);
      }
      
      if (search) {
        query += ' AND (p.name LIKE @search OR p.barcode LIKE @search OR p.description LIKE @search)';
        params.search = `%${search}%`;
      }
      
      query += ' ORDER BY p.created_at DESC';
      
      // Add pagination
      const offset = (page - 1) * limit;
      query += ` OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY`;
      params.offset = offset;
      params.limit = parseInt(limit);
      
      const result = await executeQuery(query, params);
      return result.recordset;
    } catch (error) {
      throw new Error(`Database error in findAll: ${error.message}`);
    }
  }

  // Count products with filters
  async count(filters = {}) {
    try {
      const { category_id, search } = filters;
      
      let query = `SELECT COUNT(*) as total FROM products p WHERE 1=1`;
      const params = {};
      
      if (category_id && category_id !== 'all') {
        query += ' AND p.category_id = @category_id';
        params.category_id = parseInt(category_id);
      }
      
      if (search) {
        query += ' AND (p.name LIKE @search OR p.barcode LIKE @search OR p.description LIKE @search)';
        params.search = `%${search}%`;
      }
      
      const result = await executeQuery(query, params);
      return result.recordset[0].total;
    } catch (error) {
      throw new Error(`Database error in count: ${error.message}`);
    }
  }

  // Find product by ID
  async findById(id) {
    try {
      const query = `
        SELECT 
          p.id,
          p.name,
          p.barcode,
          p.description,
          p.default_cost_price,
          p.default_selling_price,
          p.min_selling_price,
          p.created_at,
          c.name as category_name,
          c.id as category_id,
          s.name as supplier_name,
          s.id as supplier_id,
          ISNULL(iv.current_quantity, 0) as stock
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory_view iv ON p.id = iv.product_id
        WHERE p.id = @id
      `;
      
      const result = await executeQuery(query, { id });
      return result.recordset[0] || null;
    } catch (error) {
      throw new Error(`Database error in findById: ${error.message}`);
    }
  }

  // Find product by barcode
  async findByBarcode(barcode) {
    try {
      const query = `SELECT id, name, barcode FROM products WHERE barcode = @barcode`;
      const result = await executeQuery(query, { barcode });
      return result.recordset[0] || null;
    } catch (error) {
      throw new Error(`Database error in findByBarcode: ${error.message}`);
    }
  }

  // Create new product
  async create(productData) {
    try {
      const query = `
        INSERT INTO products (
          name, barcode, description, category_id, supplier_id,
          default_cost_price, default_selling_price, min_selling_price
        )
        OUTPUT INSERTED.id
        VALUES (
          @name, @barcode, @description, @category_id, @supplier_id,
          @default_cost_price, @default_selling_price, @min_selling_price
        )
      `;
      
      const params = {
        name: productData.name,
        barcode: productData.barcode || null,
        description: productData.description || null,
        category_id: parseInt(productData.category_id),
        supplier_id: productData.supplier_id ? parseInt(productData.supplier_id) : null,
        default_cost_price: parseFloat(productData.default_cost_price),
        default_selling_price: parseFloat(productData.default_selling_price),
        min_selling_price: parseFloat(productData.min_selling_price)
      };
      
      const result = await executeQuery(query, params);
      return { id: result.recordset[0].id };
    } catch (error) {
      throw new Error(`Database error in create: ${error.message}`);
    }
  }

  // Update product
  async update(id, productData) {
    try {
      const query = `
        UPDATE products SET
          name = @name,
          barcode = @barcode,
          description = @description,
          category_id = @category_id,
          supplier_id = @supplier_id,
          default_cost_price = @default_cost_price,
          default_selling_price = @default_selling_price,
          min_selling_price = @min_selling_price
        WHERE id = @id
      `;
      
      const params = {
        id,
        name: productData.name,
        barcode: productData.barcode || null,
        description: productData.description || null,
        category_id: parseInt(productData.category_id),
        supplier_id: productData.supplier_id ? parseInt(productData.supplier_id) : null,
        default_cost_price: parseFloat(productData.default_cost_price),
        default_selling_price: parseFloat(productData.default_selling_price),
        min_selling_price: parseFloat(productData.min_selling_price)
      };
      
      await executeQuery(query, params);
      return true;
    } catch (error) {
      throw new Error(`Database error in update: ${error.message}`);
    }
  }

  // Delete product
  async delete(id) {
    try {
      const query = `DELETE FROM products WHERE id = @id`;
      await executeQuery(query, { id });
      return true;
    } catch (error) {
      throw new Error(`Database error in delete: ${error.message}`);
    }
  }

  // Check if product has transactions
  async hasTransactions(id) {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM (
          SELECT product_id FROM purchase_invoice_details WHERE product_id = @id
          UNION ALL
          SELECT product_id FROM sales_invoice_details WHERE product_id = @id
          UNION ALL
          SELECT product_id FROM inventory_logs WHERE product_id = @id
        ) t
      `;
      
      const result = await executeQuery(query, { id });
      return result.recordset[0].count > 0;
    } catch (error) {
      throw new Error(`Database error in hasTransactions: ${error.message}`);
    }
  }

  // Find low stock products
  async findLowStock(threshold = 10) {
    try {
      const query = `
        SELECT 
          p.id,
          p.name,
          p.barcode,
          c.name as category_name,
          ISNULL(iv.current_quantity, 0) as current_quantity
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory_view iv ON p.id = iv.product_id
        WHERE ISNULL(iv.current_quantity, 0) <= @threshold
        ORDER BY ISNULL(iv.current_quantity, 0) ASC
      `;
      
      const result = await executeQuery(query, { threshold });
      return result.recordset;
    } catch (error) {
      throw new Error(`Database error in findLowStock: ${error.message}`);
    }
  }

  // Get product statistics
  async getStatistics() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_products,
          COUNT(CASE WHEN iv.current_quantity > 0 THEN 1 END) as products_in_stock,
          COUNT(CASE WHEN ISNULL(iv.current_quantity, 0) <= 10 THEN 1 END) as low_stock_products,
          ISNULL(SUM(iv.current_quantity * p.default_cost_price), 0) as total_stock_value,
          ISNULL(AVG(p.default_selling_price), 0) as avg_selling_price,
          ISNULL(AVG(p.default_cost_price), 0) as avg_cost_price
        FROM products p
        LEFT JOIN inventory_view iv ON p.id = iv.product_id
      `;
      
      const result = await executeQuery(query);
      return result.recordset[0];
    } catch (error) {
      throw new Error(`Database error in getStatistics: ${error.message}`);
    }
  }

  // Search products by name or barcode
  async search(searchTerm, limit = 20) {
    try {
      const query = `
        SELECT TOP (@limit)
          p.id,
          p.name,
          p.barcode,
          p.default_selling_price,
          ISNULL(iv.current_quantity, 0) as stock,
          c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory_view iv ON p.id = iv.product_id
        WHERE p.name LIKE @search OR p.barcode LIKE @search
        ORDER BY p.name
      `;
      
      const result = await executeQuery(query, { 
        search: `%${searchTerm}%`,
        limit 
      });
      return result.recordset;
    } catch (error) {
      throw new Error(`Database error in search: ${error.message}`);
    }
  }
}

module.exports = ProductRepository;
