{"version": 3, "file": "timeout-error.js", "names": ["TimeoutError", "Error", "constructor", "code", "name", "exports", "default"], "sources": ["../../src/errors/timeout-error.ts"], "sourcesContent": ["export default class TimeoutError extends Error {\n  declare code: string;\n\n  constructor() {\n    super('The operation was aborted due to timeout');\n\n    this.code = 'TIMEOUT_ERR';\n    this.name = 'TimeoutError';\n  }\n}\n"], "mappings": ";;;;;;AAAe,MAAMA,YAAY,SAASC,KAAK,CAAC;EAG9CC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,0CAA0C,CAAC;IAEjD,IAAI,CAACC,IAAI,GAAG,aAAa;IACzB,IAAI,CAACC,IAAI,GAAG,cAAc;EAC5B;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAN,YAAA"}