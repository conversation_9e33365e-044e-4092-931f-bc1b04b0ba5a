{"version": 3, "file": "char.js", "names": ["_iconvLite", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Char", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "output", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "generateParameterData", "validate", "TypeError", "Error", "codepage", "iconv", "encode", "_default", "exports", "module"], "sources": ["../../src/data-types/char.ts"], "sourcesContent": ["import iconv from 'iconv-lite';\nimport { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\n\nconst Char: { maximumLength: number } & DataType = {\n  id: 0xAF,\n  type: '<PERSON><PERSON><PERSON><PERSON>',\n  name: '<PERSON><PERSON>',\n  maximumLength: 8000,\n\n  declaration: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (value != null) {\n      length = value.length || 1;\n    } else if (value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    if (length < this.maximumLength) {\n      return 'char(' + length + ')';\n    } else {\n      return 'char(' + this.maximumLength + ')';\n    }\n  },\n\n  // ParameterData<any> is temporary solution. TODO: need to understand what type ParameterData<...> can be.\n  resolveLength: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    if (parameter.length != null) {\n      return parameter.length;\n    } else if (value != null) {\n      return value.length || 1;\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    const buffer = Buffer.alloc(8);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeUInt16LE(parameter.length!, 1);\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    const value = parameter.value as Buffer | null;\n\n    if (value == null) {\n      return NULL_LENGTH;\n    }\n\n    const buffer = Buffer.alloc(2);\n    buffer.writeUInt16LE(value.length, 0);\n    return buffer;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield Buffer.from(parameter.value, 'ascii');\n  },\n\n  validate: function(value, collation): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    if (!collation) {\n      throw new Error('No collation was set by the server for the current connection.');\n    }\n\n    if (!collation.codepage) {\n      throw new Error('The collation set by the server has no associated encoding.');\n    }\n\n    return iconv.encode(value, collation.codepage);\n  }\n};\n\nexport default Char;\nmodule.exports = Char;\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA+B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAG/B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE7C,MAAMC,IAA0C,GAAG;EACjDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxBC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MAC9CD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,IAAII,MAAM,GAAG,IAAI,CAACJ,aAAa,EAAE;MAC/B,OAAO,OAAO,GAAGI,MAAM,GAAG,GAAG;IAC/B,CAAC,MAAM;MACL,OAAO,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAG,GAAG;IAC3C;EACF,CAAC;EAED;EACAM,aAAa,EAAE,SAAAA,CAASJ,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAID,SAAS,CAACE,MAAM,IAAI,IAAI,EAAE;MAC5B,OAAOF,SAAS,CAACE,MAAM;IACzB,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAOA,KAAK,CAACC,MAAM,IAAI,CAAC;IAC1B,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDO,gBAAgBA,CAACL,SAAS,EAAE;IAC1B,MAAMM,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAC7BW,MAAM,CAACG,aAAa,CAACT,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAE1C,IAAIF,SAAS,CAACU,SAAS,EAAE;MACvBV,SAAS,CAACU,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACb,SAAS,EAAEc,OAAO,EAAE;IAC1C,MAAMb,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOV,WAAW;IACpB;IAEA,MAAMe,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACG,aAAa,CAACR,KAAK,CAACC,MAAM,EAAE,CAAC,CAAC;IACrC,OAAOI,MAAM;EACf,CAAC;EAED,CAAES,qBAAqBA,CAACf,SAAS,EAAEc,OAAO,EAAE;IAC1C,IAAId,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMT,MAAM,CAACC,IAAI,CAACO,SAAS,CAACC,KAAK,EAAE,OAAO,CAAC;EAC7C,CAAC;EAEDe,QAAQ,EAAE,SAAAA,CAASf,KAAK,EAAES,SAAS,EAAiB;IAClD,IAAIT,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIgB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAI,CAACP,SAAS,EAAE;MACd,MAAM,IAAIQ,KAAK,CAAC,gEAAgE,CAAC;IACnF;IAEA,IAAI,CAACR,SAAS,CAACS,QAAQ,EAAE;MACvB,MAAM,IAAID,KAAK,CAAC,6DAA6D,CAAC;IAChF;IAEA,OAAOE,kBAAK,CAACC,MAAM,CAACpB,KAAK,EAAES,SAAS,CAACS,QAAQ,CAAC;EAChD;AACF,CAAC;AAAC,IAAAG,QAAA,GAEa5B,IAAI;AAAA6B,OAAA,CAAAjC,OAAA,GAAAgC,QAAA;AACnBE,MAAM,CAACD,OAAO,GAAG7B,IAAI"}