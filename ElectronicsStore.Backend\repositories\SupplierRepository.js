const { executeQuery } = require('../config/database-sqlserver');

class SupplierRepository {
  
  // Find all suppliers
  async findAll() {
    try {
      const query = `SELECT id, name, phone, email, address FROM suppliers ORDER BY name`;
      const result = await executeQuery(query);
      return result.recordset;
    } catch (error) {
      throw new Error(`Database error in findAll: ${error.message}`);
    }
  }

  // Find supplier by ID
  async findById(id) {
    try {
      const query = `SELECT id, name, phone, email, address FROM suppliers WHERE id = @id`;
      const result = await executeQuery(query, { id });
      return result.recordset[0] || null;
    } catch (error) {
      throw new Error(`Database error in findById: ${error.message}`);
    }
  }

  // Create new supplier
  async create(supplierData) {
    try {
      const query = `
        INSERT INTO suppliers (name, phone, email, address)
        OUTPUT INSERTED.id
        VALUES (@name, @phone, @email, @address)
      `;
      
      const params = {
        name: supplierData.name,
        phone: supplierData.phone || null,
        email: supplierData.email || null,
        address: supplierData.address || null
      };
      
      const result = await executeQuery(query, params);
      return { id: result.recordset[0].id };
    } catch (error) {
      throw new Error(`Database error in create: ${error.message}`);
    }
  }

  // Update supplier
  async update(id, supplierData) {
    try {
      const query = `
        UPDATE suppliers SET 
          name = @name, 
          phone = @phone, 
          email = @email, 
          address = @address 
        WHERE id = @id
      `;
      
      const params = {
        id,
        name: supplierData.name,
        phone: supplierData.phone || null,
        email: supplierData.email || null,
        address: supplierData.address || null
      };
      
      await executeQuery(query, params);
      return true;
    } catch (error) {
      throw new Error(`Database error in update: ${error.message}`);
    }
  }

  // Delete supplier
  async delete(id) {
    try {
      const query = `DELETE FROM suppliers WHERE id = @id`;
      await executeQuery(query, { id });
      return true;
    } catch (error) {
      throw new Error(`Database error in delete: ${error.message}`);
    }
  }

  // Check if supplier has products
  async hasProducts(id) {
    try {
      const query = `SELECT COUNT(*) as count FROM products WHERE supplier_id = @id`;
      const result = await executeQuery(query, { id });
      return result.recordset[0].count > 0;
    } catch (error) {
      throw new Error(`Database error in hasProducts: ${error.message}`);
    }
  }
}

module.exports = SupplierRepository;
