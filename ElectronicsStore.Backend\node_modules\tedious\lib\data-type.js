"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.typeByName = exports.TYPES = exports.TYPE = void 0;
var _null = _interopRequireDefault(require("./data-types/null"));
var _tinyint = _interopRequireDefault(require("./data-types/tinyint"));
var _bit = _interopRequireDefault(require("./data-types/bit"));
var _smallint = _interopRequireDefault(require("./data-types/smallint"));
var _int = _interopRequireDefault(require("./data-types/int"));
var _smalldatetime = _interopRequireDefault(require("./data-types/smalldatetime"));
var _real = _interopRequireDefault(require("./data-types/real"));
var _money = _interopRequireDefault(require("./data-types/money"));
var _datetime = _interopRequireDefault(require("./data-types/datetime"));
var _float = _interopRequireDefault(require("./data-types/float"));
var _decimal = _interopRequireDefault(require("./data-types/decimal"));
var _numeric = _interopRequireDefault(require("./data-types/numeric"));
var _smallmoney = _interopRequireDefault(require("./data-types/smallmoney"));
var _bigint = _interopRequireDefault(require("./data-types/bigint"));
var _image = _interopRequireDefault(require("./data-types/image"));
var _text = _interopRequireDefault(require("./data-types/text"));
var _uniqueidentifier = _interopRequireDefault(require("./data-types/uniqueidentifier"));
var _intn = _interopRequireDefault(require("./data-types/intn"));
var _ntext = _interopRequireDefault(require("./data-types/ntext"));
var _bitn = _interopRequireDefault(require("./data-types/bitn"));
var _decimaln = _interopRequireDefault(require("./data-types/decimaln"));
var _numericn = _interopRequireDefault(require("./data-types/numericn"));
var _floatn = _interopRequireDefault(require("./data-types/floatn"));
var _moneyn = _interopRequireDefault(require("./data-types/moneyn"));
var _datetimen = _interopRequireDefault(require("./data-types/datetimen"));
var _varbinary = _interopRequireDefault(require("./data-types/varbinary"));
var _varchar = _interopRequireDefault(require("./data-types/varchar"));
var _binary = _interopRequireDefault(require("./data-types/binary"));
var _char = _interopRequireDefault(require("./data-types/char"));
var _nvarchar = _interopRequireDefault(require("./data-types/nvarchar"));
var _nchar = _interopRequireDefault(require("./data-types/nchar"));
var _xml = _interopRequireDefault(require("./data-types/xml"));
var _time = _interopRequireDefault(require("./data-types/time"));
var _date = _interopRequireDefault(require("./data-types/date"));
var _datetime2 = _interopRequireDefault(require("./data-types/datetime2"));
var _datetimeoffset = _interopRequireDefault(require("./data-types/datetimeoffset"));
var _udt = _interopRequireDefault(require("./data-types/udt"));
var _tvp = _interopRequireDefault(require("./data-types/tvp"));
var _sqlVariant = _interopRequireDefault(require("./data-types/sql-variant"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const TYPE = {
  [_null.default.id]: _null.default,
  [_tinyint.default.id]: _tinyint.default,
  [_bit.default.id]: _bit.default,
  [_smallint.default.id]: _smallint.default,
  [_int.default.id]: _int.default,
  [_smalldatetime.default.id]: _smalldatetime.default,
  [_real.default.id]: _real.default,
  [_money.default.id]: _money.default,
  [_datetime.default.id]: _datetime.default,
  [_float.default.id]: _float.default,
  [_decimal.default.id]: _decimal.default,
  [_numeric.default.id]: _numeric.default,
  [_smallmoney.default.id]: _smallmoney.default,
  [_bigint.default.id]: _bigint.default,
  [_image.default.id]: _image.default,
  [_text.default.id]: _text.default,
  [_uniqueidentifier.default.id]: _uniqueidentifier.default,
  [_intn.default.id]: _intn.default,
  [_ntext.default.id]: _ntext.default,
  [_bitn.default.id]: _bitn.default,
  [_decimaln.default.id]: _decimaln.default,
  [_numericn.default.id]: _numericn.default,
  [_floatn.default.id]: _floatn.default,
  [_moneyn.default.id]: _moneyn.default,
  [_datetimen.default.id]: _datetimen.default,
  [_varbinary.default.id]: _varbinary.default,
  [_varchar.default.id]: _varchar.default,
  [_binary.default.id]: _binary.default,
  [_char.default.id]: _char.default,
  [_nvarchar.default.id]: _nvarchar.default,
  [_nchar.default.id]: _nchar.default,
  [_xml.default.id]: _xml.default,
  [_time.default.id]: _time.default,
  [_date.default.id]: _date.default,
  [_datetime2.default.id]: _datetime2.default,
  [_datetimeoffset.default.id]: _datetimeoffset.default,
  [_udt.default.id]: _udt.default,
  [_tvp.default.id]: _tvp.default,
  [_sqlVariant.default.id]: _sqlVariant.default
};

/**
 * <table>
 * <thead>
 *   <tr>
 *     <th>Type</th>
 *     <th>Constant</th>
 *     <th>JavaScript</th>
 *     <th>Result set</th>
 *     <th>Parameter</th>
 *   </tr>
 * </thead>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="5">Exact numerics</th>
 *   </tr>
 *   <tr>
 *     <td><code>bit</code></td>
 *     <td><code>[[TYPES.Bit]]</code></td>
 *     <td><code>boolean</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>tinyint</code></td>
 *     <td><code>[[TYPES.TinyInt]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>smallint</code></td>
 *     <td><code>[[TYPES.SmallInt]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>int</code></td>
 *     <td><code>[[TYPES.Int]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>bigint</code><sup>1</sup></td>
 *     <td><code>[[TYPES.BigInt]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>numeric</code><sup>2</sup></td>
 *     <td><code>[[TYPES.Numeric]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>decimal</code><sup>2</sup></td>
 *     <td><code>[[TYPES.Decimal]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>smallmoney</code></td>
 *     <td><code>[[TYPES.SmallMoney]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>money</code></td>
 *     <td><code>[[TYPES.Money]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="5">Approximate numerics</th>
 *   </tr>
 *   <tr>
 *     <td><code>float</code></td>
 *     <td><code>[[TYPES.Float]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>real</code></td>
 *     <td><code>[[TYPES.Real]]</code></td>
 *     <td><code>number</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="4">Date and Time</th>
 *   </tr>
 *   <tr>
 *     <td><code>smalldatetime</code></td>
 *     <td><code>[[TYPES.SmallDateTime]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>datetime</code></td>
 *     <td><code>[[TYPES.DateTime]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>datetime2</code></td>
 *     <td><code>[[TYPES.DateTime2]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>datetimeoffset</code></td>
 *     <td><code>[[TYPES.DateTimeOffset]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>time</code></td>
 *     <td><code>[[TYPES.Time]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>date</code></td>
 *     <td><code>[[TYPES.Date]]</code></td>
 *     <td><code>Date</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="4">Character Strings</th>
 *   </tr>
 *   <tr>
 *     <td><code>char</code></td>
 *     <td><code>[[TYPES.Char]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>varchar</code><sup>3</sup></td>
 *     <td><code>[[TYPES.VarChar]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>text</code></td>
 *     <td><code>[[TYPES.Text]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="4">Unicode Strings</th>
 *   </tr>
 *   <tr>
 *     <td><code>nchar</code></td>
 *     <td><code>[[TYPES.NChar]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>nvarchar</code><sup>3</sup></td>
 *     <td><code>[[TYPES.NVarChar]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>ntext</code></td>
 *     <td><code>[[TYPES.NText]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>-</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="5">Binary Strings<sup>4</sup></th>
 *   </tr>
 *   <tr>
 *     <td><code>binary</code></td>
 *     <td><code>[[TYPES.Binary]]</code></td>
 *     <td><code>Buffer</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>varbinary</code></td>
 *     <td><code>[[TYPES.VarBinary]]</code></td>
 *     <td><code>Buffer</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>image</code></td>
 *     <td><code>[[TYPES.Image]]</code></td>
 *     <td><code>Buffer</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 * </tbody>
 *
 * <tbody>
 *   <tr class="group-heading">
 *     <th colspan="5">Other Data Types</th>
 *   </tr>
 *   <tr>
 *     <td><code>TVP</code></td>
 *     <td><code>[[TYPES.TVP]]</code></td>
 *     <td><code>Object</code></td>
 *     <td>-</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>UDT</code></td>
 *     <td><code>[[TYPES.UDT]]</code></td>
 *     <td><code>Buffer</code></td>
 *     <td>✓</td>
 *     <td>-</td>
 *   </tr>
 *   <tr>
 *     <td><code>uniqueidentifier</code><sup>4</sup></td>
 *     <td><code>[[TYPES.UniqueIdentifier]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>✓</td>
 *   </tr>
 *   <tr>
 *     <td><code>variant</code></td>
 *     <td><code>[[TYPES.Variant]]</code></td>
 *     <td><code>any</code></td>
 *     <td>✓</td>
 *     <td>-</td>
 *   </tr>
 *   <tr>
 *     <td><code>xml</code></td>
 *     <td><code>[[TYPES.Xml]]</code></td>
 *     <td><code>string</code></td>
 *     <td>✓</td>
 *     <td>-</td>
 *   </tr>
 * </tbody>
 * </table>
 *
 * <ol>
 *   <li>
 *     <h4>BigInt</h4>
 *     <p>
 *       Values are returned as a string. This is because values can exceed 53 bits of significant data, which is greater than a
 *       Javascript <code>number</code> type can represent as an integer.
 *     </p>
 *   </li>
 *   <li>
 *     <h4>Numerical, Decimal</h4>
 *     <p>
 *       For input parameters, default precision is 18 and default scale is 0. Maximum supported precision is 19.
 *     </p>
 *   </li>
 *   <li>
 *     <h4>VarChar, NVarChar</h4>
 *     <p>
 *       <code>varchar(max)</code> and <code>nvarchar(max)</code> are also supported.
 *     </p>
 *   </li>
 *   <li>
 *     <h4>UniqueIdentifier</h4>
 *     <p>
 *       Values are returned as a 16 byte hexadecimal string.
 *     </p>
 *     <p>
 *       Note that the order of bytes is not the same as the character representation. See
 *       <a href="http://msdn.microsoft.com/en-us/library/ms190215.aspx">Using uniqueidentifier Data</a>
 *       for an example of the different ordering of bytes.
 *     </p>
 *   </li>
 * </ol>
 */
exports.TYPE = TYPE;
const TYPES = {
  TinyInt: _tinyint.default,
  Bit: _bit.default,
  SmallInt: _smallint.default,
  Int: _int.default,
  SmallDateTime: _smalldatetime.default,
  Real: _real.default,
  Money: _money.default,
  DateTime: _datetime.default,
  Float: _float.default,
  Decimal: _decimal.default,
  Numeric: _numeric.default,
  SmallMoney: _smallmoney.default,
  BigInt: _bigint.default,
  Image: _image.default,
  Text: _text.default,
  UniqueIdentifier: _uniqueidentifier.default,
  NText: _ntext.default,
  VarBinary: _varbinary.default,
  VarChar: _varchar.default,
  Binary: _binary.default,
  Char: _char.default,
  NVarChar: _nvarchar.default,
  NChar: _nchar.default,
  Xml: _xml.default,
  Time: _time.default,
  Date: _date.default,
  DateTime2: _datetime2.default,
  DateTimeOffset: _datetimeoffset.default,
  UDT: _udt.default,
  TVP: _tvp.default,
  Variant: _sqlVariant.default
};
exports.TYPES = TYPES;
const typeByName = TYPES;
exports.typeByName = typeByName;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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