{"version": 3, "file": "keyCredentialAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../src/keyCredentialAuthenticationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAUlC;;GAEG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,mCAAmC,CAAC;AAEzF,MAAM,UAAU,iCAAiC,CAC/C,UAAyB,EACzB,gBAAwB;IAExB,OAAO;QACL,IAAI,EAAE,qCAAqC;QAC3C,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { KeyCredential } from \"@azure/core-auth\";\nimport type {\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\n\n/**\n * The programmatic identifier of the bearerTokenAuthenticationPolicy.\n */\nexport const keyCredentialAuthenticationPolicyName = \"keyCredentialAuthenticationPolicy\";\n\nexport function keyCredentialAuthenticationPolicy(\n  credential: KeyCredential,\n  apiKeyHeaderName: string,\n): PipelinePolicy {\n  return {\n    name: keyCredentialAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      request.headers.set(apiKeyHeaderName, credential.key);\n      return next(request);\n    },\n  };\n}\n"]}