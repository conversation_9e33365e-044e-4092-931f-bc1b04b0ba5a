import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as CartIcon,
  Person as PersonIcon,
  Print as PrintIcon,
} from '@mui/icons-material';
import PageContainer from '../components/PageContainer';
import DashboardHeader from '../components/DashboardHeader';

interface Sale {
  id: number;
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  items: SaleItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  paymentMethod: 'cash' | 'card' | 'transfer';
  saleDate: string;
  salesPerson: string;
}

interface SaleItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

const Sales: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);

  // Mock data
  useEffect(() => {
    const mockSales: Sale[] = [
      {
        id: 1,
        orderNumber: 'ORD-2024-001',
        customerName: 'أحمد محمد علي',
        customerEmail: '<EMAIL>',
        items: [
          { productId: 1, productName: 'iPhone 15 Pro', quantity: 1, unitPrice: 4500, total: 4500 },
          { productId: 4, productName: 'AirPods Pro', quantity: 1, unitPrice: 950, total: 950 },
        ],
        subtotal: 5450,
        tax: 817.5,
        discount: 200,
        total: 6067.5,
        status: 'completed',
        paymentMethod: 'card',
        saleDate: '2024-01-20T10:30:00',
        salesPerson: 'محمد أحمد',
      },
      {
        id: 2,
        orderNumber: 'ORD-2024-002',
        customerName: 'فاطمة أحمد السالم',
        customerEmail: '<EMAIL>',
        items: [
          { productId: 2, productName: 'Samsung Galaxy S24', quantity: 1, unitPrice: 3800, total: 3800 },
        ],
        subtotal: 3800,
        tax: 570,
        discount: 0,
        total: 4370,
        status: 'completed',
        paymentMethod: 'transfer',
        saleDate: '2024-01-19T14:15:00',
        salesPerson: 'سارة خالد',
      },
      {
        id: 3,
        orderNumber: 'ORD-2024-003',
        customerName: 'محمد عبدالله الغامدي',
        customerEmail: '<EMAIL>',
        items: [
          { productId: 3, productName: 'MacBook Pro M3', quantity: 1, unitPrice: 8500, total: 8500 },
        ],
        subtotal: 8500,
        tax: 1275,
        discount: 500,
        total: 9275,
        status: 'pending',
        paymentMethod: 'cash',
        saleDate: '2024-01-19T16:45:00',
        salesPerson: 'أحمد سعد',
      },
      {
        id: 4,
        orderNumber: 'ORD-2024-004',
        customerName: 'نورا سعد الحربي',
        customerEmail: '<EMAIL>',
        items: [
          { productId: 5, productName: 'Dell XPS 13', quantity: 1, unitPrice: 4200, total: 4200 },
        ],
        subtotal: 4200,
        tax: 630,
        discount: 100,
        total: 4730,
        status: 'cancelled',
        paymentMethod: 'card',
        saleDate: '2024-01-18T11:20:00',
        salesPerson: 'علي محمد',
      },
      {
        id: 5,
        orderNumber: 'ORD-2024-005',
        customerName: 'خالد عبدالرحمن القحطاني',
        customerEmail: '<EMAIL>',
        items: [
          { productId: 4, productName: 'AirPods Pro', quantity: 2, unitPrice: 950, total: 1900 },
        ],
        subtotal: 1900,
        tax: 285,
        discount: 50,
        total: 2135,
        status: 'refunded',
        paymentMethod: 'transfer',
        saleDate: '2024-01-17T09:10:00',
        salesPerson: 'فهد أحمد',
      },
    ];

    setTimeout(() => {
      setSales(mockSales);
      setLoading(false);
    }, 1000);
  }, []);

  const statuses = ['all', 'pending', 'completed', 'cancelled', 'refunded'];
  const dateFilters = ['all', 'today', 'week', 'month'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'error';
      case 'refunded': return 'info';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتملة';
      case 'pending': return 'قيد الانتظار';
      case 'cancelled': return 'ملغية';
      case 'refunded': return 'مسترد';
      default: return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقدي';
      case 'card': return 'بطاقة';
      case 'transfer': return 'تحويل';
      default: return method;
    }
  };

  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customerEmail.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || sale.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA');
  };

  const calculateTotalSales = () => {
    return sales.filter(s => s.status === 'completed').reduce((sum, sale) => sum + sale.total, 0);
  };

  const calculateTotalOrders = () => {
    return sales.length;
  };

  const calculatePendingOrders = () => {
    return sales.filter(s => s.status === 'pending').length;
  };

  const calculateCompletedOrders = () => {
    return sales.filter(s => s.status === 'completed').length;
  };

  const handleAddSale = () => {
    setSelectedSale(null);
    setOpenDialog(true);
  };

  const handleViewSale = (sale: Sale) => {
    setSelectedSale(sale);
    setOpenDialog(true);
  };

  const handleDeleteSale = (saleId: number) => {
    setSales(sales.filter(s => s.id !== saleId));
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Typography variant="h6">جاري تحميل المبيعات...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <DashboardHeader
        title="إدارة المبيعات"
        subtitle="عرض وإدارة جميع مبيعات المتجر"
        showBreadcrumbs={true}
        showLastUpdate={true}
      />

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="success.main">
                    {formatCurrency(calculateTotalSales())}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المبيعات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <MoneyIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="primary.main">
                    {calculateTotalOrders()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <CartIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {calculatePendingOrders()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    طلبات قيد الانتظار
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <ReceiptIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="info.main">
                    {calculateCompletedOrders()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    طلبات مكتملة
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TrendingUpIcon />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="البحث في المبيعات (رقم الطلب، اسم العميل، البريد الإلكتروني)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="الحالة"
              >
                {statuses.map(status => (
                  <MenuItem key={status} value={status}>
                    {status === 'all' ? 'جميع الحالات' : getStatusText(status)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>التاريخ</InputLabel>
              <Select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                label="التاريخ"
              >
                <MenuItem value="all">جميع التواريخ</MenuItem>
                <MenuItem value="today">اليوم</MenuItem>
                <MenuItem value="week">هذا الأسبوع</MenuItem>
                <MenuItem value="month">هذا الشهر</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Card>

      {/* Sales Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>رقم الطلب</TableCell>
              <TableCell>العميل</TableCell>
              <TableCell>المنتجات</TableCell>
              <TableCell>المبلغ الإجمالي</TableCell>
              <TableCell>طريقة الدفع</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>التاريخ</TableCell>
              <TableCell>موظف المبيعات</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredSales.map((sale) => (
              <TableRow key={sale.id}>
                <TableCell>
                  <Typography variant="subtitle2" color="primary">
                    {sale.orderNumber}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">{sale.customerName}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {sale.customerEmail}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {sale.items.length} منتج
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {sale.items.reduce((sum, item) => sum + item.quantity, 0)} قطعة
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" color="success.main">
                    {formatCurrency(sale.total)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {getPaymentMethodText(sale.paymentMethod)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusText(sale.status)}
                    color={getStatusColor(sale.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDateTime(sale.saleDate)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {sale.salesPerson}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="عرض">
                      <IconButton size="small" onClick={() => handleViewSale(sale)}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="طباعة">
                      <IconButton size="small">
                        <PrintIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف">
                      <IconButton size="small" color="error" onClick={() => handleDeleteSale(sale.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Sale FAB */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={handleAddSale}
      >
        <AddIcon />
      </Fab>

      {/* Sale Details Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedSale ? `تفاصيل الطلب ${selectedSale.orderNumber}` : 'إضافة طلب جديد'}
        </DialogTitle>
        <DialogContent>
          {selectedSale ? (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>معلومات العميل:</Typography>
                  <Typography variant="body2">{selectedSale.customerName}</Typography>
                  <Typography variant="body2" color="text.secondary">{selectedSale.customerEmail}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>معلومات الطلب:</Typography>
                  <Typography variant="body2">التاريخ: {formatDateTime(selectedSale.saleDate)}</Typography>
                  <Typography variant="body2">موظف المبيعات: {selectedSale.salesPerson}</Typography>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle2" gutterBottom>المنتجات:</Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>المنتج</TableCell>
                    <TableCell>الكمية</TableCell>
                    <TableCell>السعر</TableCell>
                    <TableCell>الإجمالي</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedSale.items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.productName}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                      <TableCell>{formatCurrency(item.total)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Typography variant="body2">المجموع الفرعي: {formatCurrency(selectedSale.subtotal)}</Typography>
                <Typography variant="body2">الضريبة: {formatCurrency(selectedSale.tax)}</Typography>
                <Typography variant="body2">الخصم: {formatCurrency(selectedSale.discount)}</Typography>
                <Typography variant="h6" color="primary">الإجمالي: {formatCurrency(selectedSale.total)}</Typography>
              </Box>
            </Box>
          ) : (
            <Typography>نموذج إضافة طلب جديد سيتم إضافته لاحقاً</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>إغلاق</Button>
          {!selectedSale && <Button variant="contained">حفظ</Button>}
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default Sales;
