const { executeQuery } = require('../config/database-sqlserver');

class CategoryRepository {
  
  // Find all categories
  async findAll() {
    try {
      const query = `SELECT id, name FROM categories ORDER BY name`;
      const result = await executeQuery(query);
      return result.recordset;
    } catch (error) {
      throw new Error(`Database error in findAll: ${error.message}`);
    }
  }

  // Find category by ID
  async findById(id) {
    try {
      const query = `SELECT id, name FROM categories WHERE id = @id`;
      const result = await executeQuery(query, { id });
      return result.recordset[0] || null;
    } catch (error) {
      throw new Error(`Database error in findById: ${error.message}`);
    }
  }

  // Create new category
  async create(categoryData) {
    try {
      const query = `
        INSERT INTO categories (name)
        OUTPUT INSERTED.id
        VALUES (@name)
      `;
      
      const result = await executeQuery(query, { name: categoryData.name });
      return { id: result.recordset[0].id };
    } catch (error) {
      throw new Error(`Database error in create: ${error.message}`);
    }
  }

  // Update category
  async update(id, categoryData) {
    try {
      const query = `UPDATE categories SET name = @name WHERE id = @id`;
      await executeQuery(query, { id, name: categoryData.name });
      return true;
    } catch (error) {
      throw new Error(`Database error in update: ${error.message}`);
    }
  }

  // Delete category
  async delete(id) {
    try {
      const query = `DELETE FROM categories WHERE id = @id`;
      await executeQuery(query, { id });
      return true;
    } catch (error) {
      throw new Error(`Database error in delete: ${error.message}`);
    }
  }

  // Check if category has products
  async hasProducts(id) {
    try {
      const query = `SELECT COUNT(*) as count FROM products WHERE category_id = @id`;
      const result = await executeQuery(query, { id });
      return result.recordset[0].count > 0;
    } catch (error) {
      throw new Error(`Database error in hasProducts: ${error.message}`);
    }
  }
}

module.exports = CategoryRepository;
