{"version": 3, "file": "float.js", "names": ["_floatn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Float", "id", "type", "name", "declaration", "generateTypeInfo", "FloatN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeDoubleLE", "parseFloat", "validate", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/float.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport FloatN from './floatn';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst Float: DataType = {\n  id: 0x3E,\n  type: 'FLT8',\n  name: 'Float',\n\n  declaration: function() {\n    return 'float';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([FloatN.id, 0x08]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return Buffer.from([0x08]);\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(8);\n    buffer.writeDoubleLE(parseFloat(parameter.value), 0);\n    yield buffer;\n  },\n\n  validate: function(value): number | null {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    return value;\n  }\n};\n\nexport default Float;\nmodule.exports = Float;\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,KAAe,GAAG;EACtBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,OAAO;EAEbC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,OAAO;EAChB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOP,MAAM,CAACC,IAAI,CAAC,CAACO,eAAM,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACvC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOb,WAAW;IACpB;IAEA,OAAOC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EAC5B,CAAC;EAED,CAAEY,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,aAAa,CAACC,UAAU,CAACP,SAAS,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC;IACpD,MAAME,MAAM;EACd,CAAC;EAEDI,QAAQ,EAAE,SAAAA,CAASN,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGK,UAAU,CAACL,KAAK,CAAC;IACzB,IAAIO,KAAK,CAACP,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIQ,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOR,KAAK;EACd;AACF,CAAC;AAAC,IAAAS,QAAA,GAEanB,KAAK;AAAAoB,OAAA,CAAAxB,OAAA,GAAAuB,QAAA;AACpBE,MAAM,CAACD,OAAO,GAAGpB,KAAK"}