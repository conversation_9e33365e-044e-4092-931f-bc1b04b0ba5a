{"version": 3, "file": "writable-tracking-buffer.js", "names": ["SHIFT_LEFT_32", "SHIFT_RIGHT_32", "UNKNOWN_PLP_LEN", "<PERSON><PERSON><PERSON>", "from", "ZERO_LENGTH_BUFFER", "alloc", "WritableTrackingBuffer", "constructor", "initialSize", "encoding", "doubleSizeGrowth", "buffer", "compositeBuffer", "position", "data", "new<PERSON>uffer", "copyFrom", "length", "makeRoomFor", "copy", "<PERSON><PERSON><PERSON><PERSON>", "size", "Math", "max", "slice", "concat", "writeUInt8", "value", "writeUInt16LE", "writeUShort", "writeUInt16BE", "writeUInt24LE", "writeUInt32LE", "writeBigInt64LE", "writeInt64LE", "BigInt", "writeUInt64LE", "writeBigUInt64LE", "writeUInt32BE", "writeUInt40LE", "writeInt32LE", "floor", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32BE", "writeFloatLE", "writeDoubleLE", "writeString", "byteLength", "write", "writeBVarchar", "writeUsVarchar", "writeUsVarbyte", "toString", "writeBuffer", "writePLPBody", "writeMoney", "_default", "exports", "default", "module"], "sources": ["../../src/tracking-buffer/writable-tracking-buffer.ts"], "sourcesContent": ["const SHIFT_LEFT_32 = (1 << 16) * (1 << 16);\nconst SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;\nconst UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);\nconst ZERO_LENGTH_BUFFER = Buffer.alloc(0);\n\nexport type Encoding = 'utf8' | 'ucs2' | 'ascii';\n\n/**\n  A Buffer-like class that tracks position.\n\n  As values are written, the position advances by the size of the written data.\n  When writing, automatically allocates new buffers if there's not enough space.\n */\nclass WritableTrackingBuffer {\n  declare initialSize: number;\n  declare encoding: Encoding;\n  declare doubleSizeGrowth: boolean;\n\n  declare buffer: Buffer;\n  declare compositeBuffer: Buffer;\n\n  declare position: number;\n\n  constructor(initialSize: number, encoding?: Encoding | null, doubleSizeGrowth?: boolean) {\n    this.initialSize = initialSize;\n    this.encoding = encoding || 'ucs2';\n    this.doubleSizeGrowth = doubleSizeGrowth || false;\n    this.buffer = Buffer.alloc(this.initialSize, 0);\n    this.compositeBuffer = ZERO_LENGTH_BUFFER;\n    this.position = 0;\n  }\n\n  get data() {\n    this.newBuffer(0);\n    return this.compositeBuffer;\n  }\n\n  copyFrom(buffer: Buffer) {\n    const length = buffer.length;\n    this.makeRoomFor(length);\n    buffer.copy(this.buffer, this.position);\n    this.position += length;\n  }\n\n  makeRoomFor(requiredLength: number) {\n    if (this.buffer.length - this.position < requiredLength) {\n      if (this.doubleSizeGrowth) {\n        let size = Math.max(128, this.buffer.length * 2);\n        while (size < requiredLength) {\n          size *= 2;\n        }\n        this.newBuffer(size);\n      } else {\n        this.newBuffer(requiredLength);\n      }\n    }\n  }\n\n  newBuffer(size: number) {\n    const buffer = this.buffer.slice(0, this.position);\n    this.compositeBuffer = Buffer.concat([this.compositeBuffer, buffer]);\n    this.buffer = (size === 0) ? ZERO_LENGTH_BUFFER : Buffer.alloc(size, 0);\n    this.position = 0;\n  }\n\n  writeUInt8(value: number) {\n    const length = 1;\n    this.makeRoomFor(length);\n    this.buffer.writeUInt8(value, this.position);\n    this.position += length;\n  }\n\n  writeUInt16LE(value: number) {\n    const length = 2;\n    this.makeRoomFor(length);\n    this.buffer.writeUInt16LE(value, this.position);\n    this.position += length;\n  }\n\n  writeUShort(value: number) {\n    this.writeUInt16LE(value);\n  }\n\n  writeUInt16BE(value: number) {\n    const length = 2;\n    this.makeRoomFor(length);\n    this.buffer.writeUInt16BE(value, this.position);\n    this.position += length;\n  }\n\n  writeUInt24LE(value: number) {\n    const length = 3;\n    this.makeRoomFor(length);\n    this.buffer[this.position + 2] = (value >>> 16) & 0xff;\n    this.buffer[this.position + 1] = (value >>> 8) & 0xff;\n    this.buffer[this.position] = value & 0xff;\n    this.position += length;\n  }\n\n  writeUInt32LE(value: number) {\n    const length = 4;\n    this.makeRoomFor(length);\n    this.buffer.writeUInt32LE(value, this.position);\n    this.position += length;\n  }\n\n  writeBigInt64LE(value: bigint) {\n    const length = 8;\n    this.makeRoomFor(length);\n    this.buffer.writeBigInt64LE(value, this.position);\n    this.position += length;\n  }\n\n  writeInt64LE(value: number) {\n    this.writeBigInt64LE(BigInt(value));\n  }\n\n  writeUInt64LE(value: number) {\n    this.writeBigUInt64LE(BigInt(value));\n  }\n\n  writeBigUInt64LE(value: bigint) {\n    const length = 8;\n    this.makeRoomFor(length);\n    this.buffer.writeBigUInt64LE(value, this.position);\n    this.position += length;\n  }\n\n  writeUInt32BE(value: number) {\n    const length = 4;\n    this.makeRoomFor(length);\n    this.buffer.writeUInt32BE(value, this.position);\n    this.position += length;\n  }\n\n  writeUInt40LE(value: number) {\n    // inspired by https://github.com/dpw/node-buffer-more-ints\n    this.writeInt32LE(value & -1);\n    this.writeUInt8(Math.floor(value * SHIFT_RIGHT_32));\n  }\n\n  writeInt8(value: number) {\n    const length = 1;\n    this.makeRoomFor(length);\n    this.buffer.writeInt8(value, this.position);\n    this.position += length;\n  }\n\n  writeInt16LE(value: number) {\n    const length = 2;\n    this.makeRoomFor(length);\n    this.buffer.writeInt16LE(value, this.position);\n    this.position += length;\n  }\n\n  writeInt16BE(value: number) {\n    const length = 2;\n    this.makeRoomFor(length);\n    this.buffer.writeInt16BE(value, this.position);\n    this.position += length;\n  }\n\n  writeInt32LE(value: number) {\n    const length = 4;\n    this.makeRoomFor(length);\n    this.buffer.writeInt32LE(value, this.position);\n    this.position += length;\n  }\n\n  writeInt32BE(value: number) {\n    const length = 4;\n    this.makeRoomFor(length);\n    this.buffer.writeInt32BE(value, this.position);\n    this.position += length;\n  }\n\n  writeFloatLE(value: number) {\n    const length = 4;\n    this.makeRoomFor(length);\n    this.buffer.writeFloatLE(value, this.position);\n    this.position += length;\n  }\n\n  writeDoubleLE(value: number) {\n    const length = 8;\n    this.makeRoomFor(length);\n    this.buffer.writeDoubleLE(value, this.position);\n    this.position += length;\n  }\n\n  writeString(value: string, encoding?: Encoding | null) {\n    if (encoding == null) {\n      encoding = this.encoding;\n    }\n\n    const length = Buffer.byteLength(value, encoding);\n    this.makeRoomFor(length);\n\n    // $FlowFixMe https://github.com/facebook/flow/pull/5398\n    this.buffer.write(value, this.position, encoding);\n    this.position += length;\n  }\n\n  writeBVarchar(value: string, encoding?: Encoding | null) {\n    this.writeUInt8(value.length);\n    this.writeString(value, encoding);\n  }\n\n  writeUsVarchar(value: string, encoding?: Encoding | null) {\n    this.writeUInt16LE(value.length);\n    this.writeString(value, encoding);\n  }\n\n  // TODO: Figure out what types are passed in other than `Buffer`\n  writeUsVarbyte(value: any, encoding?: Encoding | null) {\n    if (encoding == null) {\n      encoding = this.encoding;\n    }\n\n    let length;\n    if (value instanceof Buffer) {\n      length = value.length;\n    } else {\n      value = value.toString();\n      length = Buffer.byteLength(value, encoding);\n    }\n    this.writeUInt16LE(length);\n\n    if (value instanceof Buffer) {\n      this.writeBuffer(value);\n    } else {\n      this.makeRoomFor(length);\n      // $FlowFixMe https://github.com/facebook/flow/pull/5398\n      this.buffer.write(value, this.position, encoding);\n      this.position += length;\n    }\n  }\n\n  writePLPBody(value: any, encoding?: Encoding | null) {\n    if (encoding == null) {\n      encoding = this.encoding;\n    }\n\n    let length;\n    if (value instanceof Buffer) {\n      length = value.length;\n    } else {\n      value = value.toString();\n      length = Buffer.byteLength(value, encoding);\n    }\n\n    // Length of all chunks.\n    // this.writeUInt64LE(length);\n    // unknown seems to work better here - might revisit later.\n    this.writeBuffer(UNKNOWN_PLP_LEN);\n\n    // In the UNKNOWN_PLP_LEN case, the data is represented as a series of zero or more chunks.\n    if (length > 0) {\n      // One chunk.\n      this.writeUInt32LE(length);\n      if (value instanceof Buffer) {\n        this.writeBuffer(value);\n      } else {\n        this.makeRoomFor(length);\n        this.buffer.write(value, this.position, encoding);\n        this.position += length;\n      }\n    }\n\n    // PLP_TERMINATOR (no more chunks).\n    this.writeUInt32LE(0);\n  }\n\n  writeBuffer(value: Buffer) {\n    const length = value.length;\n    this.makeRoomFor(length);\n    value.copy(this.buffer, this.position);\n    this.position += length;\n  }\n\n  writeMoney(value: number) {\n    this.writeInt32LE(Math.floor(value * SHIFT_RIGHT_32));\n    this.writeInt32LE(value & -1);\n  }\n}\n\nexport default WritableTrackingBuffer;\nmodule.exports = WritableTrackingBuffer;\n"], "mappings": ";;;;;;AAAA,MAAMA,aAAa,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;AAC3C,MAAMC,cAAc,GAAG,CAAC,GAAGD,aAAa;AACxC,MAAME,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACrF,MAAMC,kBAAkB,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AAI1C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EAU3BC,WAAWA,CAACC,WAAmB,EAAEC,QAA0B,EAAEC,gBAA0B,EAAE;IACvF,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAI,MAAM;IAClC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB,IAAI,KAAK;IACjD,IAAI,CAACC,MAAM,GAAGT,MAAM,CAACG,KAAK,CAAC,IAAI,CAACG,WAAW,EAAE,CAAC,CAAC;IAC/C,IAAI,CAACI,eAAe,GAAGR,kBAAkB;IACzC,IAAI,CAACS,QAAQ,GAAG,CAAC;EACnB;EAEA,IAAIC,IAAIA,CAAA,EAAG;IACT,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IACjB,OAAO,IAAI,CAACH,eAAe;EAC7B;EAEAI,QAAQA,CAACL,MAAc,EAAE;IACvB,MAAMM,MAAM,GAAGN,MAAM,CAACM,MAAM;IAC5B,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxBN,MAAM,CAACQ,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE,IAAI,CAACE,QAAQ,CAAC;IACvC,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAC,WAAWA,CAACE,cAAsB,EAAE;IAClC,IAAI,IAAI,CAACT,MAAM,CAACM,MAAM,GAAG,IAAI,CAACJ,QAAQ,GAAGO,cAAc,EAAE;MACvD,IAAI,IAAI,CAACV,gBAAgB,EAAE;QACzB,IAAIW,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACZ,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;QAChD,OAAOI,IAAI,GAAGD,cAAc,EAAE;UAC5BC,IAAI,IAAI,CAAC;QACX;QACA,IAAI,CAACN,SAAS,CAACM,IAAI,CAAC;MACtB,CAAC,MAAM;QACL,IAAI,CAACN,SAAS,CAACK,cAAc,CAAC;MAChC;IACF;EACF;EAEAL,SAASA,CAACM,IAAY,EAAE;IACtB,MAAMV,MAAM,GAAG,IAAI,CAACA,MAAM,CAACa,KAAK,CAAC,CAAC,EAAE,IAAI,CAACX,QAAQ,CAAC;IAClD,IAAI,CAACD,eAAe,GAAGV,MAAM,CAACuB,MAAM,CAAC,CAAC,IAAI,CAACb,eAAe,EAAED,MAAM,CAAC,CAAC;IACpE,IAAI,CAACA,MAAM,GAAIU,IAAI,KAAK,CAAC,GAAIjB,kBAAkB,GAAGF,MAAM,CAACG,KAAK,CAACgB,IAAI,EAAE,CAAC,CAAC;IACvE,IAAI,CAACR,QAAQ,GAAG,CAAC;EACnB;EAEAa,UAAUA,CAACC,KAAa,EAAE;IACxB,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACe,UAAU,CAACC,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC5C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAW,aAAaA,CAACD,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACiB,aAAa,CAACD,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC/C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAY,WAAWA,CAACF,KAAa,EAAE;IACzB,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;EAC3B;EAEAG,aAAaA,CAACH,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACmB,aAAa,CAACH,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC/C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAc,aAAaA,CAACJ,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAAC,IAAI,CAACE,QAAQ,GAAG,CAAC,CAAC,GAAIc,KAAK,KAAK,EAAE,GAAI,IAAI;IACtD,IAAI,CAAChB,MAAM,CAAC,IAAI,CAACE,QAAQ,GAAG,CAAC,CAAC,GAAIc,KAAK,KAAK,CAAC,GAAI,IAAI;IACrD,IAAI,CAAChB,MAAM,CAAC,IAAI,CAACE,QAAQ,CAAC,GAAGc,KAAK,GAAG,IAAI;IACzC,IAAI,CAACd,QAAQ,IAAII,MAAM;EACzB;EAEAe,aAAaA,CAACL,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACqB,aAAa,CAACL,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC/C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAgB,eAAeA,CAACN,KAAa,EAAE;IAC7B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACsB,eAAe,CAACN,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IACjD,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAiB,YAAYA,CAACP,KAAa,EAAE;IAC1B,IAAI,CAACM,eAAe,CAACE,MAAM,CAACR,KAAK,CAAC,CAAC;EACrC;EAEAS,aAAaA,CAACT,KAAa,EAAE;IAC3B,IAAI,CAACU,gBAAgB,CAACF,MAAM,CAACR,KAAK,CAAC,CAAC;EACtC;EAEAU,gBAAgBA,CAACV,KAAa,EAAE;IAC9B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAAC0B,gBAAgB,CAACV,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAClD,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAqB,aAAaA,CAACX,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAAC2B,aAAa,CAACX,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC/C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAsB,aAAaA,CAACZ,KAAa,EAAE;IAC3B;IACA,IAAI,CAACa,YAAY,CAACb,KAAK,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACD,UAAU,CAACJ,IAAI,CAACmB,KAAK,CAACd,KAAK,GAAG3B,cAAc,CAAC,CAAC;EACrD;EAEA0C,SAASA,CAACf,KAAa,EAAE;IACvB,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAAC+B,SAAS,CAACf,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC3C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA0B,YAAYA,CAAChB,KAAa,EAAE;IAC1B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACgC,YAAY,CAAChB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC9C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA2B,YAAYA,CAACjB,KAAa,EAAE;IAC1B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACiC,YAAY,CAACjB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC9C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAuB,YAAYA,CAACb,KAAa,EAAE;IAC1B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAAC6B,YAAY,CAACb,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC9C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA4B,YAAYA,CAAClB,KAAa,EAAE;IAC1B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACkC,YAAY,CAAClB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC9C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA6B,YAAYA,CAACnB,KAAa,EAAE;IAC1B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACmC,YAAY,CAACnB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC9C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA8B,aAAaA,CAACpB,KAAa,EAAE;IAC3B,MAAMV,MAAM,GAAG,CAAC;IAChB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,IAAI,CAACN,MAAM,CAACoC,aAAa,CAACpB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;IAC/C,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEA+B,WAAWA,CAACrB,KAAa,EAAElB,QAA0B,EAAE;IACrD,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B;IAEA,MAAMQ,MAAM,GAAGf,MAAM,CAAC+C,UAAU,CAACtB,KAAK,EAAElB,QAAQ,CAAC;IACjD,IAAI,CAACS,WAAW,CAACD,MAAM,CAAC;;IAExB;IACA,IAAI,CAACN,MAAM,CAACuC,KAAK,CAACvB,KAAK,EAAE,IAAI,CAACd,QAAQ,EAAEJ,QAAQ,CAAC;IACjD,IAAI,CAACI,QAAQ,IAAII,MAAM;EACzB;EAEAkC,aAAaA,CAACxB,KAAa,EAAElB,QAA0B,EAAE;IACvD,IAAI,CAACiB,UAAU,CAACC,KAAK,CAACV,MAAM,CAAC;IAC7B,IAAI,CAAC+B,WAAW,CAACrB,KAAK,EAAElB,QAAQ,CAAC;EACnC;EAEA2C,cAAcA,CAACzB,KAAa,EAAElB,QAA0B,EAAE;IACxD,IAAI,CAACmB,aAAa,CAACD,KAAK,CAACV,MAAM,CAAC;IAChC,IAAI,CAAC+B,WAAW,CAACrB,KAAK,EAAElB,QAAQ,CAAC;EACnC;;EAEA;EACA4C,cAAcA,CAAC1B,KAAU,EAAElB,QAA0B,EAAE;IACrD,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B;IAEA,IAAIQ,MAAM;IACV,IAAIU,KAAK,YAAYzB,MAAM,EAAE;MAC3Be,MAAM,GAAGU,KAAK,CAACV,MAAM;IACvB,CAAC,MAAM;MACLU,KAAK,GAAGA,KAAK,CAAC2B,QAAQ,CAAC,CAAC;MACxBrC,MAAM,GAAGf,MAAM,CAAC+C,UAAU,CAACtB,KAAK,EAAElB,QAAQ,CAAC;IAC7C;IACA,IAAI,CAACmB,aAAa,CAACX,MAAM,CAAC;IAE1B,IAAIU,KAAK,YAAYzB,MAAM,EAAE;MAC3B,IAAI,CAACqD,WAAW,CAAC5B,KAAK,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAACT,WAAW,CAACD,MAAM,CAAC;MACxB;MACA,IAAI,CAACN,MAAM,CAACuC,KAAK,CAACvB,KAAK,EAAE,IAAI,CAACd,QAAQ,EAAEJ,QAAQ,CAAC;MACjD,IAAI,CAACI,QAAQ,IAAII,MAAM;IACzB;EACF;EAEAuC,YAAYA,CAAC7B,KAAU,EAAElB,QAA0B,EAAE;IACnD,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B;IAEA,IAAIQ,MAAM;IACV,IAAIU,KAAK,YAAYzB,MAAM,EAAE;MAC3Be,MAAM,GAAGU,KAAK,CAACV,MAAM;IACvB,CAAC,MAAM;MACLU,KAAK,GAAGA,KAAK,CAAC2B,QAAQ,CAAC,CAAC;MACxBrC,MAAM,GAAGf,MAAM,CAAC+C,UAAU,CAACtB,KAAK,EAAElB,QAAQ,CAAC;IAC7C;;IAEA;IACA;IACA;IACA,IAAI,CAAC8C,WAAW,CAACtD,eAAe,CAAC;;IAEjC;IACA,IAAIgB,MAAM,GAAG,CAAC,EAAE;MACd;MACA,IAAI,CAACe,aAAa,CAACf,MAAM,CAAC;MAC1B,IAAIU,KAAK,YAAYzB,MAAM,EAAE;QAC3B,IAAI,CAACqD,WAAW,CAAC5B,KAAK,CAAC;MACzB,CAAC,MAAM;QACL,IAAI,CAACT,WAAW,CAACD,MAAM,CAAC;QACxB,IAAI,CAACN,MAAM,CAACuC,KAAK,CAACvB,KAAK,EAAE,IAAI,CAACd,QAAQ,EAAEJ,QAAQ,CAAC;QACjD,IAAI,CAACI,QAAQ,IAAII,MAAM;MACzB;IACF;;IAEA;IACA,IAAI,CAACe,aAAa,CAAC,CAAC,CAAC;EACvB;EAEAuB,WAAWA,CAAC5B,KAAa,EAAE;IACzB,MAAMV,MAAM,GAAGU,KAAK,CAACV,MAAM;IAC3B,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxBU,KAAK,CAACR,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE,IAAI,CAACE,QAAQ,CAAC;IACtC,IAAI,CAACA,QAAQ,IAAII,MAAM;EACzB;EAEAwC,UAAUA,CAAC9B,KAAa,EAAE;IACxB,IAAI,CAACa,YAAY,CAAClB,IAAI,CAACmB,KAAK,CAACd,KAAK,GAAG3B,cAAc,CAAC,CAAC;IACrD,IAAI,CAACwC,YAAY,CAACb,KAAK,GAAG,CAAC,CAAC,CAAC;EAC/B;AACF;AAAC,IAAA+B,QAAA,GAEcpD,sBAAsB;AAAAqD,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACrCG,MAAM,CAACF,OAAO,GAAGrD,sBAAsB"}