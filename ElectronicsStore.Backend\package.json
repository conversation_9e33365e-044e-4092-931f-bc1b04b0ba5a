{"name": "electronics-store-backend", "version": "1.0.0", "description": "Backend API for Electronics Store Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/initDatabase.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["electronics", "store", "management", "api", "nodejs", "express", "sqlite"], "author": "Electronics Store", "license": "MIT"}