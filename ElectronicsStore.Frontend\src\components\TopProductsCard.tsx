import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Box,
  Chip,
  LinearProgress,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { TopProduct } from '../types';

interface TopProductsCardProps {
  products: TopProduct[];
  title?: string;
}

const TopProductsCard: React.FC<TopProductsCardProps> = ({
  products,
  title = 'أفضل المنتجات مبيعاً',
}) => {
  const theme = useTheme();

  const formatCurrency = (amount: number): string => {
    return `${amount.toLocaleString('ar-SA')} ر.س`;
  };

  const getProgressValue = (current: number, max: number): number => {
    return (current / max) * 100;
  };

  const maxSold = Math.max(...products.map(p => p.totalSold));

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {title}
          </Typography>
        }
        avatar={
          <Avatar sx={{ bgcolor: theme.palette.success.main }}>
            <TrendingUpIcon />
          </Avatar>
        }
      />
      <CardContent sx={{ pt: 0 }}>
        <List sx={{ p: 0 }}>
          {products.map((product, index) => (
            <ListItem
              key={product.id}
              sx={{
                px: 0,
                py: 2,
                borderBottom: index < products.length - 1 ? 1 : 0,
                borderColor: 'divider',
              }}
            >
              <ListItemAvatar>
                <Avatar
                  sx={{
                    bgcolor: `${theme.palette.primary.main}${Math.floor((5 - index) * 20).toString(16)}`,
                    color: theme.palette.primary.main,
                    fontWeight: 700,
                  }}
                >
                  {index + 1}
                </Avatar>
              </ListItemAvatar>
              <Box sx={{ flex: 1, ml: 2 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {product.name}
                  </Typography>
                  <Chip
                    label={`${product.totalSold} قطعة`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    الإيرادات: {formatCurrency(product.revenue)}
                  </Typography>
                  <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                    الربح: {formatCurrency(product.profit)}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={getProgressValue(product.totalSold, maxSold)}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      bgcolor: theme.palette.primary.main,
                    },
                  }}
                />
              </Box>
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default TopProductsCard;
