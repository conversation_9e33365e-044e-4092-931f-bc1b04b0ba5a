"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const BitN = {
  id: 0x68,
  type: 'BITN',
  name: 'BitN',
  declaration() {
    throw new Error('not implemented');
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  *generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = BitN;
exports.default = _default;
module.exports = BitN;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJCaXROIiwiaWQiLCJ0eXBlIiwibmFtZSIsImRlY2xhcmF0aW9uIiwiRXJyb3IiLCJnZW5lcmF0ZVR5cGVJbmZvIiwiZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgiLCJnZW5lcmF0ZVBhcmFtZXRlckRhdGEiLCJ2YWxpZGF0ZSIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsImRlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvZGF0YS10eXBlcy9iaXRuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgRGF0YVR5cGUgfSBmcm9tICcuLi9kYXRhLXR5cGUnO1xuXG5jb25zdCBCaXROOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4NjgsXG4gIHR5cGU6ICdCSVROJyxcbiAgbmFtZTogJ0JpdE4nLFxuXG4gIGRlY2xhcmF0aW9uKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVUeXBlSW5mbygpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgKiBnZW5lcmF0ZVBhcmFtZXRlckRhdGEoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfSxcblxuICB2YWxpZGF0ZSgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9XG59O1xuXG5leHBvcnQgZGVmYXVsdCBCaXROO1xubW9kdWxlLmV4cG9ydHMgPSBCaXROO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxNQUFNQSxJQUFjLEdBQUc7RUFDckJDLEVBQUUsRUFBRSxJQUFJO0VBQ1JDLElBQUksRUFBRSxNQUFNO0VBQ1pDLElBQUksRUFBRSxNQUFNO0VBRVpDLFdBQVdBLENBQUEsRUFBRztJQUNaLE1BQU0sSUFBSUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREMsZ0JBQWdCQSxDQUFBLEVBQUc7SUFDakIsTUFBTSxJQUFJRCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVERSx1QkFBdUJBLENBQUEsRUFBRztJQUN4QixNQUFNLElBQUlGLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRUQsQ0FBRUcscUJBQXFCQSxDQUFBLEVBQUc7SUFDeEIsTUFBTSxJQUFJSCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVESSxRQUFRQSxDQUFBLEVBQUc7SUFDVCxNQUFNLElBQUlKLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQztBQUNGLENBQUM7QUFBQyxJQUFBSyxRQUFBLEdBRWFWLElBQUk7QUFBQVcsT0FBQSxDQUFBQyxPQUFBLEdBQUFGLFFBQUE7QUFDbkJHLE1BQU0sQ0FBQ0YsT0FBTyxHQUFHWCxJQUFJIn0=