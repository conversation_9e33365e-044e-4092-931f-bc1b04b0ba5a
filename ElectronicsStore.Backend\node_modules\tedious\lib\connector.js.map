{"version": 3, "file": "connector.js", "names": ["_net", "_interopRequireDefault", "require", "_nodeUrl", "_abortError", "_esAggregateError", "obj", "__esModule", "default", "connectInParallel", "options", "lookup", "signal", "aborted", "AbortError", "addresses", "lookupAllAddresses", "host", "Promise", "resolve", "reject", "sockets", "Array", "length", "errors", "onError", "err", "push", "removeListener", "onConnect", "destroy", "removeEventListener", "onAbort", "AggregateError", "j", "socket", "i", "len", "net", "connect", "address", "family", "on", "addEventListener", "once", "connectInSequence", "Error", "name", "isIPv6", "isIPv4", "url", "domainToASCII", "all"], "sources": ["../src/connector.ts"], "sourcesContent": ["import net from 'net';\nimport dns, { type LookupAddress } from 'dns';\n\nimport url from 'node:url';\nimport { AbortSignal } from 'node-abort-controller';\nimport AbortError from './errors/abort-error';\n\nimport AggregateError from 'es-aggregate-error';\n\ntype LookupFunction = (hostname: string, options: dns.LookupAllOptions, callback: (err: NodeJS.ErrnoException | null, addresses: dns.LookupAddress[]) => void) => void;\n\nexport async function connectInParallel(options: { host: string, port: number, localAddress?: string | undefined }, lookup: LookupFunction, signal: AbortSignal) {\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  const addresses = await lookupAllAddresses(options.host, lookup, signal);\n\n  return await new Promise<net.Socket>((resolve, reject) => {\n    const sockets = new Array(addresses.length);\n\n    const errors: Error[] = [];\n\n    function onError(this: net.Socket, err: Error) {\n      errors.push(err);\n\n      this.removeListener('error', onError);\n      this.removeListener('connect', onConnect);\n\n      this.destroy();\n\n      if (errors.length === addresses.length) {\n        signal.removeEventListener('abort', onAbort);\n\n        reject(new AggregateError(errors, 'Could not connect (parallel)'));\n      }\n    }\n\n    function onConnect(this: net.Socket) {\n      signal.removeEventListener('abort', onAbort);\n\n      for (let j = 0; j < sockets.length; j++) {\n        const socket = sockets[j];\n\n        if (this === socket) {\n          continue;\n        }\n\n        socket.removeListener('error', onError);\n        socket.removeListener('connect', onConnect);\n        socket.destroy();\n      }\n\n      resolve(this);\n    }\n\n    const onAbort = () => {\n      for (let j = 0; j < sockets.length; j++) {\n        const socket = sockets[j];\n\n        socket.removeListener('error', onError);\n        socket.removeListener('connect', onConnect);\n\n        socket.destroy();\n      }\n\n      reject(new AbortError());\n    };\n\n    for (let i = 0, len = addresses.length; i < len; i++) {\n      const socket = sockets[i] = net.connect({\n        ...options,\n        host: addresses[i].address,\n        family: addresses[i].family\n      });\n\n      socket.on('error', onError);\n      socket.on('connect', onConnect);\n    }\n\n    signal.addEventListener('abort', onAbort, { once: true });\n  });\n}\n\nexport async function connectInSequence(options: { host: string, port: number, localAddress?: string | undefined }, lookup: LookupFunction, signal: AbortSignal) {\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  const errors: any[] = [];\n  const addresses = await lookupAllAddresses(options.host, lookup, signal);\n\n  for (const address of addresses) {\n    try {\n      return await new Promise<net.Socket>((resolve, reject) => {\n        const socket = net.connect({\n          ...options,\n          host: address.address,\n          family: address.family\n        });\n\n        const onAbort = () => {\n          socket.removeListener('error', onError);\n          socket.removeListener('connect', onConnect);\n\n          socket.destroy();\n\n          reject(new AbortError());\n        };\n\n        const onError = (err: Error) => {\n          signal.removeEventListener('abort', onAbort);\n\n          socket.removeListener('error', onError);\n          socket.removeListener('connect', onConnect);\n\n          socket.destroy();\n\n          reject(err);\n        };\n\n        const onConnect = () => {\n          signal.removeEventListener('abort', onAbort);\n\n          socket.removeListener('error', onError);\n          socket.removeListener('connect', onConnect);\n\n          resolve(socket);\n        };\n\n        signal.addEventListener('abort', onAbort, { once: true });\n\n        socket.on('error', onError);\n        socket.on('connect', onConnect);\n      });\n    } catch (err) {\n      if (err instanceof Error && err.name === 'AbortError') {\n        throw err;\n      }\n\n      errors.push(err);\n\n      continue;\n    }\n  }\n\n  throw new AggregateError(errors, 'Could not connect (sequence)');\n}\n\n/**\n * Look up all addresses for the given hostname.\n */\nexport async function lookupAllAddresses(host: string, lookup: LookupFunction, signal: AbortSignal): Promise<dns.LookupAddress[]> {\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  if (net.isIPv6(host)) {\n    return [{ address: host, family: 6 }];\n  } else if (net.isIPv4(host)) {\n    return [{ address: host, family: 4 }];\n  } else {\n    return await new Promise<LookupAddress[]>((resolve, reject) => {\n      const onAbort = () => {\n        reject(new AbortError());\n      };\n\n      signal.addEventListener('abort', onAbort);\n\n      lookup(url.domainToASCII(host), { all: true }, (err, addresses) => {\n        signal.removeEventListener('abort', onAbort);\n\n        err ? reject(err) : resolve(addresses);\n      });\n    });\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,iBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAgD,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAIzC,eAAeG,iBAAiBA,CAACC,OAA0E,EAAEC,MAAsB,EAAEC,MAAmB,EAAE;EAC/J,IAAIA,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,MAAMC,SAAS,GAAG,MAAMC,kBAAkB,CAACN,OAAO,CAACO,IAAI,EAAEN,MAAM,EAAEC,MAAM,CAAC;EAExE,OAAO,MAAM,IAAIM,OAAO,CAAa,CAACC,OAAO,EAAEC,MAAM,KAAK;IACxD,MAAMC,OAAO,GAAG,IAAIC,KAAK,CAACP,SAAS,CAACQ,MAAM,CAAC;IAE3C,MAAMC,MAAe,GAAG,EAAE;IAE1B,SAASC,OAAOA,CAAmBC,GAAU,EAAE;MAC7CF,MAAM,CAACG,IAAI,CAACD,GAAG,CAAC;MAEhB,IAAI,CAACE,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;MACrC,IAAI,CAACG,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;MAEzC,IAAI,CAACC,OAAO,CAAC,CAAC;MAEd,IAAIN,MAAM,CAACD,MAAM,KAAKR,SAAS,CAACQ,MAAM,EAAE;QACtCX,MAAM,CAACmB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;QAE5CZ,MAAM,CAAC,IAAIa,yBAAc,CAACT,MAAM,EAAE,8BAA8B,CAAC,CAAC;MACpE;IACF;IAEA,SAASK,SAASA,CAAA,EAAmB;MACnCjB,MAAM,CAACmB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;MAE5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,CAACE,MAAM,EAAEW,CAAC,EAAE,EAAE;QACvC,MAAMC,MAAM,GAAGd,OAAO,CAACa,CAAC,CAAC;QAEzB,IAAI,IAAI,KAAKC,MAAM,EAAE;UACnB;QACF;QAEAA,MAAM,CAACP,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;QACvCU,MAAM,CAACP,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;QAC3CM,MAAM,CAACL,OAAO,CAAC,CAAC;MAClB;MAEAX,OAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAMa,OAAO,GAAGA,CAAA,KAAM;MACpB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,CAACE,MAAM,EAAEW,CAAC,EAAE,EAAE;QACvC,MAAMC,MAAM,GAAGd,OAAO,CAACa,CAAC,CAAC;QAEzBC,MAAM,CAACP,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;QACvCU,MAAM,CAACP,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;QAE3CM,MAAM,CAACL,OAAO,CAAC,CAAC;MAClB;MAEAV,MAAM,CAAC,IAAIN,mBAAU,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGtB,SAAS,CAACQ,MAAM,EAAEa,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,MAAMD,MAAM,GAAGd,OAAO,CAACe,CAAC,CAAC,GAAGE,YAAG,CAACC,OAAO,CAAC;QACtC,GAAG7B,OAAO;QACVO,IAAI,EAAEF,SAAS,CAACqB,CAAC,CAAC,CAACI,OAAO;QAC1BC,MAAM,EAAE1B,SAAS,CAACqB,CAAC,CAAC,CAACK;MACvB,CAAC,CAAC;MAEFN,MAAM,CAACO,EAAE,CAAC,OAAO,EAAEjB,OAAO,CAAC;MAC3BU,MAAM,CAACO,EAAE,CAAC,SAAS,EAAEb,SAAS,CAAC;IACjC;IAEAjB,MAAM,CAAC+B,gBAAgB,CAAC,OAAO,EAAEX,OAAO,EAAE;MAAEY,IAAI,EAAE;IAAK,CAAC,CAAC;EAC3D,CAAC,CAAC;AACJ;AAEO,eAAeC,iBAAiBA,CAACnC,OAA0E,EAAEC,MAAsB,EAAEC,MAAmB,EAAE;EAC/J,IAAIA,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,MAAMU,MAAa,GAAG,EAAE;EACxB,MAAMT,SAAS,GAAG,MAAMC,kBAAkB,CAACN,OAAO,CAACO,IAAI,EAAEN,MAAM,EAAEC,MAAM,CAAC;EAExE,KAAK,MAAM4B,OAAO,IAAIzB,SAAS,EAAE;IAC/B,IAAI;MACF,OAAO,MAAM,IAAIG,OAAO,CAAa,CAACC,OAAO,EAAEC,MAAM,KAAK;QACxD,MAAMe,MAAM,GAAGG,YAAG,CAACC,OAAO,CAAC;UACzB,GAAG7B,OAAO;UACVO,IAAI,EAAEuB,OAAO,CAACA,OAAO;UACrBC,MAAM,EAAED,OAAO,CAACC;QAClB,CAAC,CAAC;QAEF,MAAMT,OAAO,GAAGA,CAAA,KAAM;UACpBG,MAAM,CAACP,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;UACvCU,MAAM,CAACP,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;UAE3CM,MAAM,CAACL,OAAO,CAAC,CAAC;UAEhBV,MAAM,CAAC,IAAIN,mBAAU,CAAC,CAAC,CAAC;QAC1B,CAAC;QAED,MAAMW,OAAO,GAAIC,GAAU,IAAK;UAC9Bd,MAAM,CAACmB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;UAE5CG,MAAM,CAACP,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;UACvCU,MAAM,CAACP,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;UAE3CM,MAAM,CAACL,OAAO,CAAC,CAAC;UAEhBV,MAAM,CAACM,GAAG,CAAC;QACb,CAAC;QAED,MAAMG,SAAS,GAAGA,CAAA,KAAM;UACtBjB,MAAM,CAACmB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;UAE5CG,MAAM,CAACP,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;UACvCU,MAAM,CAACP,cAAc,CAAC,SAAS,EAAEC,SAAS,CAAC;UAE3CV,OAAO,CAACgB,MAAM,CAAC;QACjB,CAAC;QAEDvB,MAAM,CAAC+B,gBAAgB,CAAC,OAAO,EAAEX,OAAO,EAAE;UAAEY,IAAI,EAAE;QAAK,CAAC,CAAC;QAEzDT,MAAM,CAACO,EAAE,CAAC,OAAO,EAAEjB,OAAO,CAAC;QAC3BU,MAAM,CAACO,EAAE,CAAC,SAAS,EAAEb,SAAS,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZ,IAAIA,GAAG,YAAYoB,KAAK,IAAIpB,GAAG,CAACqB,IAAI,KAAK,YAAY,EAAE;QACrD,MAAMrB,GAAG;MACX;MAEAF,MAAM,CAACG,IAAI,CAACD,GAAG,CAAC;MAEhB;IACF;EACF;EAEA,MAAM,IAAIO,yBAAc,CAACT,MAAM,EAAE,8BAA8B,CAAC;AAClE;;AAEA;AACA;AACA;AACO,eAAeR,kBAAkBA,CAACC,IAAY,EAAEN,MAAsB,EAAEC,MAAmB,EAAgC;EAChI,IAAIA,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,IAAIwB,YAAG,CAACU,MAAM,CAAC/B,IAAI,CAAC,EAAE;IACpB,OAAO,CAAC;MAAEuB,OAAO,EAAEvB,IAAI;MAAEwB,MAAM,EAAE;IAAE,CAAC,CAAC;EACvC,CAAC,MAAM,IAAIH,YAAG,CAACW,MAAM,CAAChC,IAAI,CAAC,EAAE;IAC3B,OAAO,CAAC;MAAEuB,OAAO,EAAEvB,IAAI;MAAEwB,MAAM,EAAE;IAAE,CAAC,CAAC;EACvC,CAAC,MAAM;IACL,OAAO,MAAM,IAAIvB,OAAO,CAAkB,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC7D,MAAMY,OAAO,GAAGA,CAAA,KAAM;QACpBZ,MAAM,CAAC,IAAIN,mBAAU,CAAC,CAAC,CAAC;MAC1B,CAAC;MAEDF,MAAM,CAAC+B,gBAAgB,CAAC,OAAO,EAAEX,OAAO,CAAC;MAEzCrB,MAAM,CAACuC,gBAAG,CAACC,aAAa,CAAClC,IAAI,CAAC,EAAE;QAAEmC,GAAG,EAAE;MAAK,CAAC,EAAE,CAAC1B,GAAG,EAAEX,SAAS,KAAK;QACjEH,MAAM,CAACmB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;QAE5CN,GAAG,GAAGN,MAAM,CAACM,GAAG,CAAC,GAAGP,OAAO,CAACJ,SAAS,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF"}