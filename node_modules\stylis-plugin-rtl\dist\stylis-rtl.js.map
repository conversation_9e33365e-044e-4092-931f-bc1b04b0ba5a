{"version": 3, "file": "stylis-rtl.js", "sourceRoot": "", "sources": ["../src/stylis-rtl.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EACL,OAAO,EACP,OAAO,EACP,WAAW,EACX,MAAM,EACN,OAAO,EACP,SAAS,EACT,MAAM,EAEN,SAAS,EACT,KAAK,EACL,QAAQ,GACT,MAAM,QAAQ,CAAC;AAIhB,SAAS,yBAAyB,CAChC,OAA4B,EAC5B,KAA0B,EAC1B,QAA6B;IAE7B,QAAQ,OAAO,CAAC,IAAI,EAAE;QACpB,KAAK,MAAM,CAAC;QACZ,KAAK,WAAW,CAAC;QACjB,KAAK,OAAO;YACV,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5D,KAAK,OAAO,CAAC,CAAC;YACZ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;YAEvF,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC;oBACzB,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO;wBAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC;gBAC/C,CAAC,CAAC,CAAC;aACJ;SACF;KACF;IAED,IAAM,kBAAkB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,yBAAyB,CAAC,CAAC;IAE1G,OAAO,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,GAAG,GAAG,kBAAkB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7G,CAAC;AAED,SAAS,eAAe,CACtB,OAA4B,EAC5B,KAA0B,EAC1B,QAA6B,EAC7B,QAA6B;IAE7B,IACE,OAAO,CAAC,IAAI,KAAK,SAAS;QAC1B,OAAO,CAAC,IAAI,KAAK,QAAQ;QACzB,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,EACnH;QACA,IAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,yBAAyB,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5F,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;KACrB;AACH,CAAC;AAED,qFAAqF;AACrF,YAAY;AACZ,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAE7E,eAAe,eAAe,CAAC"}