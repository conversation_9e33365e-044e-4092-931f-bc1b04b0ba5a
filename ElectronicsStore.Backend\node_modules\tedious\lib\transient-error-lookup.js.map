{"version": 3, "file": "transient-error-lookup.js", "names": ["TransientErrorLookup", "isTransientError", "error", "transientErrors", "indexOf", "exports"], "sources": ["../src/transient-error-lookup.ts"], "sourcesContent": ["// This simple piece of code is factored out into a separate class to make it\n// easy to stub it out in tests. It's hard, if not impossible, to cause a\n// transient error on demand in tests.\nexport class TransientErrorLookup {\n  isTransientError(error: number) {\n    // This list of transient errors comes from Microsoft implementation of SqlClient:\n    //  - https://github.com/dotnet/corefx/blob/master/src/System.Data.SqlClient/src/System/Data/SqlClient/SqlInternalConnectionTds.cs#L115\n    const transientErrors = [4060, 10928, 10929, 40197, 40501, 40613];\n    return transientErrors.indexOf(error) !== -1;\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACO,MAAMA,oBAAoB,CAAC;EAChCC,gBAAgBA,CAACC,KAAa,EAAE;IAC9B;IACA;IACA,MAAMC,eAAe,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACjE,OAAOA,eAAe,CAACC,OAAO,CAACF,KAAK,CAAC,KAAK,CAAC,CAAC;EAC9C;AACF;AAACG,OAAA,CAAAL,oBAAA,GAAAA,oBAAA"}