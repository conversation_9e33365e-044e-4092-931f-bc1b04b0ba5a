{"name": "c<PERSON><PERSON><PERSON>", "version": "2.3.0", "description": "Convert CSS stylesheets between left-to-right and right-to-left directions.", "author": "<PERSON> <<EMAIL>> (https://trevorparscal.com/)", "license": "Apache-2.0", "homepage": "https://www.mediawiki.org/wiki/CSSJanus", "bugs": {"url": "https://phabricator.wikimedia.org/tag/cssjanus/"}, "repository": {"type": "git", "url": "https://gerrit.wikimedia.org/g/mediawiki/libs/node-cssjanus"}, "keywords": ["i18n", "bidi", "rtl", "ltr"], "main": "./src/cssjanus.js", "files": ["src/", "History.md", "LICENSE.txt"], "scripts": {"doc": "rm docs/lib/cssjanus.js && cp src/cssjanus.js docs/lib/cssjanus.js", "renew-doc-deps": "curl -s https://unpkg.com/@wikimedia/codex-design-tokens@1.0.0/theme-wikimedia-ui.css -o docs/lib/codex-design-tokens/theme-wikimedia-ui-1.0.0.css", "test": "eslint . && qunit test/unit.js", "coverage": "nyc qunit test/unit.js"}, "engines": {"node": ">=10.0.0"}, "dependencies": {}, "devDependencies": {"eslint": "8.57.0", "eslint-config-wikimedia": "0.28.2", "nyc": "^15.1.0", "qunit": "2.20.0"}, "nyc": {"include": ["src"], "report-dir": "coverage", "reporter": ["text", "html", "clover"]}}