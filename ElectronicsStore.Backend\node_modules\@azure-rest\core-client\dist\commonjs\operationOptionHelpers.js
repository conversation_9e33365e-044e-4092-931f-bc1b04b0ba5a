"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.operationOptionsToRequestParameters = operationOptionsToRequestParameters;
const ts_http_runtime_1 = require("@typespec/ts-http-runtime");
/**
 * Helper function to convert OperationOptions to RequestParameters
 * @param options - the options that are used by Modular layer to send the request
 * @returns the result of the conversion in RequestParameters of RLC layer
 */
function operationOptionsToRequestParameters(options) {
    return (0, ts_http_runtime_1.operationOptionsToRequestParameters)(options);
}
//# sourceMappingURL=operationOptionHelpers.js.map