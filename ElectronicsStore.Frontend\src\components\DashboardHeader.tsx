import React from 'react';
import {
  Box,
  Typography,
  Breadcrumbs,
  Link,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import {
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  AccessTime as TimeIcon,
} from '@mui/icons-material';

interface DashboardHeaderProps {
  title: string;
  subtitle: string;
  showBreadcrumbs?: boolean;
  showLastUpdate?: boolean;
  lastUpdate?: Date;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  subtitle,
  showBreadcrumbs = true,
  showLastUpdate = true,
  lastUpdate = new Date(),
}) => {
  const theme = useTheme();

  const formatLastUpdate = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    
    return date.toLocaleDateString('ar-SA');
  };

  return (
    <Box
      sx={{
        mb: 4,
        p: 3,
        borderRadius: 2,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        },
      }}
    >
      {/* Breadcrumbs */}
      {showBreadcrumbs && (
        <Breadcrumbs
          sx={{ mb: 2 }}
          separator="/"
          aria-label="breadcrumb"
        >
          <Link
            underline="hover"
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              color: 'text.secondary',
              '&:hover': { color: 'primary.main' }
            }}
            href="/"
          >
            <HomeIcon sx={{ mr: 0.5, fontSize: '1rem' }} />
            الرئيسية
          </Link>
          <Box sx={{ display: 'flex', alignItems: 'center', color: 'primary.main' }}>
            <DashboardIcon sx={{ mr: 0.5, fontSize: '1rem' }} />
            {title}
          </Box>
        </Breadcrumbs>
      )}

      {/* Header Content */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 700, 
              mb: 1,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            {title}
          </Typography>
          <Typography 
            variant="body1" 
            color="text.secondary" 
            sx={{ 
              fontSize: '1.1rem',
              maxWidth: '600px',
            }}
          >
            {subtitle}
          </Typography>
        </Box>

        {/* Last Update */}
        {showLastUpdate && (
          <Chip
            icon={<TimeIcon />}
            label={`آخر تحديث: ${formatLastUpdate(lastUpdate)}`}
            variant="outlined"
            size="small"
            sx={{
              borderColor: alpha(theme.palette.primary.main, 0.3),
              color: 'text.secondary',
              '& .MuiChip-icon': {
                color: theme.palette.primary.main,
              },
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export default DashboardHeader;
