import { getPickersLocalization } from './utils/getPickersLocalization';
// maps TimeView to its translation
const timeViews = {
  hours: 'Hodiny',
  minutes: 'Minuty',
  seconds: 'Sekundy',
  meridiem: 'Odpole<PERSON>e'
};
const csCZPickers = {
  // Calendar navigation
  previousMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> měs<PERSON>',
  nextMonth: '<PERSON><PERSON><PERSON> m<PERSON>',
  // View navigation
  openPreviousView: 'otevřít předcho<PERSON> zobrazen<PERSON>',
  openNextView: 'otevřít dalš<PERSON> zobrazen<PERSON>',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'roční zobrazení otevřeno, přepněte do zobrazení kalendáře' : 'zobrazen<PERSON> kalend<PERSON>e otevřeno, přepněte do zobrazení roku',
  // DateRange placeholders
  start: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  end: '<PERSON>ne<PERSON>',
  // Action bar
  cancelButtonLabel: '<PERSON><PERSON><PERSON><PERSON>',
  clearButtonLabel: 'Vymazat',
  okButtonLabel: 'Potvrdit',
  todayButtonLabel: 'Dnes',
  // Toolbar titles
  datePickerToolbarTitle: 'Vyberte datum',
  dateTimePickerToolbarTitle: 'Vyberte datum a čas',
  timePickerToolbarTitle: 'Vyberte čas',
  dateRangePickerToolbarTitle: 'Vyberete rozmezí dat',
  // Clock labels
  clockLabelText: (view, time, adapter) => `${timeViews[view] ?? view} vybrány. ${time === null ? 'Není vybrán čas' : `Vybraný čas je ${adapter.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} hodin`,
  minutesClockNumberText: minutes => `${minutes} minut`,
  secondsClockNumberText: seconds => `${seconds} sekund`,
  // Digital clock labels
  selectViewText: view => `Vyberte ${timeViews[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Týden v roce',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `${weekNumber} týden v roce`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Vyberte datum, vybrané datum je ${utils.format(value, 'fullDate')}` : 'Vyberte datum',
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Vyberte čas, vybraný čas je ${utils.format(value, 'fullTime')}` : 'Vyberte čas',
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'vyberte čas',
  dateTableLabel: 'vyberte datum',
  // Field section placeholders
  fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => 'hh',
  fieldMinutesPlaceholder: () => 'mm',
  fieldSecondsPlaceholder: () => 'ss',
  fieldMeridiemPlaceholder: () => 'aa'
};
export const csCZ = getPickersLocalization(csCZPickers);