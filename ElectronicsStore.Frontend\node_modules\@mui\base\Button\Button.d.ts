import { PolymorphicComponent } from '../utils/PolymorphicComponent';
import { ButtonTypeMap } from './Button.types';
/**
 * The foundation for building custom-styled buttons.
 *
 * Demos:
 *
 * - [Button](https://mui.com/base-ui/react-button/)
 *
 * API:
 *
 * - [Button API](https://mui.com/base-ui/react-button/components-api/#button)
 */
declare const Button: PolymorphicComponent<ButtonTypeMap<{}, "button">>;
export { Button };
