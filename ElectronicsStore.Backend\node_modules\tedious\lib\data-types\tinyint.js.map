{"version": 3, "file": "tinyint.js", "names": ["_intn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "TinyInt", "id", "type", "name", "declaration", "generateTypeInfo", "IntN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeUInt8", "Number", "validate", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/tinyint.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport IntN from './intn';\n\nconst DATA_LENGTH = Buffer.from([0x01]);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst TinyInt: DataType = {\n  id: 0x30,\n  type: 'INT1',\n  name: 'TinyInt',\n\n  declaration: function() {\n    return 'tinyint';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([IntN.id, 0x01]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(1);\n    buffer.writeUInt8(Number(parameter.value), 0);\n    yield buffer;\n  },\n\n  validate: function(value): number | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'number') {\n      value = Number(value);\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n\n    if (value < 0 || value > 255) {\n      throw new TypeError('Value must be between 0 and 255, inclusive.');\n    }\n\n    return value | 0;\n  }\n};\n\nexport default TinyInt;\nmodule.exports = TinyInt;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE1B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,OAAiB,GAAG;EACxBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,SAAS;EAEfC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,SAAS;EAClB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,aAAI,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAED,CAAEe,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAACC,MAAM,CAACP,SAAS,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAME,MAAM;EACd,CAAC;EAEDI,QAAQ,EAAE,SAAAA,CAASN,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGK,MAAM,CAACL,KAAK,CAAC;IACvB;IAEA,IAAIO,KAAK,CAACP,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIQ,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAIR,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,GAAG,EAAE;MAC5B,MAAM,IAAIQ,SAAS,CAAC,6CAA6C,CAAC;IACpE;IAEA,OAAOR,KAAK,GAAG,CAAC;EAClB;AACF,CAAC;AAAC,IAAAS,QAAA,GAEanB,OAAO;AAAAoB,OAAA,CAAAzB,OAAA,GAAAwB,QAAA;AACtBE,MAAM,CAACD,OAAO,GAAGpB,OAAO"}