{"version": 3, "file": "bitn.js", "names": ["BitN", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/bitn.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst BitN: DataType = {\n  id: 0x68,\n  type: 'BITN',\n  name: 'BitN',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  * generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default BitN;\nmodule.exports = BitN;\n"], "mappings": ";;;;;;AAEA,MAAMA,IAAc,GAAG;EACrBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EAEZC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAED,CAAEG,qBAAqBA,CAAA,EAAG;IACxB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAEaV,IAAI;AAAAW,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACnBG,MAAM,CAACF,OAAO,GAAGX,IAAI"}