{"version": 3, "file": "instance-lookup.js", "names": ["_dns", "_interopRequireDefault", "require", "_abortError", "_sender", "_withTimeout", "obj", "__esModule", "default", "SQL_SERVER_BROWSER_PORT", "TIMEOUT", "RETRIES", "MYSTERY_HEADER_LENGTH", "instanceLookup", "options", "server", "TypeError", "instanceName", "timeout", "undefined", "retries", "lookup", "dns", "port", "signal", "aborted", "AbortError", "response", "i", "withTimeout", "request", "<PERSON><PERSON><PERSON>", "from", "sendMessage", "err", "Error", "name", "message", "toString", "foundPort", "parseBrowserResponse", "getPort", "instances", "split", "len", "length", "instance", "parts", "p", "partsLen", "value", "parseInt", "toUpperCase"], "sources": ["../src/instance-lookup.ts"], "sourcesContent": ["import dns from 'dns';\nimport { AbortSignal } from 'node-abort-controller';\n\nimport AbortError from './errors/abort-error';\nimport { sendMessage } from './sender';\nimport { withTimeout } from './utils/with-timeout';\n\nconst SQL_SERVER_BROWSER_PORT = 1434;\nconst TIMEOUT = 2 * 1000;\nconst RETRIES = 3;\n// There are three bytes at the start of the response, whose purpose is unknown.\nconst MYSTERY_HEADER_LENGTH = 3;\n\ntype LookupFunction = (hostname: string, options: dns.LookupAllOptions, callback: (err: NodeJS.ErrnoException | null, addresses: dns.LookupAddress[]) => void) => void;\n\n// Most of the functionality has been determined from from jTDS's MSSqlServerInfo class.\nexport async function instanceLookup(options: { server: string, instanceName: string, timeout?: number, retries?: number, port?: number, lookup?: LookupFunction, signal: AbortSignal }) {\n  const server = options.server;\n  if (typeof server !== 'string') {\n    throw new TypeError('Invalid arguments: \"server\" must be a string');\n  }\n\n  const instanceName = options.instanceName;\n  if (typeof instanceName !== 'string') {\n    throw new TypeError('Invalid arguments: \"instanceName\" must be a string');\n  }\n\n  const timeout = options.timeout === undefined ? TIMEOUT : options.timeout;\n  if (typeof timeout !== 'number') {\n    throw new TypeError('Invalid arguments: \"timeout\" must be a number');\n  }\n\n  const retries = options.retries === undefined ? RETRIES : options.retries;\n  if (typeof retries !== 'number') {\n    throw new TypeError('Invalid arguments: \"retries\" must be a number');\n  }\n\n  if (options.lookup !== undefined && typeof options.lookup !== 'function') {\n    throw new TypeError('Invalid arguments: \"lookup\" must be a function');\n  }\n  const lookup = options.lookup ?? dns.lookup;\n\n  if (options.port !== undefined && typeof options.port !== 'number') {\n    throw new TypeError('Invalid arguments: \"port\" must be a number');\n  }\n  const port = options.port ?? SQL_SERVER_BROWSER_PORT;\n\n  const signal = options.signal;\n\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  let response;\n\n  for (let i = 0; i <= retries; i++) {\n    try {\n      response = await withTimeout(timeout, async (signal) => {\n        const request = Buffer.from([0x02]);\n        return await sendMessage(options.server, port, lookup, signal, request);\n      }, signal);\n    } catch (err) {\n      // If the current attempt timed out, continue with the next\n      if (!signal.aborted && err instanceof Error && err.name === 'TimeoutError') {\n        continue;\n      }\n\n      throw err;\n    }\n  }\n\n  if (!response) {\n    throw new Error('Failed to get response from SQL Server Browser on ' + server);\n  }\n\n  const message = response.toString('ascii', MYSTERY_HEADER_LENGTH);\n  const foundPort = parseBrowserResponse(message, instanceName);\n\n  if (!foundPort) {\n    throw new Error('Port for ' + instanceName + ' not found in ' + options.server);\n  }\n\n  return foundPort;\n}\n\nexport function parseBrowserResponse(response: string, instanceName: string) {\n  let getPort;\n\n  const instances = response.split(';;');\n  for (let i = 0, len = instances.length; i < len; i++) {\n    const instance = instances[i];\n    const parts = instance.split(';');\n\n    for (let p = 0, partsLen = parts.length; p < partsLen; p += 2) {\n      const name = parts[p];\n      const value = parts[p + 1];\n\n      if (name === 'tcp' && getPort) {\n        const port = parseInt(value, 10);\n        return port;\n      }\n\n      if (name === 'InstanceName') {\n        if (value.toUpperCase() === instanceName.toUpperCase()) {\n          getPort = true;\n        } else {\n          getPort = false;\n        }\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAAmD,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEnD,MAAMG,uBAAuB,GAAG,IAAI;AACpC,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI;AACxB,MAAMC,OAAO,GAAG,CAAC;AACjB;AACA,MAAMC,qBAAqB,GAAG,CAAC;AAI/B;AACO,eAAeC,cAAcA,CAACC,OAAkJ,EAAE;EACvL,MAAMC,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC7B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,SAAS,CAAC,8CAA8C,CAAC;EACrE;EAEA,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;EACzC,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACpC,MAAM,IAAID,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEA,MAAME,OAAO,GAAGJ,OAAO,CAACI,OAAO,KAAKC,SAAS,GAAGT,OAAO,GAAGI,OAAO,CAACI,OAAO;EACzE,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIF,SAAS,CAAC,+CAA+C,CAAC;EACtE;EAEA,MAAMI,OAAO,GAAGN,OAAO,CAACM,OAAO,KAAKD,SAAS,GAAGR,OAAO,GAAGG,OAAO,CAACM,OAAO;EACzE,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIJ,SAAS,CAAC,+CAA+C,CAAC;EACtE;EAEA,IAAIF,OAAO,CAACO,MAAM,KAAKF,SAAS,IAAI,OAAOL,OAAO,CAACO,MAAM,KAAK,UAAU,EAAE;IACxE,MAAM,IAAIL,SAAS,CAAC,gDAAgD,CAAC;EACvE;EACA,MAAMK,MAAM,GAAGP,OAAO,CAACO,MAAM,IAAIC,YAAG,CAACD,MAAM;EAE3C,IAAIP,OAAO,CAACS,IAAI,KAAKJ,SAAS,IAAI,OAAOL,OAAO,CAACS,IAAI,KAAK,QAAQ,EAAE;IAClE,MAAM,IAAIP,SAAS,CAAC,4CAA4C,CAAC;EACnE;EACA,MAAMO,IAAI,GAAGT,OAAO,CAACS,IAAI,IAAId,uBAAuB;EAEpD,MAAMe,MAAM,GAAGV,OAAO,CAACU,MAAM;EAE7B,IAAIA,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,IAAIC,QAAQ;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIR,OAAO,EAAEQ,CAAC,EAAE,EAAE;IACjC,IAAI;MACFD,QAAQ,GAAG,MAAM,IAAAE,wBAAW,EAACX,OAAO,EAAE,MAAOM,MAAM,IAAK;QACtD,MAAMM,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACnC,OAAO,MAAM,IAAAC,mBAAW,EAACnB,OAAO,CAACC,MAAM,EAAEQ,IAAI,EAAEF,MAAM,EAAEG,MAAM,EAAEM,OAAO,CAAC;MACzE,CAAC,EAAEN,MAAM,CAAC;IACZ,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZ;MACA,IAAI,CAACV,MAAM,CAACC,OAAO,IAAIS,GAAG,YAAYC,KAAK,IAAID,GAAG,CAACE,IAAI,KAAK,cAAc,EAAE;QAC1E;MACF;MAEA,MAAMF,GAAG;IACX;EACF;EAEA,IAAI,CAACP,QAAQ,EAAE;IACb,MAAM,IAAIQ,KAAK,CAAC,oDAAoD,GAAGpB,MAAM,CAAC;EAChF;EAEA,MAAMsB,OAAO,GAAGV,QAAQ,CAACW,QAAQ,CAAC,OAAO,EAAE1B,qBAAqB,CAAC;EACjE,MAAM2B,SAAS,GAAGC,oBAAoB,CAACH,OAAO,EAAEpB,YAAY,CAAC;EAE7D,IAAI,CAACsB,SAAS,EAAE;IACd,MAAM,IAAIJ,KAAK,CAAC,WAAW,GAAGlB,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACC,MAAM,CAAC;EACjF;EAEA,OAAOwB,SAAS;AAClB;AAEO,SAASC,oBAAoBA,CAACb,QAAgB,EAAEV,YAAoB,EAAE;EAC3E,IAAIwB,OAAO;EAEX,MAAMC,SAAS,GAAGf,QAAQ,CAACgB,KAAK,CAAC,IAAI,CAAC;EACtC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEgB,GAAG,GAAGF,SAAS,CAACG,MAAM,EAAEjB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IACpD,MAAMkB,QAAQ,GAAGJ,SAAS,CAACd,CAAC,CAAC;IAC7B,MAAMmB,KAAK,GAAGD,QAAQ,CAACH,KAAK,CAAC,GAAG,CAAC;IAEjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAGF,KAAK,CAACF,MAAM,EAAEG,CAAC,GAAGC,QAAQ,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7D,MAAMZ,IAAI,GAAGW,KAAK,CAACC,CAAC,CAAC;MACrB,MAAME,KAAK,GAAGH,KAAK,CAACC,CAAC,GAAG,CAAC,CAAC;MAE1B,IAAIZ,IAAI,KAAK,KAAK,IAAIK,OAAO,EAAE;QAC7B,MAAMlB,IAAI,GAAG4B,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;QAChC,OAAO3B,IAAI;MACb;MAEA,IAAIa,IAAI,KAAK,cAAc,EAAE;QAC3B,IAAIc,KAAK,CAACE,WAAW,CAAC,CAAC,KAAKnC,YAAY,CAACmC,WAAW,CAAC,CAAC,EAAE;UACtDX,OAAO,GAAG,IAAI;QAChB,CAAC,MAAM;UACLA,OAAO,GAAG,KAAK;QACjB;MACF;IACF;EACF;AACF"}