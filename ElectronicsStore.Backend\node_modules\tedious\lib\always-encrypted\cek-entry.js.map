{"version": 3, "file": "cek-entry.js", "names": ["CEKEntry", "constructor", "ordinalVal", "ordinal", "databaseId", "cekId", "cekVersion", "cekMdVersion", "<PERSON><PERSON><PERSON>", "alloc", "columnEncryptionKeyValues", "add", "encrypted<PERSON>ey", "dbId", "keyId", "keyVersion", "mdVersion", "keyP<PERSON>", "keyStoreName", "algorithmName", "<PERSON><PERSON><PERSON>", "push", "length", "Error", "exports"], "sources": ["../../src/always-encrypted/cek-entry.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { type EncryptionKeyInfo } from './types';\n\nexport class CEKEntry {\n  declare columnEncryptionKeyValues: EncryptionKeyInfo[];\n  declare ordinal: number;\n  declare databaseId: number;\n  declare cekId: number;\n  declare cekVersion: number;\n  declare cekMdVersion: Buffer;\n\n  constructor(ordinalVal: number) {\n    this.ordinal = ordinalVal;\n    this.databaseId = 0;\n    this.cekId = 0;\n    this.cekVersion = 0;\n    this.cekMdVersion = Buffer.alloc(0);\n    this.columnEncryptionKeyValues = [];\n  }\n\n  add(encryptedKey: Buffer, dbId: number, keyId: number, keyVersion: number, mdVersion: Buffer, keyPath: string, keyStoreName: string, algorithmName: string): void {\n    const encryptionKey: EncryptionKeyInfo = {\n      encryptedKey,\n      dbId,\n      keyId,\n      keyVersion,\n      mdVersion,\n      keyPath,\n      keyStoreName,\n      algorithmName,\n    };\n\n    this.columnEncryptionKeyValues.push(encryptionKey);\n\n    if (this.databaseId === 0) {\n      this.databaseId = dbId;\n      this.cekId = keyId;\n      this.cekVersion = keyVersion;\n      this.cekMdVersion = mdVersion;\n    } else if ((this.databaseId !== dbId) || (this.cekId !== keyId) || (this.cekVersion !== keyVersion) || !this.cekMdVersion || !mdVersion || this.cekMdVersion.length !== mdVersion.length) {\n      throw new Error('Invalid databaseId, cekId, cekVersion or cekMdVersion.');\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;;AAIO,MAAMA,QAAQ,CAAC;EAQpBC,WAAWA,CAACC,UAAkB,EAAE;IAC9B,IAAI,CAACC,OAAO,GAAGD,UAAU;IACzB,IAAI,CAACE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAGC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACnC,IAAI,CAACC,yBAAyB,GAAG,EAAE;EACrC;EAEAC,GAAGA,CAACC,YAAoB,EAAEC,IAAY,EAAEC,KAAa,EAAEC,UAAkB,EAAEC,SAAiB,EAAEC,OAAe,EAAEC,YAAoB,EAAEC,aAAqB,EAAQ;IAChK,MAAMC,aAAgC,GAAG;MACvCR,YAAY;MACZC,IAAI;MACJC,KAAK;MACLC,UAAU;MACVC,SAAS;MACTC,OAAO;MACPC,YAAY;MACZC;IACF,CAAC;IAED,IAAI,CAACT,yBAAyB,CAACW,IAAI,CAACD,aAAa,CAAC;IAElD,IAAI,IAAI,CAAChB,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACA,UAAU,GAAGS,IAAI;MACtB,IAAI,CAACR,KAAK,GAAGS,KAAK;MAClB,IAAI,CAACR,UAAU,GAAGS,UAAU;MAC5B,IAAI,CAACR,YAAY,GAAGS,SAAS;IAC/B,CAAC,MAAM,IAAK,IAAI,CAACZ,UAAU,KAAKS,IAAI,IAAM,IAAI,CAACR,KAAK,KAAKS,KAAM,IAAK,IAAI,CAACR,UAAU,KAAKS,UAAW,IAAI,CAAC,IAAI,CAACR,YAAY,IAAI,CAACS,SAAS,IAAI,IAAI,CAACT,YAAY,CAACe,MAAM,KAAKN,SAAS,CAACM,MAAM,EAAE;MACxL,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;IAC3E;EACF;AACF;AAACC,OAAA,CAAAxB,QAAA,GAAAA,QAAA"}