{"version": 3, "file": "collation.js", "names": ["codepageByLanguageId", "exports", "codepageBySortId", "Flags", "IGNORE_CASE", "IGNORE_ACCENT", "IGNORE_KANA", "IGNORE_WIDTH", "BINARY", "BINARY2", "UTF8", "Collation", "fromBuffer", "buffer", "offset", "lcid", "flags", "version", "sortId", "constructor", "undefined", "codepage", "languageId", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "alloc"], "sources": ["../src/collation.ts"], "sourcesContent": ["type Encoding = 'utf-8' | 'CP437' | 'CP850' | 'CP874' | 'CP932' | 'CP936' | 'CP949' | 'CP950' | 'CP1250' | 'CP1251' | 'CP1252' | 'CP1253' | 'CP1254' | 'CP1255' | 'CP1256' | 'CP1257' | 'CP1258';\n\n// http://technet.microsoft.com/en-us/library/aa176553(v=sql.80).aspx\nexport const codepageByLanguageId: { [key: number]: Encoding } = {\n  // Arabic_*\n  [0x0401]: 'CP1256',\n\n  // Chinese_Taiwan_Stroke_*\n  // Chinese_Traditional_Stroke_Count_*\n  // Chinese_Taiwan_Bopomofo_*\n  // Chinese_Traditional_Bopomofo_*\n  [0x0404]: 'CP950',\n\n  // Czech_*\n  [0x0405]: 'CP1250',\n\n  // Danish_Greenlandic_*\n  // Danish_Norwegian_*\n  [0x0406]: 'CP1252',\n\n  // Greek_*\n  [0x0408]: 'CP1253',\n\n  // Latin1_General_*\n  [0x0409]: 'CP1252',\n\n  // Traditional_Spanish_*\n  [0x040A]: 'CP1252',\n\n  // Finnish_Swedish_*\n  [0x040B]: 'CP1252',\n\n  // French_*\n  [0x040C]: 'CP1252',\n\n  // Hebrew_*\n  [0x040D]: 'CP1255',\n\n  // Hungarian_*\n  // Hungarian_Technical_*\n  [0x040E]: 'CP1250',\n\n  // Icelandic_*\n  [0x040F]: 'CP1252',\n\n  // Japanese_*\n  // Japanese_XJIS_*\n  // Japanese_Unicode_*\n  // Japanese_Bushu_Kakusu_*\n  [0x0411]: 'CP932',\n\n  // Korean_*\n  // Korean_Wansung_*\n  [0x0412]: 'CP949',\n\n  // Norwegian_*\n  [0x0414]: 'CP1252',\n\n  // Polish_*\n  [0x0415]: 'CP1250',\n\n  // Romansh_*\n  [0x0417]: 'CP1252',\n\n  // Romanian_*\n  [0x0418]: 'CP1250',\n\n  // Cyrillic_*\n  [0x0419]: 'CP1251',\n\n  // Croatian_*\n  [0x041A]: 'CP1250',\n\n  // Slovak_*\n  [0x041B]: 'CP1250',\n\n  // Albanian_*\n  [0x041C]: 'CP1250',\n\n  // Thai_*\n  [0x041E]: 'CP874',\n\n  // Turkish_*\n  [0x041F]: 'CP1254',\n\n  // Urdu_*\n  [0x0420]: 'CP1256',\n\n  // Ukrainian_*\n  [0x0422]: 'CP1251',\n\n  // Slovenian_*\n  [0x0424]: 'CP1250',\n\n  // Estonian_*\n  [0x0425]: 'CP1257',\n\n  // Latvian_*\n  [0x0426]: 'CP1257',\n\n  // Lithuanian_*\n  [0x0427]: 'CP1257',\n\n  // Persian_*\n  [0x0429]: 'CP1256',\n\n  // Vietnamese_*\n  [0x042A]: 'CP1258',\n\n  // Azeri_Latin_*\n  [0x042C]: 'CP1254',\n\n  // Upper_Sorbian_*\n  [0x042E]: 'CP1252',\n\n  // Macedonian_FYROM_*\n  [0x042F]: 'CP1251',\n\n  // Sami_Norway_*\n  [0x043B]: 'CP1252',\n\n  // Kazakh_*\n  [0x043F]: 'CP1251',\n\n  // Turkmen_*\n  [0x0442]: 'CP1250',\n\n  // Uzbek_Latin_*\n  [0x0443]: 'CP1254',\n\n  // Tatar_*\n  [0x0444]: 'CP1251',\n\n  // Welsh_*\n  [0x0452]: 'CP1252',\n\n  // Frisian_*\n  [0x0462]: 'CP1252',\n\n  // Bashkir_*\n  [0x046D]: 'CP1251',\n\n  // Mapudungan_*\n  [0x047A]: 'CP1252',\n\n  // Mohawk_*\n  [0x047C]: 'CP1252',\n\n  // Breton_*\n  [0x047E]: 'CP1252',\n\n  // Uighur_*\n  [0x0480]: 'CP1256',\n\n  // Corsican_*\n  [0x0483]: 'CP1252',\n\n  // Yakut_*\n  [0x0485]: 'CP1251',\n\n  // Dari_*\n  [0x048C]: 'CP1256',\n\n  // Chinese_PRC_*\n  // Chinese_Simplified_Pinyin_*\n  // Chinese_PRC_Stroke_*\n  // Chinese_Simplified_Stroke_Order_*\n  [0x0804]: 'CP936',\n\n  // Serbian_Latin_*\n  [0x081A]: 'CP1250',\n\n  // Azeri_Cyrillic_*\n  [0x082C]: 'CP1251',\n\n  // Sami_Sweden_Finland_*\n  [0x083B]: 'CP1252',\n\n  // Tamazight_*\n  [0x085F]: 'CP1252',\n\n  // Chinese_Hong_Kong_Stroke_*\n  [0x0C04]: 'CP950',\n\n  // Modern_Spanish_*\n  [0x0C0A]: 'CP1252',\n\n  // Serbian_Cyrillic_*\n  [0x0C1A]: 'CP1251',\n\n  // Chinese_Traditional_Pinyin_*\n  // Chinese_Traditional_Stroke_Order_*\n  [0x1404]: 'CP950',\n\n  // Bosnian_Latin_*\n  [0x141A]: 'CP1250',\n\n  // Bosnian_Cyrillic_*\n  [0x201A]: 'CP1251',\n\n  // German\n  // German_PhoneBook_*\n  [0x0407]: 'CP1252',\n\n  // Georgian_Modern_Sort_*\n  [0x0437]: 'CP1252'\n};\n\nexport const codepageBySortId: { [key: number]: Encoding } = {\n  [30]: 'CP437', // SQL_Latin1_General_CP437_BIN\n  [31]: 'CP437', // SQL_Latin1_General_CP437_CS_AS\n  [32]: 'CP437', // SQL_Latin1_General_CP437_CI_AS\n  [33]: 'CP437', // SQL_Latin1_General_Pref_CP437_CI_AS\n  [34]: 'CP437', // SQL_Latin1_General_CP437_CI_AI\n  [40]: 'CP850', // SQL_Latin1_General_CP850_BIN\n  [41]: 'CP850', // SQL_Latin1_General_CP850_CS_AS\n  [42]: 'CP850', // SQL_Latin1_General_CP850_CI_AS\n  [43]: 'CP850', // SQL_Latin1_General_Pref_CP850_CI_AS\n  [44]: 'CP850', // SQL_Latin1_General_CP850_CI_AI\n  [49]: 'CP850', // SQL_1xCompat_CP850_CI_AS\n  [51]: 'CP1252', // SQL_Latin1_General_Cp1_CS_AS_KI_WI\n  [52]: 'CP1252', // SQL_Latin1_General_Cp1_CI_AS_KI_WI\n  [53]: 'CP1252', // SQL_Latin1_General_Pref_Cp1_CI_AS_KI_WI\n  [54]: 'CP1252', // SQL_Latin1_General_Cp1_CI_AI_KI_WI\n  [55]: 'CP850', // SQL_AltDiction_CP850_CS_AS\n  [56]: 'CP850', // SQL_AltDiction_Pref_CP850_CI_AS\n  [57]: 'CP850', // SQL_AltDiction_CP850_CI_AI\n  [58]: 'CP850', // SQL_Scandinavian_Pref_CP850_CI_AS\n  [59]: 'CP850', // SQL_Scandinavian_CP850_CS_AS\n  [60]: 'CP850', // SQL_Scandinavian_CP850_CI_AS\n  [61]: 'CP850', // SQL_AltDiction_CP850_CI_AS\n  [80]: 'CP1250', // SQL_Latin1_General_1250_BIN\n  [81]: 'CP1250', // SQL_Latin1_General_CP1250_CS_AS\n  [82]: 'CP1250', // SQL_Latin1_General_Cp1250_CI_AS_KI_WI\n  [83]: 'CP1250', // SQL_Czech_Cp1250_CS_AS_KI_WI\n  [84]: 'CP1250', // SQL_Czech_Cp1250_CI_AS_KI_WI\n  [85]: 'CP1250', // SQL_Hungarian_Cp1250_CS_AS_KI_WI\n  [86]: 'CP1250', // SQL_Hungarian_Cp1250_CI_AS_KI_WI\n  [87]: 'CP1250', // SQL_Polish_Cp1250_CS_AS_KI_WI\n  [88]: 'CP1250', // SQL_Polish_Cp1250_CI_AS_KI_WI\n  [89]: 'CP1250', // SQL_Romanian_Cp1250_CS_AS_KI_WI\n  [90]: 'CP1250', // SQL_Romanian_Cp1250_CI_AS_KI_WI\n  [91]: 'CP1250', // SQL_Croatian_Cp1250_CS_AS_KI_WI\n  [92]: 'CP1250', // SQL_Croatian_Cp1250_CI_AS_KI_WI\n  [93]: 'CP1250', // SQL_Slovak_Cp1250_CS_AS_KI_WI\n  [94]: 'CP1250', // SQL_Slovak_Cp1250_CI_AS_KI_WI\n  [95]: 'CP1250', // SQL_Slovenian_Cp1250_CS_AS_KI_WI\n  [96]: 'CP1250', // SQL_Slovenian_Cp1250_CI_AS_KI_WI\n  [104]: 'CP1251', // SQL_Latin1_General_1251_BIN\n  [105]: 'CP1251', // SQL_Latin1_General_CP1251_CS_AS\n  [106]: 'CP1251', // SQL_Latin1_General_CP1251_CI_AS\n  [107]: 'CP1251', // SQL_Ukrainian_Cp1251_CS_AS_KI_WI\n  [108]: 'CP1251', // SQL_Ukrainian_Cp1251_CI_AS_KI_WI\n  [112]: 'CP1253', // SQL_Latin1_General_1253_BIN\n  [113]: 'CP1253', // SQL_Latin1_General_CP1253_CS_AS\n  [114]: 'CP1253', // SQL_Latin1_General_CP1253_CI_AS\n  [120]: 'CP1253', // SQL_MixDiction_CP1253_CS_AS\n  [121]: 'CP1253', // SQL_AltDiction_CP1253_CS_AS\n  [122]: 'CP1253', // SQL_AltDiction2_CP1253_CS_AS\n  [124]: 'CP1253', // SQL_Latin1_General_CP1253_CI_AI\n  [128]: 'CP1254', // SQL_Latin1_General_1254_BIN\n  [129]: 'CP1254', // SQL_Latin1_General_Cp1254_CS_AS_KI_WI\n  [130]: 'CP1254', // SQL_Latin1_General_Cp1254_CI_AS_KI_WI\n  [136]: 'CP1255', // SQL_Latin1_General_1255_BIN\n  [137]: 'CP1255', // SQL_Latin1_General_CP1255_CS_AS\n  [138]: 'CP1255', // SQL_Latin1_General_CP1255_CI_AS\n  [144]: 'CP1256', // SQL_Latin1_General_1256_BIN\n  [145]: 'CP1256', // SQL_Latin1_General_CP1256_CS_AS\n  [146]: 'CP1256', // SQL_Latin1_General_CP1256_CI_AS\n  [152]: 'CP1257', // SQL_Latin1_General_1257_BIN\n  [153]: 'CP1257', // SQL_Latin1_General_CP1257_CS_AS\n  [154]: 'CP1257', // SQL_Latin1_General_CP1257_CI_AS\n  [155]: 'CP1257', // SQL_Estonian_Cp1257_CS_AS_KI_WI\n  [156]: 'CP1257', // SQL_Estonian_Cp1257_CI_AS_KI_WI\n  [157]: 'CP1257', // SQL_Latvian_Cp1257_CS_AS_KI_WI\n  [158]: 'CP1257', // SQL_Latvian_Cp1257_CI_AS_KI_WI\n  [159]: 'CP1257', // SQL_Lithuanian_Cp1257_CS_AS_KI_WI\n  [160]: 'CP1257', // SQL_Lithuanian_Cp1257_CI_AS_KI_WI\n  [183]: 'CP1252', // SQL_Danish_Pref_Cp1_CI_AS_KI_WI\n  [184]: 'CP1252', // SQL_SwedishPhone_Pref_Cp1_CI_AS_KI_WI\n  [185]: 'CP1252', // SQL_SwedishStd_Pref_Cp1_CI_AS_KI_WI\n  [186]: 'CP1252' // SQL_Icelandic_Pref_Cp1_CI_AS_KI_WI\n};\n\nexport const Flags = {\n  IGNORE_CASE: 1 << 0,\n  IGNORE_ACCENT: 1 << 1,\n  IGNORE_KANA: 1 << 2,\n  IGNORE_WIDTH: 1 << 3,\n  BINARY: 1 << 4,\n  BINARY2: 1 << 5,\n  UTF8: 1 << 6,\n};\n\nexport class Collation {\n  declare readonly lcid: number;\n  declare readonly flags: number;\n  declare readonly version: number;\n  declare readonly sortId: number;\n  declare readonly codepage: Encoding | undefined;\n\n  declare private buffer: Buffer | undefined;\n\n  static fromBuffer(buffer: Buffer, offset = 0) {\n    let lcid = (buffer[offset + 2] & 0x0F) << 16;\n    lcid |= buffer[offset + 1] << 8;\n    lcid |= buffer[offset + 0];\n\n    let flags = (buffer[offset + 3] & 0x0F) << 4;\n    flags |= (buffer[offset + 2] & 0xF0) >>> 4;\n\n    const version = (buffer[offset + 3] & 0xF0) >>> 4;\n\n    const sortId = buffer[offset + 4];\n\n    return new this(lcid, flags, version, sortId);\n  }\n\n  constructor(lcid: number, flags: number, version: number, sortId: number) {\n    this.buffer = undefined;\n\n    this.lcid = lcid;\n    this.flags = flags;\n    this.version = version;\n    this.sortId = sortId;\n\n    if (this.flags & Flags.UTF8) {\n      this.codepage = 'utf-8';\n    } else if (this.sortId) {\n      this.codepage = codepageBySortId[this.sortId];\n    } else {\n      // The last 16 bits of the LCID are the language id.\n      // The first 4 bits define additional sort orders.\n      const languageId = this.lcid & 0xFFFF;\n      this.codepage = codepageByLanguageId[languageId];\n    }\n  }\n\n  toBuffer(): Buffer {\n    if (this.buffer) {\n      return this.buffer;\n    }\n\n    this.buffer = Buffer.alloc(5);\n\n    this.buffer[0] = this.lcid & 0xFF;\n    this.buffer[1] = (this.lcid >>> 8) & 0xFF;\n    this.buffer[2] = ((this.lcid >>> 16) & 0x0F) | ((this.flags & 0x0F) << 4);\n    this.buffer[3] = ((this.flags & 0xF0) >>> 4) | ((this.version & 0x0F) << 4);\n    this.buffer[4] = this.sortId & 0xFF;\n\n    return this.buffer;\n  }\n}\n"], "mappings": ";;;;;;AAEA;AACO,MAAMA,oBAAiD,GAAG;EAC/D;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA;EACA;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA;EACA;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA;EACA;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA,CAAC,MAAM,GAAG,OAAO;EAEjB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA;EACA,CAAC,MAAM,GAAG,QAAQ;EAElB;EACA,CAAC,MAAM,GAAG;AACZ,CAAC;AAACC,OAAA,CAAAD,oBAAA,GAAAA,oBAAA;AAEK,MAAME,gBAA6C,GAAG;EAC3D,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,OAAO;EAAE;EACf,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,EAAE,GAAG,QAAQ;EAAE;EAChB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ;EAAE;EACjB,CAAC,GAAG,GAAG,QAAQ,CAAC;AAClB,CAAC;AAACD,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AAEK,MAAMC,KAAK,GAAG;EACnBC,WAAW,EAAE,CAAC,IAAI,CAAC;EACnBC,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,WAAW,EAAE,CAAC,IAAI,CAAC;EACnBC,YAAY,EAAE,CAAC,IAAI,CAAC;EACpBC,MAAM,EAAE,CAAC,IAAI,CAAC;EACdC,OAAO,EAAE,CAAC,IAAI,CAAC;EACfC,IAAI,EAAE,CAAC,IAAI;AACb,CAAC;AAACT,OAAA,CAAAE,KAAA,GAAAA,KAAA;AAEK,MAAMQ,SAAS,CAAC;EASrB,OAAOC,UAAUA,CAACC,MAAc,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC5C,IAAIC,IAAI,GAAG,CAACF,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IAC5CC,IAAI,IAAIF,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;IAC/BC,IAAI,IAAIF,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAE1B,IAAIE,KAAK,GAAG,CAACH,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAC5CE,KAAK,IAAI,CAACH,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC;IAE1C,MAAMG,OAAO,GAAG,CAACJ,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC;IAEjD,MAAMI,MAAM,GAAGL,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAEjC,OAAO,IAAI,IAAI,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,CAAC;EAC/C;EAEAC,WAAWA,CAACJ,IAAY,EAAEC,KAAa,EAAEC,OAAe,EAAEC,MAAc,EAAE;IACxE,IAAI,CAACL,MAAM,GAAGO,SAAS;IAEvB,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IAEpB,IAAI,IAAI,CAACF,KAAK,GAAGb,KAAK,CAACO,IAAI,EAAE;MAC3B,IAAI,CAACW,QAAQ,GAAG,OAAO;IACzB,CAAC,MAAM,IAAI,IAAI,CAACH,MAAM,EAAE;MACtB,IAAI,CAACG,QAAQ,GAAGnB,gBAAgB,CAAC,IAAI,CAACgB,MAAM,CAAC;IAC/C,CAAC,MAAM;MACL;MACA;MACA,MAAMI,UAAU,GAAG,IAAI,CAACP,IAAI,GAAG,MAAM;MACrC,IAAI,CAACM,QAAQ,GAAGrB,oBAAoB,CAACsB,UAAU,CAAC;IAClD;EACF;EAEAC,QAAQA,CAAA,EAAW;IACjB,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,OAAO,IAAI,CAACA,MAAM;IACpB;IAEA,IAAI,CAACA,MAAM,GAAGW,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAE7B,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACE,IAAI,GAAG,IAAI;IACjC,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACE,IAAI,KAAK,CAAC,GAAI,IAAI;IACzC,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC,GAAK,IAAI,CAACE,IAAI,KAAK,EAAE,GAAI,IAAI,GAAK,CAAC,IAAI,CAACC,KAAK,GAAG,IAAI,KAAK,CAAE;IACzE,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACG,KAAK,GAAG,IAAI,MAAM,CAAC,GAAK,CAAC,IAAI,CAACC,OAAO,GAAG,IAAI,KAAK,CAAE;IAC3E,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACK,MAAM,GAAG,IAAI;IAEnC,OAAO,IAAI,CAACL,MAAM;EACpB;AACF;AAACZ,OAAA,CAAAU,SAAA,GAAAA,SAAA"}