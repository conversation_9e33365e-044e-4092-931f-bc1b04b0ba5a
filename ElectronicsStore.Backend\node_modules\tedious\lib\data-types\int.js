"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _intn = _interopRequireDefault(require("./intn"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const NULL_LENGTH = Buffer.from([0x00]);
const DATA_LENGTH = Buffer.from([0x04]);
const Int = {
  id: 0x38,
  type: 'INT4',
  name: 'Int',
  declaration: function () {
    return 'int';
  },
  generateTypeInfo() {
    return Buffer.from([_intn.default.id, 0x04]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = Buffer.alloc(4);
    buffer.writeInt32LE(Number(parameter.value), 0);
    yield buffer;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'number') {
      value = Number(value);
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid number.');
    }
    if (value < -2147483648 || value > 2147483647) {
      throw new TypeError('Value must be between -2147483648 and 2147483647, inclusive.');
    }
    return value | 0;
  }
};
var _default = Int;
exports.default = _default;
module.exports = Int;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50biIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwib2JqIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJOVUxMX0xFTkdUSCIsIkJ1ZmZlciIsImZyb20iLCJEQVRBX0xFTkdUSCIsIkludCIsImlkIiwidHlwZSIsIm5hbWUiLCJkZWNsYXJhdGlvbiIsImdlbmVyYXRlVHlwZUluZm8iLCJJbnROIiwiZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgiLCJwYXJhbWV0ZXIiLCJvcHRpb25zIiwidmFsdWUiLCJnZW5lcmF0ZVBhcmFtZXRlckRhdGEiLCJidWZmZXIiLCJhbGxvYyIsIndyaXRlSW50MzJMRSIsIk51bWJlciIsInZhbGlkYXRlIiwiaXNOYU4iLCJUeXBlRXJyb3IiLCJfZGVmYXVsdCIsImV4cG9ydHMiLCJtb2R1bGUiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvZGF0YS10eXBlcy9pbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBEYXRhVHlwZSB9IGZyb20gJy4uL2RhdGEtdHlwZSc7XG5pbXBvcnQgSW50TiBmcm9tICcuL2ludG4nO1xuXG5jb25zdCBOVUxMX0xFTkdUSCA9IEJ1ZmZlci5mcm9tKFsweDAwXSk7XG5jb25zdCBEQVRBX0xFTkdUSCA9IEJ1ZmZlci5mcm9tKFsweDA0XSk7XG5cbmNvbnN0IEludDogRGF0YVR5cGUgPSB7XG4gIGlkOiAweDM4LFxuICB0eXBlOiAnSU5UNCcsXG4gIG5hbWU6ICdJbnQnLFxuXG4gIGRlY2xhcmF0aW9uOiBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gJ2ludCc7XG4gIH0sXG5cbiAgZ2VuZXJhdGVUeXBlSW5mbygpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oW0ludE4uaWQsIDB4MDRdKTtcbiAgfSxcblxuICBnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aChwYXJhbWV0ZXIsIG9wdGlvbnMpIHtcbiAgICBpZiAocGFyYW1ldGVyLnZhbHVlID09IG51bGwpIHtcbiAgICAgIHJldHVybiBOVUxMX0xFTkdUSDtcbiAgICB9XG5cbiAgICByZXR1cm4gREFUQV9MRU5HVEg7XG4gIH0sXG5cbiAgKiBnZW5lcmF0ZVBhcmFtZXRlckRhdGEocGFyYW1ldGVyLCBvcHRpb25zKSB7XG4gICAgaWYgKHBhcmFtZXRlci52YWx1ZSA9PSBudWxsKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDQpO1xuICAgIGJ1ZmZlci53cml0ZUludDMyTEUoTnVtYmVyKHBhcmFtZXRlci52YWx1ZSksIDApO1xuICAgIHlpZWxkIGJ1ZmZlcjtcbiAgfSxcblxuICB2YWxpZGF0ZTogZnVuY3Rpb24odmFsdWUpOiBudW1iZXIgfCBudWxsIHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ251bWJlcicpIHtcbiAgICAgIHZhbHVlID0gTnVtYmVyKHZhbHVlKTtcbiAgICB9XG5cbiAgICBpZiAoaXNOYU4odmFsdWUpKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdJbnZhbGlkIG51bWJlci4nKTtcbiAgICB9XG5cbiAgICBpZiAodmFsdWUgPCAtMjE0NzQ4MzY0OCB8fCB2YWx1ZSA+IDIxNDc0ODM2NDcpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ1ZhbHVlIG11c3QgYmUgYmV0d2VlbiAtMjE0NzQ4MzY0OCBhbmQgMjE0NzQ4MzY0NywgaW5jbHVzaXZlLicpO1xuICAgIH1cblxuICAgIHJldHVybiB2YWx1ZSB8IDA7XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IEludDtcbm1vZHVsZS5leHBvcnRzID0gSW50O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFDQSxJQUFBQSxLQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFBMEIsU0FBQUQsdUJBQUFFLEdBQUEsV0FBQUEsR0FBQSxJQUFBQSxHQUFBLENBQUFDLFVBQUEsR0FBQUQsR0FBQSxLQUFBRSxPQUFBLEVBQUFGLEdBQUE7QUFFMUIsTUFBTUcsV0FBVyxHQUFHQyxNQUFNLENBQUNDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQ3ZDLE1BQU1DLFdBQVcsR0FBR0YsTUFBTSxDQUFDQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUV2QyxNQUFNRSxHQUFhLEdBQUc7RUFDcEJDLEVBQUUsRUFBRSxJQUFJO0VBQ1JDLElBQUksRUFBRSxNQUFNO0VBQ1pDLElBQUksRUFBRSxLQUFLO0VBRVhDLFdBQVcsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDdEIsT0FBTyxLQUFLO0VBQ2QsQ0FBQztFQUVEQyxnQkFBZ0JBLENBQUEsRUFBRztJQUNqQixPQUFPUixNQUFNLENBQUNDLElBQUksQ0FBQyxDQUFDUSxhQUFJLENBQUNMLEVBQUUsRUFBRSxJQUFJLENBQUMsQ0FBQztFQUNyQyxDQUFDO0VBRURNLHVCQUF1QkEsQ0FBQ0MsU0FBUyxFQUFFQyxPQUFPLEVBQUU7SUFDMUMsSUFBSUQsU0FBUyxDQUFDRSxLQUFLLElBQUksSUFBSSxFQUFFO01BQzNCLE9BQU9kLFdBQVc7SUFDcEI7SUFFQSxPQUFPRyxXQUFXO0VBQ3BCLENBQUM7RUFFRCxDQUFFWSxxQkFBcUJBLENBQUNILFNBQVMsRUFBRUMsT0FBTyxFQUFFO0lBQzFDLElBQUlELFNBQVMsQ0FBQ0UsS0FBSyxJQUFJLElBQUksRUFBRTtNQUMzQjtJQUNGO0lBRUEsTUFBTUUsTUFBTSxHQUFHZixNQUFNLENBQUNnQixLQUFLLENBQUMsQ0FBQyxDQUFDO0lBQzlCRCxNQUFNLENBQUNFLFlBQVksQ0FBQ0MsTUFBTSxDQUFDUCxTQUFTLENBQUNFLEtBQUssQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUMvQyxNQUFNRSxNQUFNO0VBQ2QsQ0FBQztFQUVESSxRQUFRLEVBQUUsU0FBQUEsQ0FBU04sS0FBSyxFQUFpQjtJQUN2QyxJQUFJQSxLQUFLLElBQUksSUFBSSxFQUFFO01BQ2pCLE9BQU8sSUFBSTtJQUNiO0lBRUEsSUFBSSxPQUFPQSxLQUFLLEtBQUssUUFBUSxFQUFFO01BQzdCQSxLQUFLLEdBQUdLLE1BQU0sQ0FBQ0wsS0FBSyxDQUFDO0lBQ3ZCO0lBRUEsSUFBSU8sS0FBSyxDQUFDUCxLQUFLLENBQUMsRUFBRTtNQUNoQixNQUFNLElBQUlRLFNBQVMsQ0FBQyxpQkFBaUIsQ0FBQztJQUN4QztJQUVBLElBQUlSLEtBQUssR0FBRyxDQUFDLFVBQVUsSUFBSUEsS0FBSyxHQUFHLFVBQVUsRUFBRTtNQUM3QyxNQUFNLElBQUlRLFNBQVMsQ0FBQyw4REFBOEQsQ0FBQztJQUNyRjtJQUVBLE9BQU9SLEtBQUssR0FBRyxDQUFDO0VBQ2xCO0FBQ0YsQ0FBQztBQUFDLElBQUFTLFFBQUEsR0FFYW5CLEdBQUc7QUFBQW9CLE9BQUEsQ0FBQXpCLE9BQUEsR0FBQXdCLFFBQUE7QUFDbEJFLE1BQU0sQ0FBQ0QsT0FBTyxHQUFHcEIsR0FBRyJ9