"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createNTLMRequest = createNTLMRequest;
const NTLMFlags = {
  NTLM_NegotiateUnicode: 0x00000001,
  NTLM_NegotiateOEM: 0x00000002,
  NTLM_RequestTarget: 0x00000004,
  NTLM_Unknown9: 0x00000008,
  NTLM_NegotiateSign: 0x00000010,
  NTLM_NegotiateSeal: 0x00000020,
  NTLM_NegotiateDatagram: 0x00000040,
  NTLM_NegotiateLanManagerKey: 0x00000080,
  NTLM_Unknown8: 0x00000100,
  NTLM_NegotiateNTLM: 0x00000200,
  NTLM_NegotiateNTOnly: 0x00000400,
  NTLM_Anonymous: 0x00000800,
  NTLM_NegotiateOemDomainSupplied: 0x00001000,
  NTLM_NegotiateOemWorkstationSupplied: 0x00002000,
  NTLM_Unknown6: 0x00004000,
  NTLM_NegotiateAlwaysSign: 0x00008000,
  NTLM_TargetTypeDomain: 0x00010000,
  NTLM_TargetTypeServer: 0x00020000,
  NTLM_TargetTypeShare: 0x00040000,
  NTLM_NegotiateExtendedSecurity: 0x00080000,
  NTLM_NegotiateIdentify: 0x00100000,
  NTLM_Unknown5: 0x00200000,
  NTLM_RequestNonNTSessionKey: 0x00400000,
  NTLM_NegotiateTargetInfo: 0x00800000,
  NTLM_Unknown4: 0x01000000,
  NTLM_NegotiateVersion: 0x02000000,
  NTLM_Unknown3: 0x04000000,
  NTLM_Unknown2: 0x08000000,
  NTLM_Unknown1: 0x10000000,
  NTLM_Negotiate128: 0x20000000,
  NTLM_NegotiateKeyExchange: 0x40000000,
  NTLM_Negotiate56: 0x80000000
};
function createNTLMRequest(options) {
  const domain = escape(options.domain.toUpperCase());
  const workstation = options.workstation ? escape(options.workstation.toUpperCase()) : '';
  let type1flags = NTLMFlags.NTLM_NegotiateUnicode + NTLMFlags.NTLM_NegotiateOEM + NTLMFlags.NTLM_RequestTarget + NTLMFlags.NTLM_NegotiateNTLM + NTLMFlags.NTLM_NegotiateOemDomainSupplied + NTLMFlags.NTLM_NegotiateOemWorkstationSupplied + NTLMFlags.NTLM_NegotiateAlwaysSign + NTLMFlags.NTLM_NegotiateVersion + NTLMFlags.NTLM_NegotiateExtendedSecurity + NTLMFlags.NTLM_Negotiate128 + NTLMFlags.NTLM_Negotiate56;
  if (workstation === '') {
    type1flags -= NTLMFlags.NTLM_NegotiateOemWorkstationSupplied;
  }
  const fixedData = Buffer.alloc(40);
  const buffers = [fixedData];
  let offset = 0;
  offset += fixedData.write('NTLMSSP', offset, 7, 'ascii');
  offset = fixedData.writeUInt8(0, offset);
  offset = fixedData.writeUInt32LE(1, offset);
  offset = fixedData.writeUInt32LE(type1flags, offset);
  offset = fixedData.writeUInt16LE(domain.length, offset);
  offset = fixedData.writeUInt16LE(domain.length, offset);
  offset = fixedData.writeUInt32LE(fixedData.length + workstation.length, offset);
  offset = fixedData.writeUInt16LE(workstation.length, offset);
  offset = fixedData.writeUInt16LE(workstation.length, offset);
  offset = fixedData.writeUInt32LE(fixedData.length, offset);
  offset = fixedData.writeUInt8(5, offset);
  offset = fixedData.writeUInt8(0, offset);
  offset = fixedData.writeUInt16LE(2195, offset);
  offset = fixedData.writeUInt8(0, offset);
  offset = fixedData.writeUInt8(0, offset);
  offset = fixedData.writeUInt8(0, offset);
  fixedData.writeUInt8(15, offset);
  buffers.push(Buffer.from(workstation, 'ascii'));
  buffers.push(Buffer.from(domain, 'ascii'));
  return Buffer.concat(buffers);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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