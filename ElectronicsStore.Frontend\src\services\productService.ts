import { apiService, ApiResponse, PaginatedResponse } from './apiClient';
import { Product, ProductFormData } from '../types';

// Product API endpoints
const ENDPOINTS = {
  PRODUCTS: '/products',
  PRODUCT_BY_ID: (id: number) => `/products/${id}`,
  PRODUCT_BY_BARCODE: (barcode: string) => `/products/barcode/${barcode}`,
  PRODUCTS_SEARCH: '/products/search',
  PRODUCTS_LOW_STOCK: '/products/low-stock',
  PRODUCTS_STATISTICS: '/products/statistics',
};

// Product Service
export const productService = {
  // Get all products with pagination and filters
  getProducts: async (params?: {
    pageNumber?: number;
    pageSize?: number;
    categoryId?: number;
    search?: string;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<Product>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.pageNumber) queryParams.append('pageNumber', params.pageNumber.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params?.categoryId) queryParams.append('categoryId', params.categoryId.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortDirection) queryParams.append('sortDirection', params.sortDirection);

    const url = `${ENDPOINTS.PRODUCTS}?${queryParams.toString()}`;
    return await apiService.get<PaginatedResponse<Product>>(url);
  },

  // Get product by ID
  getProductById: async (id: number): Promise<Product> => {
    return await apiService.get<Product>(ENDPOINTS.PRODUCT_BY_ID(id));
  },

  // Get product by barcode
  getProductByBarcode: async (barcode: string): Promise<Product> => {
    return await apiService.get<Product>(ENDPOINTS.PRODUCT_BY_BARCODE(barcode));
  },

  // Create new product
  createProduct: async (productData: ProductFormData): Promise<Product> => {
    return await apiService.post<Product>(ENDPOINTS.PRODUCTS, productData);
  },

  // Update existing product
  updateProduct: async (id: number, productData: ProductFormData): Promise<Product> => {
    return await apiService.put<Product>(ENDPOINTS.PRODUCT_BY_ID(id), productData);
  },

  // Delete product
  deleteProduct: async (id: number): Promise<void> => {
    return await apiService.delete<void>(ENDPOINTS.PRODUCT_BY_ID(id));
  },

  // Search products
  searchProducts: async (query: string, limit?: number): Promise<Product[]> => {
    const params = new URLSearchParams();
    params.append('query', query);
    if (limit) params.append('limit', limit.toString());

    const url = `${ENDPOINTS.PRODUCTS_SEARCH}?${params.toString()}`;
    return await apiService.get<Product[]>(url);
  },

  // Get low stock products
  getLowStockProducts: async (threshold?: number): Promise<Product[]> => {
    const params = new URLSearchParams();
    if (threshold) params.append('threshold', threshold.toString());

    const url = `${ENDPOINTS.PRODUCTS_LOW_STOCK}?${params.toString()}`;
    return await apiService.get<Product[]>(url);
  },

  // Get product statistics
  getProductStatistics: async (): Promise<{
    totalProducts: number;
    totalValue: number;
    lowStockCount: number;
    outOfStockCount: number;
    averagePrice: number;
    topSellingProducts: Product[];
  }> => {
    return await apiService.get(ENDPOINTS.PRODUCTS_STATISTICS);
  },

  // Upload product image
  uploadProductImage: async (productId: number, imageFile: File): Promise<string> => {
    const formData = new FormData();
    formData.append('image', imageFile);

    return await apiService.post<string>(
      `/products/${productId}/image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },

  // Bulk operations
  bulkUpdateProducts: async (updates: Array<{ id: number; data: Partial<ProductFormData> }>): Promise<void> => {
    return await apiService.post<void>('/products/bulk-update', updates);
  },

  bulkDeleteProducts: async (productIds: number[]): Promise<void> => {
    return await apiService.post<void>('/products/bulk-delete', { productIds });
  },

  // Import/Export
  exportProducts: async (format: 'csv' | 'excel' = 'csv'): Promise<Blob> => {
    const response = await apiService.get<Blob>(`/products/export?format=${format}`, {
      responseType: 'blob',
    });
    return response;
  },

  importProducts: async (file: File): Promise<{
    successCount: number;
    errorCount: number;
    errors: string[];
  }> => {
    const formData = new FormData();
    formData.append('file', file);

    return await apiService.post('/products/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export default productService;
