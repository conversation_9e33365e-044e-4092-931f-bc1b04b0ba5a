import React from 'react';
import {
  Box,
  Typography,
  Paper,
  useTheme,
  alpha,
} from '@mui/material';

interface DashboardSectionProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  elevation?: number;
  sx?: any;
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  title,
  subtitle,
  children,
  elevation = 1,
  sx = {},
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={elevation}
      sx={{
        p: 3,
        borderRadius: 2,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.02)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          elevation: elevation + 1,
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[elevation + 2],
        },
        ...sx,
      }}
    >
      {/* Section Header */}
      <Box mb={2}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            mb: subtitle ? 0.5 : 0,
          }}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: '0.9rem' }}
          >
            {subtitle}
          </Typography>
        )}
      </Box>

      {/* Section Content */}
      <Box>
        {children}
      </Box>
    </Paper>
  );
};

export default DashboardSection;
