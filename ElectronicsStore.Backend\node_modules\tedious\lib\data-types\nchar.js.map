{"version": 3, "file": "nchar.js", "names": ["NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NChar", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "toString", "output", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "byteLength", "generateParameterData", "validate", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/nchar.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\n\nconst NChar: DataType & { maximumLength: number } = {\n  id: 0xEF,\n  type: 'NCHAR',\n  name: '<PERSON>har',\n  maximumLength: 4000,\n\n  declaration: function(parameter) {\n    // const value = parameter.value as null | string | { toString(): string };\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (parameter.value != null) {\n      length = value.toString().length || 1;\n    } else if (parameter.value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    if (length < this.maximumLength) {\n      return 'nchar(' + length + ')';\n    } else {\n      return 'nchar(' + this.maximumLength + ')';\n    }\n  },\n\n  resolveLength: function(parameter) {\n    // const value = parameter.value as null | string | { toString(): string };\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    if (parameter.length != null) {\n      return parameter.length;\n    } else if (parameter.value != null) {\n      if (Buffer.isBuffer(parameter.value)) {\n        return (parameter.value.length / 2) || 1;\n      } else {\n        return value.toString().length || 1;\n      }\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo: function(parameter) {\n    const buffer = Buffer.alloc(8);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeUInt16LE(parameter.length! * 2, 1);\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const { value } = parameter;\n    if (value instanceof Buffer) {\n      const length = value.length;\n      const buffer = Buffer.alloc(2);\n\n      buffer.writeUInt16LE(length, 0);\n\n      return buffer;\n    } else {\n      const length = Buffer.byteLength(value.toString(), 'ucs2');\n\n      const buffer = Buffer.alloc(2);\n      buffer.writeUInt16LE(length, 0);\n      return buffer;\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value;\n    if (value instanceof Buffer) {\n      yield value;\n    } else {\n      yield Buffer.from(value, 'ucs2');\n    }\n  },\n\n  validate: function(value): string | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    return value;\n  }\n};\n\nexport default NChar;\nmodule.exports = NChar;\n"], "mappings": ";;;;;;AAEA,MAAMA,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE7C,MAAMC,KAA2C,GAAG;EAClDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B;IACA,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;;IAEtC,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAIF,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAClCC,MAAM,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACD,MAAM,IAAI,CAAC;IACvC,CAAC,MAAM,IAAIF,SAAS,CAACC,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACI,MAAM,EAAE;MACxDF,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,IAAII,MAAM,GAAG,IAAI,CAACJ,aAAa,EAAE;MAC/B,OAAO,QAAQ,GAAGI,MAAM,GAAG,GAAG;IAChC,CAAC,MAAM;MACL,OAAO,QAAQ,GAAG,IAAI,CAACJ,aAAa,GAAG,GAAG;IAC5C;EACF,CAAC;EAEDO,aAAa,EAAE,SAAAA,CAASL,SAAS,EAAE;IACjC;IACA,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;;IAEtC,IAAID,SAAS,CAACE,MAAM,IAAI,IAAI,EAAE;MAC5B,OAAOF,SAAS,CAACE,MAAM;IACzB,CAAC,MAAM,IAAIF,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAClC,IAAIT,MAAM,CAACc,QAAQ,CAACN,SAAS,CAACC,KAAK,CAAC,EAAE;QACpC,OAAQD,SAAS,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,IAAK,CAAC;MAC1C,CAAC,MAAM;QACL,OAAOD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACD,MAAM,IAAI,CAAC;MACrC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDS,gBAAgB,EAAE,SAAAA,CAASP,SAAS,EAAE;IACpC,MAAMQ,MAAM,GAAGhB,MAAM,CAACiB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACf,EAAE,EAAE,CAAC,CAAC;IAC7Ba,MAAM,CAACG,aAAa,CAACX,SAAS,CAACE,MAAM,GAAI,CAAC,EAAE,CAAC,CAAC;IAE9C,IAAIF,SAAS,CAACY,SAAS,EAAE;MACvBZ,SAAS,CAACY,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACf,SAAS,EAAEgB,OAAO,EAAE;IAC1C,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOV,WAAW;IACpB;IAEA,MAAM;MAAEU;IAAM,CAAC,GAAGD,SAAS;IAC3B,IAAIC,KAAK,YAAYT,MAAM,EAAE;MAC3B,MAAMU,MAAM,GAAGD,KAAK,CAACC,MAAM;MAC3B,MAAMM,MAAM,GAAGhB,MAAM,CAACiB,KAAK,CAAC,CAAC,CAAC;MAE9BD,MAAM,CAACG,aAAa,CAACT,MAAM,EAAE,CAAC,CAAC;MAE/B,OAAOM,MAAM;IACf,CAAC,MAAM;MACL,MAAMN,MAAM,GAAGV,MAAM,CAACyB,UAAU,CAAChB,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;MAE1D,MAAMK,MAAM,GAAGhB,MAAM,CAACiB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACG,aAAa,CAACT,MAAM,EAAE,CAAC,CAAC;MAC/B,OAAOM,MAAM;IACf;EACF,CAAC;EAED,CAAEU,qBAAqBA,CAAClB,SAAS,EAAEgB,OAAO,EAAE;IAC1C,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGD,SAAS,CAACC,KAAK;IAC7B,IAAIA,KAAK,YAAYT,MAAM,EAAE;MAC3B,MAAMS,KAAK;IACb,CAAC,MAAM;MACL,MAAMT,MAAM,CAACC,IAAI,CAACQ,KAAK,EAAE,MAAM,CAAC;IAClC;EACF,CAAC;EAEDkB,QAAQ,EAAE,SAAAA,CAASlB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAImB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,OAAOnB,KAAK;EACd;AACF,CAAC;AAAC,IAAAoB,QAAA,GAEa3B,KAAK;AAAA4B,OAAA,CAAAC,OAAA,GAAAF,QAAA;AACpBG,MAAM,CAACF,OAAO,GAAG5B,KAAK"}