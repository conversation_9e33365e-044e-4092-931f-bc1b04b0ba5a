"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var _helpers = require("./helpers");
// s2.2.7.14

function orderParser(buf, offset, _options) {
  // length
  let tokenLength;
  ({
    offset,
    value: tokenLength
  } = (0, _helpers.readUInt16LE)(buf, offset));
  if (buf.length < offset + tokenLength) {
    throw new _helpers.NotEnoughDataError(offset + tokenLength);
  }
  const orderColumns = [];
  for (let i = 0; i < tokenLength; i += 2) {
    let column;
    ({
      offset,
      value: column
    } = (0, _helpers.readUInt16LE)(buf, offset));
    orderColumns.push(column);
  }
  return new _helpers.Result(new _token.OrderToken(orderColumns), offset);
}
var _default = orderParser;
exports.default = _default;
module.exports = orderParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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