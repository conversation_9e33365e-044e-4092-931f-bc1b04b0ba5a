import type { PipelinePolicy } from "@azure/core-rest-pipeline";
import type { ClientOptions } from "./common.js";
export declare const apiVersionPolicyName = "ApiVersionPolicy";
/**
 * Creates a policy that sets the apiVersion as a query parameter on every request
 * @param options - Client options
 * @returns Pipeline policy that sets the apiVersion as a query parameter on every request
 */
export declare function apiVersionPolicy(options: ClientOptions): PipelinePolicy;
//# sourceMappingURL=apiVersionPolicy.d.ts.map